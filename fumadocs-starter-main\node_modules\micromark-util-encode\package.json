{"name": "micromark-util-encode", "version": "2.0.1", "description": "micromark utility to encode dangerous html characters", "license": "MIT", "keywords": ["micromark", "util", "utility", "html", "encode"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-encode", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["index.d.ts.map", "index.d.ts", "index.js"], "exports": "./index.js", "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"unicorn/prefer-string-replace-all": "off", "unicorn/prefer-code-point": "off"}}}