import {isAlphabetical} from 'is-alphabetical'
import {isDecimal} from 'is-decimal'

/**
 * Check if the given character code, or the character code at the first
 * character, is alphanumerical.
 *
 * @param {string|number} character
 * @returns {boolean} Whether `character` is alphanumerical.
 */
export function isAlphanumerical(character) {
  return isAlphabetical(character) || isDecimal(character)
}
