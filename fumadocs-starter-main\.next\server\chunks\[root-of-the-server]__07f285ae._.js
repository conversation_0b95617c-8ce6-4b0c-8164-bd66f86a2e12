module.exports = [
"[externals]/node:fs/promises [external] (node:fs/promises, cjs, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_node:fs_promises_ef82023e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[externals]/node:fs/promises [external] (node:fs/promises, cjs)");
    });
});
}),
"[project]/node_modules/fumadocs-mdx/dist/mdx-options-3NB74EMB.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_fumadocs-mdx_dist_mdx-options-3NB74EMB_60dac223.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-mdx/dist/mdx-options-3NB74EMB.js [app-route] (ecmascript)");
    });
});
}),
"[externals]/shiki [external] (shiki, esm_import, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[externals]/shiki [external] (shiki, esm_import)");
    });
});
}),
"[externals]/shiki/engine/oniguruma [external] (shiki/engine/oniguruma, esm_import, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_shiki_engine_oniguruma_f5b30f3f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[externals]/shiki/engine/oniguruma [external] (shiki/engine/oniguruma, esm_import)");
    });
});
}),
"[externals]/shiki/wasm [external] (shiki/wasm, esm_import, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_shiki_wasm_ac43d9bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[externals]/shiki/wasm [external] (shiki/wasm, esm_import)");
    });
});
}),
"[externals]/shiki/engine/javascript [external] (shiki/engine/javascript, esm_import, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_shiki_engine_javascript_1fc3cfdb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[externals]/shiki/engine/javascript [external] (shiki/engine/javascript, esm_import)");
    });
});
}),
];