{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40shikijs/langs/dist/asciidoc.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"AsciiDoc\\\",\\\"fileTypes\\\":[\\\"ad\\\",\\\"asc\\\",\\\"adoc\\\",\\\"asciidoc\\\",\\\"adoc.txt\\\"],\\\"name\\\":\\\"asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#callout-list-item\\\"},{\\\"include\\\":\\\"#titles\\\"},{\\\"include\\\":\\\"#attribute-entry\\\"},{\\\"include\\\":\\\"#blocks\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"include\\\":\\\"#tables\\\"},{\\\"include\\\":\\\"#horizontal-rule\\\"},{\\\"include\\\":\\\"#list\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-attribute\\\"},{\\\"include\\\":\\\"#line-break\\\"}],\\\"repository\\\":{\\\"admonition-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(NOTE|TIP|IMPORTANT|WARNING|CAUTION)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|====)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.admonition.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(NOTE|TIP|IMPORTANT|WARNING|CAUTION)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(={4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]}]},{\\\"begin\\\":\\\"^(NOTE|TIP|IMPORTANT|WARNING|CAUTION):\\\\\\\\p{blank}+\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"}},\\\"end\\\":\\\"^\\\\\\\\p{blank}*$\\\",\\\"name\\\":\\\"markup.admonition.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"}]}]},\\\"anchor-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.blockid.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[{2})([:_[:alpha:]][-.:[:word:]]*)(?:,\\\\\\\\p{blank}*(\\\\\\\\S.*?))?(]{2})\\\",\\\"name\\\":\\\"markup.other.anchor.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.blockid.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(anchor):(\\\\\\\\S+)\\\\\\\\[(.*?[^\\\\\\\\\\\\\\\\])?]\\\",\\\"name\\\":\\\"markup.other.anchor.asciidoc\\\"}]},\\\"attribute-entry\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(:)(!?\\\\\\\\w.*?)(:)(\\\\\\\\p{blank}+.+\\\\\\\\p{blank}[+\\\\\\\\\\\\\\\\])$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.attribute-entry.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.constant.attribute-name.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.attribute-entry.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#hard-break-backslash\\\"},{\\\"include\\\":\\\"#line-break\\\"},{\\\"include\\\":\\\"#line-break-backslash\\\"}]}},\\\"contentName\\\":\\\"string.unquoted.attribute-value.asciidoc\\\",\\\"end\\\":\\\"^(?:\\\\\\\\p{blank}+.+$(?<![+\\\\\\\\\\\\\\\\])|\\\\\\\\p{blank}*$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.asciidoc\\\"}},\\\"name\\\":\\\"meta.definition.attribute-entry.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#hard-break-backslash\\\"},{\\\"include\\\":\\\"#line-break\\\"},{\\\"include\\\":\\\"#line-break-backslash\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.constant.attribute-name.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#line-break\\\"}]}},\\\"match\\\":\\\"^(:)(!?\\\\\\\\w.*?)(:)(\\\\\\\\p{blank}+(.*))?$\\\",\\\"name\\\":\\\"meta.definition.attribute-entry.asciidoc\\\"}]},\\\"attribute-reference\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.constant.attribute-name.asciidoc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"7\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\{)(set|counter2?)(:)([-!_[:alnum:]]+)((:)(.*?))?(?<!\\\\\\\\\\\\\\\\)(})\\\",\\\"name\\\":\\\"markup.substitution.attribute-reference.asciidoc\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\{)(\\\\\\\\w+(?:-\\\\\\\\w+)*)(?<!\\\\\\\\\\\\\\\\)(})\\\",\\\"name\\\":\\\"markup.substitution.attribute-reference.asciidoc\\\"}]},\\\"bibliography-anchor\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.biblioref.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[{3})([:[:word:]][-.:[:word:]]*?)(]{3})\\\",\\\"name\\\":\\\"bibliography-anchor.asciidoc\\\"}]},\\\"bibtex-macro\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(citenp:)([,a-z]*)(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"]|^$\\\",\\\"name\\\":\\\"markup.macro.inline.bibtex.asciidoc\\\"}]},\\\"block-attribute\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(|\\\\\\\\p{blank}*[\\\\\\\"#%',.{[:word:]].*)]$\\\",\\\"name\\\":\\\"markup.heading.block-attribute.asciidoc\\\"}]},\\\"block-attribute-inner\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"([#%,.])\\\",\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"}]}},\\\"match\\\":\\\"(?<=\\\\\\\\[)([^]#%,.=\\\\\\\\[]+)\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]}},\\\"match\\\":\\\"(?<=[,{]|.|[\\\\\\\"#%'])([^]#%,.]+)\\\",\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"}]},\\\"block-callout\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"}},\\\"match\\\":\\\"(?:(?://|#|--|;;) ?)?( )?(?<!\\\\\\\\\\\\\\\\)(<)!?(--|)(\\\\\\\\d+)\\\\\\\\3(>)(?=(?: ?<!?\\\\\\\\3\\\\\\\\d+\\\\\\\\3>)*$)\\\",\\\"name\\\":\\\"callout.source.code.asciidoc\\\"}]},\\\"block-title\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\.([^.[:blank:]].*)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.blocktitle.asciidoc\\\"}},\\\"end\\\":\\\"$\\\"}]},\\\"blocks\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#front-matter-block\\\"},{\\\"include\\\":\\\"#comment-paragraph\\\"},{\\\"include\\\":\\\"#admonition-paragraph\\\"},{\\\"include\\\":\\\"#quote-paragraph\\\"},{\\\"include\\\":\\\"#listing-paragraph\\\"},{\\\"include\\\":\\\"#source-paragraphs\\\"},{\\\"include\\\":\\\"#passthrough-paragraph\\\"},{\\\"include\\\":\\\"#example-paragraph\\\"},{\\\"include\\\":\\\"#sidebar-paragraph\\\"},{\\\"include\\\":\\\"#literal-paragraph\\\"},{\\\"include\\\":\\\"#open-block\\\"}]},\\\"callout-list-item\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"}]}},\\\"match\\\":\\\"^(<)(\\\\\\\\d+)(>)\\\\\\\\p{blank}+(.*)$\\\",\\\"name\\\":\\\"callout.asciidoc\\\"}]},\\\"characters\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(&)(\\\\\\\\S+?)(;)\\\",\\\"name\\\":\\\"markup.character-reference.asciidoc\\\"}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(/{4,})$\\\",\\\"end\\\":\\\"^\\\\\\\\1$\\\",\\\"name\\\":\\\"comment.block.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"}]},{\\\"match\\\":\\\"^/{2}([^/].*)?$\\\",\\\"name\\\":\\\"comment.inline.asciidoc\\\"}]},\\\"comment-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(comment)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"comment.block.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(comment)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]},{\\\"include\\\":\\\"#inlines\\\"}]}]},\\\"emphasis\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)(\\\\\\\\[[^]]+?])?((__)((?!_).+?)(__))\\\",\\\"name\\\":\\\"markup.emphasis.unconstrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?!_{4,}\\\\\\\\s*$)(?<=^|[^:;[:word:]])(\\\\\\\\[[^]]+?])?((_)(\\\\\\\\S(?:|.*?\\\\\\\\S))(_))(?!\\\\\\\\p{word})\\\",\\\"name\\\":\\\"markup.emphasis.constrained.asciidoc\\\"}]},\\\"example-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(example)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|====)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.block.example.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(example)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(={4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(-{2})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#inlines\\\"}]},{\\\"begin\\\":\\\"^(={4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"name\\\":\\\"markup.block.example.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"footnote-macro\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)footnote(?:(ref):|:([-\\\\\\\\w]+)?)\\\\\\\\[(.*?[^\\\\\\\\\\\\\\\\])??\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.constant.attribute-name.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"]|^$\\\",\\\"name\\\":\\\"markup.other.footnote.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"}]}]},\\\"front-matter-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\A(-{3})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"name\\\":\\\"markup.block.front-matter.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}]}]},\\\"general-block-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"}},\\\"match\\\":\\\"^(\\\\\\\\p{word}+)(::)(\\\\\\\\S*?)(\\\\\\\\[)((?:\\\\\\\\\\\\\\\\]|[^]])*?)(])$\\\",\\\"name\\\":\\\"markup.macro.block.general.asciidoc\\\"}]},\\\"hard-break-backslash\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.symbol.hard-break.asciidoc\\\"}},\\\"match\\\":\\\"(?<=\\\\\\\\S)\\\\\\\\p{blank}+(\\\\\\\\+ \\\\\\\\\\\\\\\\)$\\\"}]},\\\"horizontal-rule\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^(?:['<]{3,}| {0,3}([-'*])( *)\\\\\\\\1\\\\\\\\2\\\\\\\\1)$\\\",\\\"name\\\":\\\"constant.other.symbol.horizontal-rule.asciidoc\\\"}]},\\\"image-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(i(?:mage|con)):([^:\\\\\\\\[][^\\\\\\\\[]*)\\\\\\\\[((?:\\\\\\\\\\\\\\\\]|[^]])*?)]\\\",\\\"name\\\":\\\"markup.macro.image.asciidoc\\\"}]},\\\"include-directive\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.asciidoc\\\"}},\\\"match\\\":\\\"^(include)(::)([^\\\\\\\\[]+)(\\\\\\\\[)(.*?)(])$\\\"}]},\\\"inlines\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#typographic-quotes\\\"},{\\\"include\\\":\\\"#strong\\\"},{\\\"include\\\":\\\"#monospace\\\"},{\\\"include\\\":\\\"#emphasis\\\"},{\\\"include\\\":\\\"#superscript\\\"},{\\\"include\\\":\\\"#subscript\\\"},{\\\"include\\\":\\\"#mark\\\"},{\\\"include\\\":\\\"#general-block-macro\\\"},{\\\"include\\\":\\\"#anchor-macro\\\"},{\\\"include\\\":\\\"#footnote-macro\\\"},{\\\"include\\\":\\\"#image-macro\\\"},{\\\"include\\\":\\\"#kbd-macro\\\"},{\\\"include\\\":\\\"#link-macro\\\"},{\\\"include\\\":\\\"#stem-macro\\\"},{\\\"include\\\":\\\"#menu-macro\\\"},{\\\"include\\\":\\\"#passthrough-macro\\\"},{\\\"include\\\":\\\"#xref-macro\\\"},{\\\"include\\\":\\\"#attribute-reference\\\"},{\\\"include\\\":\\\"#characters\\\"},{\\\"include\\\":\\\"#bibtex-macro\\\"},{\\\"include\\\":\\\"#bibliography-anchor\\\"}]},\\\"kbd-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(kbd|btn):(\\\\\\\\[)((?:\\\\\\\\\\\\\\\\]|[^]])+?)(])\\\",\\\"name\\\":\\\"markup.macro.kbd.asciidoc\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\\\",\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},{\\\"match\\\":\\\"(comment|example|literal|listing|normal|pass|quote|sidebar|source|verse|abstract|partintro)\\\",\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},{\\\"match\\\":\\\"(actdiag|blockdiag|ditaa|graphviz|tikz|meme|mermaid|nwdiag|packetdiag|pikchr|plantuml|rackdiag|seqdiag|shaape|wavedrom)\\\",\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},{\\\"match\\\":\\\"(sect[1-4]|preface|colophon|dedication|glossary|bibliography|synopsis|appendix|index|normal|partintro|music|latex|stem)\\\",\\\"name\\\":\\\"entity.name.function.asciidoc\\\"}]},\\\"line-break\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.line-break.asciidoc\\\"}},\\\"match\\\":\\\"(?<=\\\\\\\\S)\\\\\\\\p{blank}+(\\\\\\\\+)$\\\"}]},\\\"line-break-backslash\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.line-break.asciidoc\\\"}},\\\"match\\\":\\\"(?<=\\\\\\\\S)\\\\\\\\p{blank}+(\\\\\\\\\\\\\\\\)$\\\"}]},\\\"link-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?:^|[]();<>\\\\\\\\[\\\\\\\\s])((?<!\\\\\\\\\\\\\\\\)(?:https?|file|ftp|irc)://[^]<\\\\\\\\[\\\\\\\\s]*[^]),.<\\\\\\\\[\\\\\\\\s])(?:\\\\\\\\[((?:\\\\\\\\\\\\\\\\]|[^]])*?)])?\\\",\\\"name\\\":\\\"markup.other.url.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.substitution.attribute-reference.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?:^|[]();<>\\\\\\\\[[:blank:]])((?<!\\\\\\\\\\\\\\\\)\\\\\\\\{uri-\\\\\\\\w+(?:-\\\\\\\\w+)*(?<!\\\\\\\\\\\\\\\\)})\\\\\\\\[((?:\\\\\\\\\\\\\\\\]|[^]])*?)]\\\",\\\"name\\\":\\\"markup.other.url.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(link|mailto):([^\\\\\\\\[\\\\\\\\s]+)\\\\\\\\[((?:\\\\\\\\\\\\\\\\]|[^]])*?)]\\\",\\\"name\\\":\\\"markup.other.url.asciidoc\\\"},{\\\"match\\\":\\\"\\\\\\\\p{word}[-%+.[:word:]]*(@)\\\\\\\\p{alnum}[-.[:alnum:]]*(\\\\\\\\.)\\\\\\\\p{alpha}{2,4}\\\\\\\\b\\\",\\\"name\\\":\\\"markup.link.email.asciidoc\\\"}]},\\\"list\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.list.bullet.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.todo.box.asciidoc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(-)\\\\\\\\p{blank}(\\\\\\\\[[*x[:blank:]]])(?=\\\\\\\\p{blank})\\\",\\\"name\\\":\\\"markup.todo.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.list.bullet.asciidoc\\\"}},\\\"match\\\":\\\"^\\\\\\\\p{blank}*(-|\\\\\\\\*{1,5}|•{1,5})(?=\\\\\\\\p{blank})\\\",\\\"name\\\":\\\"markup.list.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.list.bullet.asciidoc\\\"}},\\\"match\\\":\\\"^\\\\\\\\p{blank}*(\\\\\\\\.{1,5}|\\\\\\\\d+\\\\\\\\.|[A-Za-z]\\\\\\\\.|[IVXivx]+\\\\\\\\))(?=\\\\\\\\p{blank})\\\",\\\"name\\\":\\\"markup.list.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#link-macro\\\"},{\\\"include\\\":\\\"#attribute-reference\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"markup.list.bullet.asciidoc\\\"}},\\\"match\\\":\\\"^\\\\\\\\p{blank}*(.*?\\\\\\\\S)(:{2,4}|;;)($|\\\\\\\\p{blank}+)\\\",\\\"name\\\":\\\"markup.heading.list.asciidoc\\\"}]},\\\"listing-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(listing)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.block.listing.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(listing)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\"},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\"},{\\\"include\\\":\\\"#inlines\\\"}]}]},\\\"literal-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(literal)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.block.literal.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(literal)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\.{4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\"},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\"},{\\\"include\\\":\\\"#inlines\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\.{4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"name\\\":\\\"markup.block.literal.asciidoc\\\"}]},\\\"mark\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.mark.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)(\\\\\\\\[[^]]+?])((##)(.+?)(##))\\\",\\\"name\\\":\\\"markup.mark.unconstrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.highlight.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)((##)(.+?)(##))\\\",\\\"name\\\":\\\"markup.mark.unconstrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.mark.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<![#:;\\\\\\\\\\\\\\\\[:word:]])(\\\\\\\\[[^]]+?])((#)(\\\\\\\\S(?:|.*?\\\\\\\\S))(#)(?!\\\\\\\\p{word}))\\\",\\\"name\\\":\\\"markup.mark.constrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.highlight.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<![#:;\\\\\\\\\\\\\\\\[:word:]])(\\\\\\\\[[^]]+?])?((#)(\\\\\\\\S(?:|.*?\\\\\\\\S))(#)(?!\\\\\\\\p{word}))\\\",\\\"name\\\":\\\"markup.mark.constrained.asciidoc\\\"}]},\\\"menu-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.link.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(menu):(\\\\\\\\p{word}(?:|.*?\\\\\\\\S))\\\\\\\\[\\\\\\\\p{blank}*(.+?)?]\\\",\\\"name\\\":\\\"markup.other.menu.asciidoc\\\"}]},\\\"monospace\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.raw.monospace.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[.+?])?((``)(.+?)(``))\\\",\\\"name\\\":\\\"markup.monospace.unconstrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.raw.monospace.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\"':;\\\\\\\\\\\\\\\\`[:word:]])(\\\\\\\\[.+?])?((`)(\\\\\\\\S(?:|.*?\\\\\\\\S))(`))(?![\\\\\\\"'`[:word:]])\\\",\\\"name\\\":\\\"markup.monospace.constrained.asciidoc\\\"}]},\\\"open-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(-{2})$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"}},\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.symbol.asciidoc\\\"}},\\\"name\\\":\\\"markup.block.open.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"passthrough-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"support.constant.asciidoc\\\"}},\\\"match\\\":\\\"(?:(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[([^]]+?)]))?\\\\\\\\\\\\\\\\{0,2}(?<delim>\\\\\\\\+{2,3}|\\\\\\\\${2})(.*?)(\\\\\\\\k<delim>)\\\",\\\"name\\\":\\\"markup.macro.inline.passthrough.asciidoc\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(pass:)([,a-z]*)(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"]|^$\\\",\\\"name\\\":\\\"markup.macro.inline.passthrough.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},\\\"passthrough-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(pass)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\+\\\\\\\\+)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.block.passthrough.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(pass)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\+{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},{\\\"begin\\\":\\\"^(\\\\\\\\+{4,})$\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"markup.block.passthrough.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}]}]},\\\"quote-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(quote|verse)([#%,.]([^],]+))*]$))\\\",\\\"end\\\":\\\"((?<=____|\\\\\\\"\\\\\\\"|--)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.italic.quotes.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(quote|verse)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"begin\\\":\\\"^(_{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\"{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]},{\\\"begin\\\":\\\"^(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(?<=\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]}]},{\\\"begin\\\":\\\"^(\\\\\\\"\\\\\\\")$\\\",\\\"end\\\":\\\"^\\\\\\\\1$\\\",\\\"name\\\":\\\"markup.italic.quotes.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\p{blank}*(>) \\\",\\\"end\\\":\\\"^\\\\\\\\p{blank}*?$\\\",\\\"name\\\":\\\"markup.italic.quotes.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#list\\\"}]}]},\\\"sidebar-paragraph\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(sidebar)([#%,.][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\*\\\\\\\\*\\\\\\\\*\\\\\\\\*)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.block.sidebar.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(sidebar)([#%,.]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\*{4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(-{2})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#inlines\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\*{4,})$\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"name\\\":\\\"markup.block.sidebar.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"source-asciidoctor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(css(?:|.erb)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.css.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(css(?:|.erb)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(html?|shtml|xhtml|inc|tmpl|tpl))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.basic.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(html?|shtml|xhtml|inc|tmpl|tpl))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.html\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.html\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.html\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ini|conf))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.ini.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ini|conf))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ini\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ini\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ini\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ini\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ini\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ini\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(java|bsh))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.java.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(java|bsh))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(lua))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.lua.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(lua))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.lua\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.lua\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.lua\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:[Mm]|GNUm|OCamlM)akefile))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.makefile.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:[Mm]|GNUm|OCamlM)akefile))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.makefile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.makefile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.makefile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.makefile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.makefile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.makefile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(perl|pl|pm|pod|t|PL|psgi|vcl))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.perl.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(perl|pl|pm|pod|t|PL|psgi|vcl))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([RSrs]|Rprofile|\\\\\\\\{\\\\\\\\.r.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.r.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([RSrs]|Rprofile|\\\\\\\\{\\\\\\\\.r.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ruby|rbx??|rjs|Rakefile|rake|cgi|fcgi|gemspec|irbrc|Capfile|ru|prawn|Cheffile|Gemfile|Guardfile|Hobofile|Vagrantfile|Appraisals|Rantfile|Berksfile|Berksfile.lock|Thorfile|Puppetfile))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.ruby.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ruby|rbx??|rjs|Rakefile|rake|cgi|fcgi|gemspec|irbrc|Capfile|ru|prawn|Cheffile|Gemfile|Guardfile|Hobofile|Vagrantfile|Appraisals|Rantfile|Berksfile|Berksfile.lock|Thorfile|Puppetfile))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(php3??|php4|php5|phpt|phtml|aw|ctp))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.php.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(php3??|php4|php5|phpt|phtml|aw|ctp))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.php\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"source.php\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.php\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"source.php\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.php\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"source.php\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(sql|ddl|dml))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.sql.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(sql|ddl|dml))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(vb))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.vs_net.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(vb))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.vs_net\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asp.vb.net\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.vs_net\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asp.vb.net\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.vs_net\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asp.vb.net\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(xml|xsd|tld|jsp|pt|cpt|dtml|rss|opml))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.xml.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(xml|xsd|tld|jsp|pt|cpt|dtml|rss|opml))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(xslt??))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.xsl.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(xslt??))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml.xsl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml.xsl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml.xsl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ya?ml))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.yaml.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(ya?ml))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yaml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yaml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yaml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(bat(?:|ch)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.dosbatch.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(bat(?:|ch)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dosbatch\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.batchfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dosbatch\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.batchfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dosbatch\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.batchfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(cl(?:js??|ojure)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.clojure.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(cl(?:js??|ojure)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.clojure\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.clojure\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.clojure\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.clojure\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.clojure\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.clojure\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(coffee|Cakefile|coffee.erb))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.coffee.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(coffee|Cakefile|coffee.erb))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.coffee\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.coffee\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.coffee\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.coffee\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.coffee\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.coffee\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([ch]))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.c.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([ch]))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.c\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.c\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.c\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(c(?:pp|\\\\\\\\+\\\\\\\\+|xx)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.cpp.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(c(?:pp|\\\\\\\\+\\\\\\\\+|xx)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.cpp source.cpp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.cpp source.cpp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.cpp source.cpp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(patch|diff|rej))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.diff.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(patch|diff|rej))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.diff\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.diff\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.diff\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([Dd]ockerfile))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.dockerfile.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:([Dd]ockerfile))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dockerfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dockerfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dockerfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:COMMIT_EDIT|MERGE_)MSG))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.git_commit.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:COMMIT_EDIT|MERGE_)MSG))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-commit\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-commit\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-commit\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(git-rebase-todo))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.git_rebase.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(git-rebase-todo))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_rebase\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-rebase\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_rebase\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-rebase\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_rebase\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-rebase\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(go(?:|lang)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.go.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(go(?:|lang)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.go\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.go\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.go\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(g(?:roovy|vy)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.groovy.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(g(?:roovy|vy)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.groovy\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.groovy\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.groovy\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jade|pug))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.pug.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jade|pug))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.pug\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.pug\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.pug\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.pug\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.pug\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.pug\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jsx??|javascript|es6|mjs|cjs|dataviewjs|\\\\\\\\{\\\\\\\\.js.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.js.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jsx??|javascript|es6|mjs|cjs|dataviewjs|\\\\\\\\{\\\\\\\\.js.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(regexp))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.js_regexp.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(regexp))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.js_regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js.regexp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.js_regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js.regexp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.js_regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js.regexp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(json5??|sublime-settings|sublime-menu|sublime-keymap|sublime-mousemap|sublime-theme|sublime-build|sublime-project|sublime-completions))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.json.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(json5??|sublime-settings|sublime-menu|sublime-keymap|sublime-mousemap|sublime-theme|sublime-build|sublime-project|sublime-completions))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jsonc))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.jsonc.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(jsonc))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json.comments\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json.comments\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json.comments\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(less))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.less.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(less))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.less\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.less\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.less\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(objectivec|objective-c|mm|objc|obj-c|[hm]))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.objc.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(objectivec|objective-c|mm|objc|obj-c|[hm]))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.objc\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.objc\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.objc\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(swift))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.swift.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(swift))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.swift\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.swift\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.swift\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.swift\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.swift\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.swift\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(scss))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.scss.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(scss))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.scss\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.scss\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.scss\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(perl6|p6|pl6|pm6|nqp))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.perl6.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(perl6|p6|pl6|pm6|nqp))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl6\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl.6\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl6\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl.6\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl6\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl.6\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(p(?:owershell|s1|sm1|sd1|wsh)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.powershell.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(p(?:owershell|s1|sm1|sd1|wsh)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.powershell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.powershell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.powershell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.powershell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.powershell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.powershell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(python|py3??|rpy|pyw|cpy|SConstruct|Sconstruct|sconstruct|SConscript|gypi??|\\\\\\\\{\\\\\\\\.python.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.python.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(python|py3??|rpy|pyw|cpy|SConstruct|Sconstruct|sconstruct|SConscript|gypi??|\\\\\\\\{\\\\\\\\.python.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(julia|\\\\\\\\{\\\\\\\\.julia.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.julia.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(julia|\\\\\\\\{\\\\\\\\.julia.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(re))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.regexp_python.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(re))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.regexp_python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.regexp.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.regexp_python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.regexp.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.regexp_python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.regexp.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(rust|rs|\\\\\\\\{\\\\\\\\.rust.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.rust.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(rust|rs|\\\\\\\\{\\\\\\\\.rust.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.rust\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.rust\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.rust\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(s(?:cala|bt)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.scala.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(s(?:cala|bt)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.scala\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.scala\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.scala\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(shell|sh|bash|zsh|bashrc|bash_profile|bash_login|profile|bash_logout|.textmate_init|\\\\\\\\{\\\\\\\\.bash.+?}))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.shell.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(shell|sh|bash|zsh|bashrc|bash_profile|bash_login|profile|bash_logout|.textmate_init|\\\\\\\\{\\\\\\\\.bash.+?}))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.shellscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.shellscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.shellscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(t(?:ypescript|s)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.ts.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(t(?:ypescript|s)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(tsx))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.tsx.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(tsx))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescriptreact\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescriptreact\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescriptreact\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(c(?:s|sharp|#)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.csharp.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(c(?:s|sharp|#)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.csharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cs\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.csharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cs\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.csharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cs\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(f(?:s|sharp|#)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.fsharp.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(f(?:s|sharp|#)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.fsharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.fsharp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.fsharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.fsharp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.fsharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.fsharp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(dart))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.dart.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(dart))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dart\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dart\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dart\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dart\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dart\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dart\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(h(?:andlebars|bs)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.handlebars.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(h(?:andlebars|bs)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.handlebars\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.handlebars\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.handlebars\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.handlebars\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.handlebars\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.handlebars\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(m(?:arkdown|d)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.markdown.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(m(?:arkdown|d)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.markdown\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.markdown\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.markdown\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(log))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.log.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(log))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.log\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.log\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.log\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.log\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.log\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.log\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(erlang))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.erlang.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(erlang))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.erlang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.erlang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.erlang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.erlang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.erlang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.erlang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(elixir))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.elixir.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(elixir))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.elixir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.elixir\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.elixir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.elixir\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.elixir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.elixir\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:la|)tex))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.latex.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:((?:la|)tex))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(bibtex))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.bibtex.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(bibtex))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.bibtex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.bibtex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.bibtex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(twig))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.twig.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(twig))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.twig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.twig\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.twig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.twig\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.twig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.twig\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(yang))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.yang.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(yang))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(abap))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.abap.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(abap))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.abap\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.abap\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.abap\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.abap\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.abap\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.abap\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(r(?:estructuredtext|st)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.restructuredtext.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(r(?:estructuredtext|st)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.restructuredtext\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rst\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.restructuredtext\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rst\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.restructuredtext\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rst\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(haskell))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.haskell.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(haskell))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]},{\\\"begin\\\":\\\"(?=(?>^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(k(?:otlin|t)))([#,][^]]+)*]$))\\\",\\\"end\\\":\\\"((?<=--|\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\\\\\\.)|^\\\\\\\\p{blank}*)$\\\",\\\"name\\\":\\\"markup.code.kotlin.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.heading.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-attribute-inner\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\[(source)[#,]\\\\\\\\p{blank}*(?i:(k(?:otlin|t)))([#,]([^],]+))*]$\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.kotlin\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.kotlin\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{4,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(-{2})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.kotlin\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.kotlin\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(-{2})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\.{4,})\\\\\\\\s*$\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"include\\\":\\\"#include-directive\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.kotlin\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.kotlin\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!(\\\\\\\\.{4,})\\\\\\\\s*$)\\\"}]}]}]},\\\"source-markdown\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(css(?:|.erb))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.css.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(html?|shtml|xhtml|inc|tmpl|tpl)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.basic.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.html\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(ini|conf)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.ini.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ini\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ini\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(java|bsh)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.java.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.java\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(lua)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.lua.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.lua\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lua\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:((?:[Mm]|GNUm|OCamlM)akefile)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.makefile.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.makefile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.makefile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(perl|pl|pm|pod|t|PL|psgi|vcl)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.perl.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:([RSrs]|Rprofile|\\\\\\\\{\\\\\\\\.r.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.r.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(ruby|rbx??|rjs|Rakefile|rake|cgi|fcgi|gemspec|irbrc|Capfile|ru|prawn|Cheffile|Gemfile|Guardfile|Hobofile|Vagrantfile|Appraisals|Rantfile|Berksfile|Berksfile.lock|Thorfile|Puppetfile)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.ruby.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.ruby\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ruby\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(php3??|php4|php5|phpt|phtml|aw|ctp)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.php.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.php\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"source.php\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(sql|ddl|dml)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.sql.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(vb)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.vs_net.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.vs_net\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asp.vb.net\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(xml|xsd|tld|jsp|pt|cpt|dtml|rss|opml)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.xml.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(xslt??)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.xsl.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.xsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml.xsl\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(ya?ml)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.yaml.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yaml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yaml\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(bat(?:|ch))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.dosbatch.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dosbatch\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.batchfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(cl(?:js??|ojure))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.clojure.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.clojure\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.clojure\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(coffee|Cakefile|coffee.erb)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.coffee.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.coffee\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.coffee\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:([ch])((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.c.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.c\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(c(?:pp|\\\\\\\\+\\\\\\\\+|xx))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.cpp.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.cpp source.cpp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(patch|diff|rej)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.diff.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.diff\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:([Dd]ockerfile)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.dockerfile.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dockerfile\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:((?:COMMIT_EDIT|MERGE_)MSG)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.git_commit.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-commit\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(git-rebase-todo)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.git_rebase.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.git_rebase\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.git-rebase\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(go(?:|lang))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.go.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.go\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(g(?:roovy|vy))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.groovy.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.groovy\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(jade|pug)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.pug.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.pug\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.pug\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(jsx??|javascript|es6|mjs|cjs|dataviewjs|\\\\\\\\{\\\\\\\\.js.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.js.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(regexp)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.js_regexp.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.js_regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js.regexp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(json5??|sublime-settings|sublime-menu|sublime-keymap|sublime-mousemap|sublime-theme|sublime-build|sublime-project|sublime-completions)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.json.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(jsonc)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.jsonc.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.jsonc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json.comments\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(less)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.less.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.less\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(objectivec|objective-c|mm|objc|obj-c|[hm])((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.objc.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.objc\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(swift)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.swift.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.swift\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.swift\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(scss)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.scss.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.scss\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(perl6|p6|pl6|pm6|nqp)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.perl6.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.perl6\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.perl.6\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(p(?:owershell|s1|sm1|sd1|wsh))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.powershell.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.powershell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.powershell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(python|py3??|rpy|pyw|cpy|SConstruct|Sconstruct|sconstruct|SConscript|gypi??|\\\\\\\\{\\\\\\\\.python.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.python.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(julia|\\\\\\\\{\\\\\\\\.julia.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.julia.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.julia\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(re)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.regexp_python.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.regexp_python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.regexp.python\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(rust|rs|\\\\\\\\{\\\\\\\\.rust.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.rust.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.rust\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rust\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(s(?:cala|bt))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.scala.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.scala\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(shell|sh|bash|zsh|bashrc|bash_profile|bash_login|profile|bash_logout|.textmate_init|\\\\\\\\{\\\\\\\\.bash.+?})((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.shell.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.shellscript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(t(?:ypescript|s))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.ts.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(tsx)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.tsx.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.typescriptreact\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(c(?:s|sharp|#))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.csharp.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.csharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cs\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(f(?:s|sharp|#))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.fsharp.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.fsharp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.fsharp\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(dart)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.dart.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.dart\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.dart\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(h(?:andlebars|bs))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.handlebars.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.handlebars\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.handlebars\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(m(?:arkdown|d))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.markdown.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.markdown\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.markdown\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(log)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.log.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.log\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.log\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(erlang)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.erlang.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.erlang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.erlang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(elixir)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.elixir.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.elixir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.elixir\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:((?:la|)tex)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.latex.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.latex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.tex.latex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(bibtex)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.bibtex.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.bibtex\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.bibtex\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(twig)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.twig.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.twig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.twig\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(yang)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.yang.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.yang\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.yang\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(abap)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.abap.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.abap\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.abap\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(r(?:estructuredtext|st))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.restructuredtext.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.restructuredtext\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.rst\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(haskell)((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.haskell.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.haskell\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(`{3,})\\\\\\\\s*(?i:(k(?:otlin|t))((\\\\\\\\s+|[,:?{])[^`]*)?$)\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\2)\\\\\\\\s*$\\\",\\\"name\\\":\\\"markup.code.kotlin.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-callout\\\"},{\\\"begin\\\":\\\"(^|\\\\\\\\G)(\\\\\\\\s*)(.*)\\\",\\\"contentName\\\":\\\"meta.embedded.block.kotlin\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.kotlin\\\"}],\\\"while\\\":\\\"(^|\\\\\\\\G)(?!\\\\\\\\s*(`{3,})\\\\\\\\s*$)\\\"}]}]},\\\"source-paragraphs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#source-asciidoctor\\\"},{\\\"include\\\":\\\"#source-markdown\\\"}]},\\\"stem-macro\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(stem|(?:latex|ascii)math):([,a-z]*)(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"]|^$\\\",\\\"name\\\":\\\"markup.macro.inline.stem.asciidoc\\\"}]},\\\"strong\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)(\\\\\\\\[.+?])?((\\\\\\\\*\\\\\\\\*)(.+?)(\\\\\\\\*\\\\\\\\*))\\\",\\\"name\\\":\\\"markup.strong.unconstrained.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<![*:;\\\\\\\\\\\\\\\\[:word:]])(\\\\\\\\[.+?])?((\\\\\\\\*)(\\\\\\\\S(?:|.*?\\\\\\\\S))(\\\\\\\\*)(?!\\\\\\\\p{word}))\\\",\\\"name\\\":\\\"markup.strong.constrained.asciidoc\\\"}]},\\\"subscript\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.sub.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.sub.subscript.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[.+?])?((~)(\\\\\\\\S+?)(~))\\\",\\\"name\\\":\\\"markup.subscript.asciidoc\\\"}]},\\\"superscript\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.super.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.super.superscript.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\[.+?])?((\\\\\\\\^)(\\\\\\\\S+?)(\\\\\\\\^))\\\",\\\"name\\\":\\\"markup.superscript.asciidoc\\\"}]},\\\"table-csv\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(,===)$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"name\\\":\\\"markup.table.csv.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.csv\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.cell.delimiter.asciidoc\\\"}},\\\"match\\\":\\\",\\\"},{\\\"include\\\":\\\"#general-block-macro\\\"}]}]},\\\"table-dsv\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(:===)$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"name\\\":\\\"markup.table.dsv.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.cell.delimiter.asciidoc\\\"}},\\\"match\\\":\\\":\\\"},{\\\"include\\\":\\\"#general-block-macro\\\"}]}]},\\\"table-nested\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(!===)$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"contentName\\\":\\\"markup.table.content.asciidoc\\\",\\\"end\\\":\\\"^(\\\\\\\\1)$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"name\\\":\\\"markup.table.nested.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.table.cell.delimiter.asciidoc\\\"}},\\\"match\\\":\\\"(^|[^\\\\\\\\\\\\\\\\[:blank:]]*)(?<!\\\\\\\\\\\\\\\\)(!)\\\"},{\\\"include\\\":\\\"#tables-includes\\\"}]}]},\\\"table-psv\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\|===)\\\\\\\\s*$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"contentName\\\":\\\"markup.table.content.asciidoc\\\",\\\"end\\\":\\\"^(\\\\\\\\1)\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"markup.table.delimiter.asciidoc\\\"}},\\\"name\\\":\\\"markup.table.asciidoc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.table.cell.delimiter.asciidoc\\\"}},\\\"match\\\":\\\"(^|[^\\\\\\\\\\\\\\\\[:blank:]]*)(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\|)\\\"},{\\\"include\\\":\\\"#tables-includes\\\"}]}]},\\\"tables\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#table-psv\\\"},{\\\"include\\\":\\\"#table-nested\\\"},{\\\"include\\\":\\\"#table-csv\\\"},{\\\"include\\\":\\\"#table-dsv\\\"}]},\\\"tables-includes\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#callout-list-item\\\"},{\\\"include\\\":\\\"#attribute-entry\\\"},{\\\"include\\\":\\\"#block-title\\\"},{\\\"include\\\":\\\"#explicit-paragraph\\\"},{\\\"include\\\":\\\"#section\\\"},{\\\"include\\\":\\\"#blocks\\\"},{\\\"include\\\":\\\"#list\\\"},{\\\"include\\\":\\\"#inlines\\\"},{\\\"include\\\":\\\"#line-break\\\"}]},\\\"titles\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^([#=]{6})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-5.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^([#=]{5})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-4.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^([#=]{4})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-3.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^([#=]{3})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-2.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^([#=]{2})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-1.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^([#=]{1})(\\\\\\\\p{blank}+)(?=\\\\\\\\S+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.heading.marker.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.heading.space.asciidoc\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"markup.heading.heading-0.asciidoc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"typographic-quotes\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?:^|(?<![:;[:word:]]))(\\\\\\\\[([^]]+?)])?(\\\\\\\"`)(\\\\\\\\S(?:|.*?\\\\\\\\S))(`\\\\\\\")(?!\\\\\\\\p{word})\\\",\\\"name\\\":\\\"markup.italic.quote.typographic-quotes.asciidoc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.asciidoc\\\"}},\\\"match\\\":\\\"(?:^|(?<![:;[:word:]]))(\\\\\\\\[([^]]+?)])?('`)(\\\\\\\\S(?:|.*?\\\\\\\\S))(`')(?!\\\\\\\\p{word})\\\",\\\"name\\\":\\\"markup.italic.quote.typographic-quotes.asciidoc\\\"}]},\\\"xref-macro\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.unquoted.asciidoc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.asciidoc\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(<<)([\\\\\\\"./:[:word:]]+,)?(.*?)(>>)\\\",\\\"name\\\":\\\"markup.reference.xref.asciidoc\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(xref:)([\\\\\\\"./:[:word:]].*?)(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.asciidoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.meta.attribute-list.asciidoc\\\"}},\\\"contentName\\\":\\\"string.unquoted.asciidoc\\\",\\\"end\\\":\\\"]|^$\\\",\\\"name\\\":\\\"markup.reference.xref.asciidoc\\\"}]}},\\\"scopeName\\\":\\\"text.asciidoc\\\",\\\"embeddedLangs\\\":[],\\\"aliases\\\":[\\\"adoc\\\"],\\\"embeddedLangsLazy\\\":[\\\"html\\\",\\\"yaml\\\",\\\"csv\\\",\\\"css\\\",\\\"ini\\\",\\\"java\\\",\\\"lua\\\",\\\"make\\\",\\\"perl\\\",\\\"r\\\",\\\"ruby\\\",\\\"php\\\",\\\"sql\\\",\\\"vb\\\",\\\"xml\\\",\\\"xsl\\\",\\\"bat\\\",\\\"clojure\\\",\\\"coffee\\\",\\\"c\\\",\\\"cpp\\\",\\\"diff\\\",\\\"docker\\\",\\\"git-commit\\\",\\\"git-rebase\\\",\\\"go\\\",\\\"groovy\\\",\\\"pug\\\",\\\"javascript\\\",\\\"json\\\",\\\"jsonc\\\",\\\"less\\\",\\\"objective-c\\\",\\\"swift\\\",\\\"scss\\\",\\\"raku\\\",\\\"powershell\\\",\\\"python\\\",\\\"julia\\\",\\\"regexp\\\",\\\"rust\\\",\\\"scala\\\",\\\"shellscript\\\",\\\"typescript\\\",\\\"tsx\\\",\\\"csharp\\\",\\\"fsharp\\\",\\\"dart\\\",\\\"handlebars\\\",\\\"markdown\\\",\\\"log\\\",\\\"erlang\\\",\\\"elixir\\\",\\\"latex\\\",\\\"bibtex\\\",\\\"abap\\\",\\\"rst\\\",\\\"haskell\\\",\\\"kotlin\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0], "debugId": null}}]}