---
title: Introduction
description: 'Welcome to your new documentation'
icon: Book
---

## Getting Started

The first step to creating amazing documentation is setting up your editing environment.

<Cards cols={2}>
  <Card title="Quickstart" icon={<PlayIcon />} href="/docs/app/quickstart">
    Learn how to set up your docs for easy local development.
  </Card>
  <Card title="Routing" icon={<FilesIcon />} href="/docs/app/essentials/routing">
    Learn how to structure your .mdx files and folders to define the sidebar
    layout in Fumadocs
  </Card>
</Cards>

## Make it yours

Customize your documentation to reflect your brand and include meaningful content to maximize user engagement and conversions.

<Cards cols={2}>
  <Card
    title="Customize Style"
    icon={<PaletteIcon />}
    href="https://fumadocs.dev/docs/ui/theme"
  >
    Customize your documentation by applying your brand colors and styles.
  </Card>
  <Card
    title="Reference APIs"
    icon={<WebhookIcon />}
    href="/docs/app/features/openapi"
  >
    Auto-generate interactive endpoint docs straight from your OpenAPI spec.
  </Card>
  <Card
    title="Add Components"
    icon={<PuzzleIcon />}
    href="https://fumadocs.dev/docs/ui/components"
  >
    Embed interactive elements and guides to improve user engagement.
  </Card>
  <Card
    title="Get Inspiration"
    icon={<SparklesIcon />}
    href="https://fumadocs.dev/showcase"
  >
    Browse our showcase for creative ideas and best practices.
  </Card>
</Cards>
