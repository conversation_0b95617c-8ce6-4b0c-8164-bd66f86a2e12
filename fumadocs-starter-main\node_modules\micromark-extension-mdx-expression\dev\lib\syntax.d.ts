/**
 * Create an extension for `micromark` to enable MDX expression syntax.
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns {Extension}
 *   Extension for `micromark` that can be passed in `extensions` to enable MDX
 *   expression syntax.
 */
export function mdxExpression(options?: Options | null | undefined): Extension;
import type { Options } from 'micromark-extension-mdx-expression';
import type { Extension } from 'micromark-util-types';
//# sourceMappingURL=syntax.d.ts.map