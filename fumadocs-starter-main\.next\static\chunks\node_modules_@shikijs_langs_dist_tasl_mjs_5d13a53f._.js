(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/@shikijs/langs/dist/tasl.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Tasl\",\"fileTypes\":[\"tasl\"],\"name\":\"tasl\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#namespace\"},{\"include\":\"#type\"},{\"include\":\"#class\"},{\"include\":\"#edge\"}],\"repository\":{\"class\":{\"begin\":\"^\\\\s*(class)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.class\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#key\"},{\"include\":\"#export\"},{\"include\":\"#expression\"}]},\"comment\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.tasl\"}},\"match\":\"(#).*$\",\"name\":\"comment.line.number-sign.tasl\"},\"component\":{\"begin\":\"->\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.tasl.component\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"coproduct\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.coproduct\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.coproduct\"}},\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#term\"},{\"include\":\"#option\"}]},\"datatype\":{\"match\":\"[A-Za-z][0-9A-Za-z]*:(?:[!$\\\\&-;=?-Z_a-z~]|%\\\\h{2})+\",\"name\":\"string.regexp\"},\"edge\":{\"begin\":\"^\\\\s*(edge)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.edge\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#key\"},{\"include\":\"#export\"},{\"match\":\"=/\",\"name\":\"punctuation.separator.tasl.edge.source\"},{\"match\":\"/=>\",\"name\":\"punctuation.separator.tasl.edge.target\"},{\"match\":\"=>\",\"name\":\"punctuation.separator.tasl.edge\"},{\"include\":\"#expression\"}]},\"export\":{\"match\":\"::\",\"name\":\"keyword.operator.tasl.export\"},\"expression\":{\"patterns\":[{\"include\":\"#literal\"},{\"include\":\"#uri\"},{\"include\":\"#product\"},{\"include\":\"#coproduct\"},{\"include\":\"#reference\"},{\"include\":\"#optional\"},{\"include\":\"#identifier\"}]},\"identifier\":{\"captures\":{\"1\":{\"name\":\"variable\"}},\"match\":\"([A-Za-z][0-9A-Za-z]*)\\\\b\"},\"key\":{\"match\":\"[A-Za-z][0-9A-Za-z]*:(?:[!$\\\\&-;=?-Z_a-z~]|%\\\\h{2})+\",\"name\":\"markup.bold entity.name.class\"},\"literal\":{\"patterns\":[{\"include\":\"#datatype\"}]},\"namespace\":{\"captures\":{\"1\":{\"name\":\"keyword.control.tasl.namespace\"},\"2\":{\"patterns\":[{\"include\":\"#namespaceURI\"},{\"match\":\"[A-Za-z][0-9A-Za-z]*\\\\b\",\"name\":\"entity.name\"}]}},\"match\":\"^\\\\s*(namespace)\\\\b(.*)\"},\"namespaceURI\":{\"match\":\"[a-z]+:[]!#-;=?-\\\\[_a-z~]+\",\"name\":\"markup.underline.link\"},\"option\":{\"begin\":\"<-\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.tasl.option\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"optional\":{\"begin\":\"\\\\?\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"product\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.product\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.product\"}},\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#term\"},{\"include\":\"#component\"}]},\"reference\":{\"captures\":{\"1\":{\"name\":\"markup.bold keyword.operator\"},\"2\":{\"patterns\":[{\"include\":\"#key\"}]}},\"match\":\"(\\\\*)\\\\s*(.*)\"},\"term\":{\"match\":\"[A-Za-z][0-9A-Za-z]*:(?:[!$\\\\&-;=?-Z_a-z~]|%\\\\h{2})+\",\"name\":\"entity.other.tasl.key\"},\"type\":{\"begin\":\"^\\\\s*(type)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.type\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"uri\":{\"match\":\"<>\",\"name\":\"variable.other.constant\"}},\"scopeName\":\"source.tasl\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}),
]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_tasl_mjs_5d13a53f._.js.map