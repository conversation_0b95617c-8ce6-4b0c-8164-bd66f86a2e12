{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_class_private_method_get.js"], "sourcesContent": ["function _class_private_method_get(receiver, privateSet, fn) {\n    if (!privateSet.has(receiver)) throw new TypeError(\"attempted to get private field on non-instance\");\n\n    return fn;\n}\nexport { _class_private_method_get as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,0BAA0B,QAAQ,EAAE,UAAU,EAAE,EAAE;IACvD,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IAEnD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_class_private_method_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_method_init(obj, privateSet) {\n    _check_private_redeclaration(obj, privateSet);\n    privateSet.add(obj);\n}\nexport { _class_private_method_init as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,2BAA2B,GAAG,EAAE,UAAU;IAC/C,IAAA,+KAA4B,EAAC,KAAK;IAClC,WAAW,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_get_prototype_of.js"], "sourcesContent": ["function _get_prototype_of(o) {\n    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n\n    return _get_prototype_of(o);\n}\nexport { _get_prototype_of as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB,CAAC;IACxB,oBAAoB,OAAO,cAAc,GAAG,OAAO,cAAc,GAAG,SAAS,eAAe,CAAC;QACzF,OAAO,EAAE,SAAS,IAAI,OAAO,cAAc,CAAC;IAChD;IAEA,OAAO,kBAAkB;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_super_prop_base.js"], "sourcesContent": ["import { _ as _get_prototype_of } from \"./_get_prototype_of.js\";\n\nfunction _super_prop_base(object, property) {\n    while (!Object.prototype.hasOwnProperty.call(object, property)) {\n        object = _get_prototype_of(object);\n        if (object === null) break;\n    }\n\n    return object;\n}\nexport { _super_prop_base as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,iBAAiB,MAAM,EAAE,QAAQ;IACtC,MAAO,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,UAAW;QAC5D,SAAS,IAAA,oKAAiB,EAAC;QAC3B,IAAI,WAAW,MAAM;IACzB;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_get.js"], "sourcesContent": ["import { _ as _super_prop_base } from \"./_super_prop_base.js\";\n\nfunction _get(target, property, receiver) {\n    if (typeof Reflect !== \"undefined\" && Reflect.get) _get = Reflect.get;\n    else {\n        _get = function get(target, property, receiver) {\n            var base = _super_prop_base(target, property);\n\n            if (!base) return;\n\n            var desc = Object.getOwnPropertyDescriptor(base, property);\n\n            if (desc.get) return desc.get.call(receiver || target);\n\n            return desc.value;\n        };\n    }\n\n    return _get(target, property, receiver || target);\n}\nexport { _get as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,KAAK,MAAM,EAAE,QAAQ,EAAE,QAAQ;IACpC,IAAI,OAAO,YAAY,eAAe,QAAQ,GAAG,EAAE,OAAO,QAAQ,GAAG;SAChE;QACD,OAAO,SAAS,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ;YAC1C,IAAI,OAAO,IAAA,mKAAgB,EAAC,QAAQ;YAEpC,IAAI,CAAC,MAAM;YAEX,IAAI,OAAO,OAAO,wBAAwB,CAAC,MAAM;YAEjD,IAAI,KAAK,GAAG,EAAE,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,YAAY;YAE/C,OAAO,KAAK,KAAK;QACrB;IACJ;IAEA,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40swc/helpers/esm/_tagged_template_literal.js"], "sourcesContent": ["function _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\nexport { _tagged_template_literal as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,yBAAyB,OAAO,EAAE,GAAG;IAC1C,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,OAAO,OAAO,MAAM,CAAC,OAAO,gBAAgB,CAAC,SAAS;QAAE,KAAK;YAAE,OAAO,OAAO,MAAM,CAAC;QAAK;IAAE;AAC/F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-parser/src/utils.ts"], "sourcesContent": ["function cpOf(char: string): number {\n  // Count code point length\n  if ([...char].length !== 1) {\n    throw new Error(`Expected \"${char}\" to be a single code point`);\n  }\n  return char.codePointAt(0)!;\n}\n\nfunction getOrInsert<Key, Value>(map: Map<Key, Value>, key: Key, defaultValue: Value): Value {\n  if (!map.has(key)) {\n    map.set(key, defaultValue);\n  }\n  return map.get(key)!;\n}\n\nconst PosixClassNames = new Set([\n  'alnum',\n  'alpha',\n  'ascii',\n  'blank',\n  'cntrl',\n  'digit',\n  'graph',\n  'lower',\n  'print',\n  'punct',\n  'space',\n  'upper',\n  'word',\n  'xdigit',\n]);\n\nconst r = String.raw;\n\nfunction throwIfNullish<Value>(value: Value, msg?: string): NonNullable<Value> {\n  if (value == null) {\n    throw new Error(msg ?? 'Value expected');\n  }\n  return value;\n}\n\nexport {\n  cpOf,\n  getOrInsert,\n  PosixClassNames,\n  r,\n  throwIfNullish,\n};\n"], "names": ["cpOf", "char", "getOrInsert", "map", "key", "defaultValue", "PosixClassNames", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "msg"], "mappings": ";;;;;;;;;;;;;AAAA,SAASA,EAAKC,CAAAA,CAAsB;IAElC,IAAI,CAAC;WAAGA,CAAI;KAAA,CAAE,MAAA,KAAW,GACvB,MAAM,IAAI,MAAM,aAAiB,OAAJA,CAAI,EAAA,4BAA6B;IAEhE,OAAOA,EAAK,WAAA,CAAY,CAAC;AAC3B;AAEA,SAASC,EAAwBC,CAAAA,EAAsBC,CAAAA,EAAUC,CAAAA,CAA4B;IAC3F,OAAKF,EAAI,GAAA,CAAIC,CAAG,KACdD,EAAI,GAAA,CAAIC,GAAKC,CAAY,GAEpBF,EAAI,GAAA,CAAIC,CAAG;AACpB;AAEA,MAAME,IAAkB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QACF;CAAC,GAEKC,IAAI,OAAO,GAAA;AAEjB,SAASC,EAAsBC,CAAAA,EAAcC,CAAAA,CAAkC;IAC7E,IAAID,KAAS,MACX,MAAM,IAAI,mCAAMC,IAAO,gBAAgB;IAEzC,OAAOD;AACT,CAEA,OACET,KAAA,KACAE,KAAA,YACAI,KAAA,gBACAC,KAAA,EACAC,KAAA", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-parser/src/tokenizer/tokenize.ts"], "sourcesContent": ["import {cpOf, PosixClassNames, r, throwIfNullish} from '../utils.js';\n\ntype Token =\n  AlternatorToken |\n  AssertionToken |\n  BackreferenceToken |\n  CharacterToken |\n  CharacterClassCloseToken |\n  CharacterClassHyphenToken |\n  CharacterClassIntersectorToken |\n  CharacterClassOpenToken |\n  CharacterSetToken |\n  DirectiveToken |\n  GroupCloseToken |\n  GroupOpenToken |\n  NamedCalloutToken |\n  QuantifierToken |\n  SubroutineToken;\n\ntype IntermediateToken =\n  EscapedNumberToken;\n\ntype TokenCharacterSetKind =\n  'any' |\n  'digit' |\n  'dot' |\n  'hex' |\n  'newline' |\n  'posix' |\n  'property' |\n  'space' |\n  'text_segment' |\n  'word';\n\ntype TokenDirectiveKind =\n  'flags' |\n  'keep';\n\ntype TokenGroupOpenKind =\n  'absence_repeater' |\n  'atomic' |\n  'capturing' |\n  'group' |\n  'lookahead' |\n  'lookbehind';\n\ntype TokenQuantifierKind =\n  'greedy' |\n  'lazy' |\n  'possessive';\n\ntype TokenNamedCalloutKind =\n  'count' |\n  'cmp' |\n  'error' |\n  'fail' |\n  'max' |\n  'mismatch' |\n  'skip' |\n  'total_count' |\n  'custom';\n\nconst charClassOpenPattern = r`\\[\\^?`;\nconst sharedEscapesPattern = `${\n  // Control char\n  'c.? | C(?:-.?)?'\n}|${\n  // Unicode property; Onig considers `\\p` an identity escape, but e.g. `\\p{`, `\\p{ ^L}`, and\n  // `\\p{gc=L}` are invalid\n  r`[pP]\\{(?:\\^?[-\\x20_]*[A-Za-z][-\\x20\\w]*\\})?`\n}|${\n  // Hex encoded byte sequence; attempt match before other `\\xNN` hex char\n  r`x[89A-Fa-f]\\p{AHex}(?:\\\\x[89A-Fa-f]\\p{AHex})*`\n}|${\n  // Hex char\n  r`u(?:\\p{AHex}{4})? | x\\{[^\\}]*\\}? | x\\p{AHex}{0,2}`\n}|${\n  // Enclosed octal code point\n  r`o\\{[^\\}]*\\}?`\n}|${\n  // Escaped number\n  r`\\d{1,3}`\n}`;\n// Even with flag x, Onig doesn't allow whitespace to separate a quantifier from the `?` or `+`\n// that makes it lazy or possessive. Possessive suffixes don't apply to interval quantifiers\nconst quantifierRe = /[?*+][?+]?|\\{(?:\\d+(?:,\\d*)?|,\\d+)\\}\\??/;\nconst tokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | [gk]<[^>]*>?\n    | [gk]'[^']*'?\n    | .\n  )\n  | \\( (?:\n    \\? (?:\n      [:=!>({]\n      | <[=!]\n      | <[^>]*>\n      | '[^']*'\n      | ~\\|?\n      | #(?:[^)\\\\]|\\\\.?)*\n      | [^:)]*[:)]\n    )?\n    | \\*[^\\)]*\\)?\n  )?\n  | (?:${quantifierRe.source})+\n  | ${charClassOpenPattern}\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\nconst charClassTokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | .\n  )\n  | \\[:(?:\\^?\\p{Alpha}+|\\^):\\]\n  | ${charClassOpenPattern}\n  | &&\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\n\ntype Context = {\n  captureGroup: boolean;\n  getCurrentModX(): boolean;\n  numOpenGroups: number;\n  popModX(): void;\n  pushModX(isXOn: boolean): void;\n  replaceCurrentModX(isXOn: boolean): void;\n  singleline: boolean;\n};\n\ntype TokenizeOptions = {\n  flags?: string;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n};\n\nfunction tokenize(pattern: string, options: TokenizeOptions = {}): {\n  tokens: Array<Token>;\n  flags: FlagProperties;\n} {\n  const opts = {\n    flags: '',\n    ...options,\n    rules: {\n      captureGroup: false, // `ONIG_OPTION_CAPTURE_GROUP`\n      singleline: false, // `ONIG_OPTION_SINGLELINE`\n      ...options.rules,\n    },\n  };\n  if (typeof pattern !== 'string') {\n    throw new Error('String expected as pattern');\n  }\n  const flagProperties = getFlagProperties(opts.flags);\n  const xStack = [flagProperties.extended];\n  const context: Context = {\n    captureGroup: opts.rules.captureGroup,\n    // Always at least has the top-level flag x\n    getCurrentModX() {return xStack.at(-1)!},\n    numOpenGroups: 0,\n    popModX() {xStack.pop()},\n    pushModX(isXOn) {xStack.push(isXOn)},\n    replaceCurrentModX(isXOn) {xStack[xStack.length - 1] = isXOn},\n    singleline: opts.rules.singleline,\n  };\n  let tokens: Array<Token | IntermediateToken> = [];\n  let match: RegExpExecArray | null;\n  tokenRe.lastIndex = 0;\n  while ((match = tokenRe.exec(pattern))) {\n    const result = getTokenWithDetails(context, pattern, match[0], tokenRe.lastIndex);\n    if (result.tokens) {\n      tokens.push(...result.tokens);\n    } else if (result.token) {\n      tokens.push(result.token);\n    }\n    if (result.lastIndex !== undefined) {\n      tokenRe.lastIndex = result.lastIndex;\n    }\n  }\n\n  const potentialUnnamedCaptureTokens: Array<GroupOpenToken> = [];\n  let numNamedAndOptInUnnamedCaptures = 0;\n  tokens.filter(t => t.type === 'GroupOpen').forEach(t => {\n    if (t.kind === 'capturing') {\n      t.number = ++numNamedAndOptInUnnamedCaptures;\n    } else if (t.raw === '(') {\n      potentialUnnamedCaptureTokens.push(t);\n    }\n  });\n  // Enable unnamed capturing groups if no named captures (when `captureGroup` not enabled)\n  if (!numNamedAndOptInUnnamedCaptures) {\n    potentialUnnamedCaptureTokens.forEach((t, i) => {\n      t.kind = 'capturing';\n      t.number = i + 1;\n    });\n  }\n  const numCaptures = numNamedAndOptInUnnamedCaptures || potentialUnnamedCaptureTokens.length;\n  // Can now split escaped nums accurately, accounting for number of captures\n  const tokensWithoutIntermediate = tokens.map(\n    t => t.type === 'EscapedNumber' ? splitEscapedNumberToken(t, numCaptures) : t\n  ).flat();\n\n  return {\n    tokens: tokensWithoutIntermediate,\n    flags: flagProperties,\n  };\n}\n\nfunction getTokenWithDetails(context: Context, pattern: string, m: string, lastIndex: number): {\n  token: Token | IntermediateToken;\n  tokens?: never;\n  lastIndex?: number;\n} | {\n  token?: never;\n  tokens: Array<Token | IntermediateToken>;\n  lastIndex?: number;\n} | {\n  token?: never;\n  tokens?: never;\n  lastIndex: number;\n} {\n  const [m0, m1] = m;\n\n  if (m === '[' || m === '[^') {\n    const result = getAllTokensForCharClass(pattern, m, lastIndex);\n    return {\n      // Array of all of the char class's tokens\n      tokens: result.tokens,\n      // Jump forward to the end of the char class\n      lastIndex: result.lastIndex,\n    };\n  }\n\n  if (m0 === '\\\\') {\n    if ('AbBGyYzZ'.includes(m1)) {\n      return {\n        token: createAssertionToken(m, m),\n      };\n    }\n    if (/^\\\\g[<']/.test(m)) {\n      if (!/^\\\\g(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createSubroutineToken(m),\n      };\n    }\n    if (/^\\\\k[<']/.test(m)) {\n      if (!/^\\\\k(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createBackreferenceToken(m),\n      };\n    }\n    if (m1 === 'K') {\n      return {\n        token: createDirectiveToken('keep', m),\n      };\n    }\n    if (m1 === 'N' || m1 === 'R') {\n      return {\n        token: createCharacterSetToken('newline', m, {\n          // `\\N` and `\\R` are not actually opposites since the former only excludes `\\n`\n          negate: m1 === 'N',\n        }),\n      };\n    }\n    if (m1 === 'O') {\n      return {\n        token: createCharacterSetToken('any', m),\n      };\n    }\n    if (m1 === 'X') {\n      return {\n        token: createCharacterSetToken('text_segment', m),\n      };\n    }\n    // Run last since it assumes an identity escape as final condition\n    const result = tokenizeSharedEscape(m, {inCharClass: false});\n    return Array.isArray(result) ? {tokens: result} : {token: result};\n  }\n\n  if (m0 === '(') {\n    if (m1 === '*') {\n      return {\n        token: tokenizeNamedCallout(m),\n      };\n    }\n    if (m === '(?{') {\n      throw new Error(`Unsupported callout \"${m}\"`);\n    }\n    // Comment group\n    if (m.startsWith('(?#')) {\n      // Everything except the closing unescaped `)` is included in the match\n      if (pattern[lastIndex] !== ')') {\n        throw new Error('Unclosed comment group \"(?#\"');\n      }\n      return {\n        // Jump forward to after the closing paren\n        lastIndex: lastIndex + 1,\n      };\n    }\n    // Flag modifier (directive or group opener)\n    if (/^\\(\\?[-imx]+[:)]$/.test(m)) {\n      return {\n        token: tokenizeFlagModifier(m, context),\n      };\n    }\n    // --- Remaining group types all reuse current flag x status ---\n    context.pushModX(context.getCurrentModX());\n    context.numOpenGroups++;\n    if (\n      // Unnamed capture if no named captures present and `captureGroup` not enabled, else\n      // noncapturing group\n      (m === '(' && !context.captureGroup) ||\n      // Noncapturing group\n      m === '(?:'\n    ) {\n      return {\n        // For `(`, will later change to `capturing` and add `number` prop if no named captures\n        token: createGroupOpenToken('group', m),\n      };\n    }\n    // Atomic group\n    if (m === '(?>') {\n      return {\n        token: createGroupOpenToken('atomic', m),\n      };\n    }\n    // Lookaround\n    if (m === '(?=' || m === '(?!' || m === '(?<=' || m === '(?<!') {\n      return {\n        token: createGroupOpenToken(m[2] === '<' ? 'lookbehind' : 'lookahead', m, {\n          negate: m.endsWith('!'),\n        }),\n      };\n    }\n    // Unnamed capture when `captureGroup` enabled, or named capture (checked after lookbehind due\n    // to similar syntax)\n    if (\n      (m === '(' && context.captureGroup) ||\n      (m.startsWith('(?<') && m.endsWith('>')) ||\n      (m.startsWith(\"(?'\") && m.endsWith(\"'\"))\n    ) {\n      return {\n        token: createGroupOpenToken('capturing', m, {\n          // Will add `number` prop in a second pass\n          ...(m !== '(' && {name: m.slice(3, -1)}),\n        }),\n      };\n    }\n    if (m.startsWith('(?~')) {\n      if (m === '(?~|') {\n        throw new Error(`Unsupported absence function kind \"${m}\"`);\n      }\n      return {\n        token: createGroupOpenToken('absence_repeater', m),\n      };\n    }\n    if (m === '(?(') {\n      // TODO: Add support\n      throw new Error(`Unsupported conditional \"${m}\"`);\n    }\n    throw new Error(`Invalid or unsupported group option \"${m}\"`);\n  }\n  if (m === ')') {\n    context.popModX();\n    context.numOpenGroups--;\n    if (context.numOpenGroups < 0) {\n      throw new Error('Unmatched \")\"');\n    }\n    return {\n      token: createGroupCloseToken(m),\n    };\n  }\n\n  if (context.getCurrentModX()) {\n    if (m === '#') {\n      // Onig's only line break char is line feed\n      const end = pattern.indexOf('\\n', lastIndex);\n      return {\n        // Jump forward to the end of the comment\n        lastIndex: end === -1 ? pattern.length : end,\n      };\n    }\n    if (/^\\s$/.test(m)) {\n      const re = /\\s+/y;\n      re.lastIndex = lastIndex;\n      const rest = re.exec(pattern);\n      return {\n        // Jump forward to the end of the whitespace\n        lastIndex: rest ? re.lastIndex : lastIndex,\n      };\n    }\n  }\n\n  if (m === '.') {\n    return {\n      token: createCharacterSetToken('dot', m),\n    };\n  }\n\n  if (m === '^' || m === '$') {\n    const kind = context.singleline ? {\n      '^': r`\\A`,\n      '$': r`\\Z`,\n    }[m] : m;\n    return {\n      token: createAssertionToken(kind, m),\n    };\n  }\n\n  if (m === '|') {\n    return {\n      token: createAlternatorToken(m),\n    };\n  }\n\n  if (quantifierRe.test(m)) {\n    return {\n      tokens: splitQuantifierMatch(m),\n    };\n  }\n\n  // `cpOf` asserts that it's a single code point\n  return {\n    token: createCharacterToken(cpOf(m), m),\n  };\n}\n\nfunction getAllTokensForCharClass(pattern: string, opener: CharacterClassOpener, lastIndex: number): {\n  tokens: Array<Token | IntermediateToken>;\n  lastIndex: number;\n} {\n  const tokens: Array<Token | IntermediateToken> = [createCharacterClassOpenToken(opener[1] === '^', opener)];\n  let numCharClassesOpen = 1;\n  let match: RegExpExecArray | null;\n  charClassTokenRe.lastIndex = lastIndex;\n  while ((match = charClassTokenRe.exec(pattern))) {\n    const m = match[0];\n    // Start of nested char class\n    // POSIX classes are handled as a single token; not as a nested char class\n    if (m[0] === '[' && m[1] !== ':') {\n      numCharClassesOpen++;\n      tokens.push(createCharacterClassOpenToken(m[1] === '^', m as CharacterClassOpener));\n    } else if (m === ']') {\n      // Always at least includes the char class opener\n      if (tokens.at(-1)!.type === 'CharacterClassOpen') {\n        // Allow unescaped `]` as leading char\n        tokens.push(createCharacterToken(93, m));\n      } else {\n        numCharClassesOpen--;\n        tokens.push(createCharacterClassCloseToken(m));\n        if (!numCharClassesOpen) {\n          break;\n        }\n      }\n    } else {\n      const result = tokenizeAnyTokenWithinCharClass(m);\n      if (Array.isArray(result)) {\n        tokens.push(...result);\n      } else {\n        tokens.push(result);\n      }\n    }\n  }\n  return {\n    tokens,\n    lastIndex: charClassTokenRe.lastIndex || pattern.length,\n  };\n}\n\nfunction tokenizeAnyTokenWithinCharClass(raw: string): Token | IntermediateToken | Array<Token> {\n  if (raw[0] === '\\\\') {\n    // Assumes an identity escape as final condition\n    return tokenizeSharedEscape(raw, {inCharClass: true});\n  }\n  // POSIX class: `[:name:]` or `[:^name:]`\n  if (raw[0] === '[') {\n    const posix = /\\[:(?<negate>\\^?)(?<name>[a-z]+):\\]/.exec(raw);\n    if (!posix || !PosixClassNames.has(posix.groups!.name)) {\n      throw new Error(`Invalid POSIX class \"${raw}\"`);\n    }\n    return createCharacterSetToken('posix', raw, {\n      value: posix.groups!.name,\n      negate: !!posix.groups!.negate,\n    });\n  }\n  // Range (possibly invalid) or literal hyphen\n  if (raw === '-') {\n    return createCharacterClassHyphenToken(raw);\n  }\n  if (raw === '&&') {\n    return createCharacterClassIntersectorToken(raw);\n  }\n  // `cpOf` asserts that it's a single code point\n  return createCharacterToken(cpOf(raw), raw);\n}\n\n// Tokens shared by base syntax and char class syntax that start with `\\`\nfunction tokenizeSharedEscape(raw: string, {inCharClass}: {inCharClass: boolean}): Token | IntermediateToken | Array<Token> {\n  const char1 = raw[1];\n  if (char1 === 'c' || char1 === 'C') {\n    return tokenizeControlCharacter(raw);\n  }\n  if ('dDhHsSwW'.includes(char1)) {\n    return tokenizeShorthand(raw);\n  }\n  if (raw.startsWith(r`\\o{`)) {\n    throw new Error(`Incomplete, invalid, or unsupported octal code point \"${raw}\"`);\n  }\n  if (/^\\\\[pP]\\{/.test(raw)) {\n    if (raw.length === 3) {\n      throw new Error(`Incomplete or invalid Unicode property \"${raw}\"`);\n    }\n    return tokenizeUnicodeProperty(raw);\n  }\n  // Hex UTF-8 encoded byte sequence\n  if (/^\\\\x[89A-Fa-f]\\p{AHex}/u.test(raw)) {\n    try {\n      const bytes = raw.split(/\\\\x/).slice(1).map(hex => parseInt(hex, 16));\n      const decoded = new TextDecoder('utf-8', {\n        ignoreBOM: true,\n        fatal: true,\n      }).decode(new Uint8Array(bytes));\n      const encoder = new TextEncoder();\n      const tokens = [...decoded].map(char => {\n        // Since this regenerates `raw`, it might have different casing for hex A-F than the input\n        const raw = [...encoder.encode(char)].map(byte => `\\\\x${byte.toString(16)}`).join('');\n        return createCharacterToken(cpOf(char), raw);\n      });\n      return tokens;\n    } catch {\n      throw new Error(`Multibyte code \"${raw}\" incomplete or invalid in Oniguruma`);\n    }\n  }\n  if (char1 === 'u' || char1 === 'x') {\n    return createCharacterToken(getValidatedHexCharCode(raw), raw);\n  }\n  if (EscapeCharCodes.has(char1)) {\n    return createCharacterToken(EscapeCharCodes.get(char1)!, raw);\n  }\n  // Escaped number: backref (possibly invalid), null, octal, or identity escape, possibly followed\n  // by 1-2 literal digits\n  if (/\\d/.test(char1)) {\n    return createEscapedNumberToken(inCharClass, raw);\n  }\n  if (raw === '\\\\') {\n    throw new Error(r`Incomplete escape \"\\\"`);\n  }\n  // Meta `\\M-x` and `\\M-\\C-x` are unsupported; avoid treating as an identity escape\n  if (char1 === 'M') {\n    // TODO: Add support. See:\n    // - <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#12-onig_syn_op2_esc_capital_m_bar_meta-enable-m-x>\n    // - <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>\n    // - <github.com/ammar/regexp_parser/blob/8851030feda68223d74f502335fb254a20d77016/lib/regexp_parser/expression/classes/escape_sequence.rb#L75>\n    throw new Error(`Unsupported meta \"${raw}\"`);\n  }\n  // Identity escape; count code point length\n  if ([...raw].length === 2) {\n    return createCharacterToken(raw.codePointAt(1)!, raw);\n  }\n  throw new Error(`Unexpected escape \"${raw}\"`);\n}\n\n// --------------------------------\n// --- Token creation and types ---\n// --------------------------------\n\ntype AlternatorToken = {\n  type: 'Alternator';\n  raw: '|';\n};\nfunction createAlternatorToken(raw: '|'): AlternatorToken {\n  return {\n    type: 'Alternator',\n    raw,\n  };\n}\n\ntype AssertionToken = {\n  type: 'Assertion';\n  kind: string;\n  raw: string;\n};\nfunction createAssertionToken(kind: string, raw: string): AssertionToken {\n  return {\n    type: 'Assertion',\n    kind,\n    raw,\n  };\n}\n\ntype BackreferenceToken = {\n  type: 'Backreference';\n  raw: string;\n};\nfunction createBackreferenceToken(raw: string): BackreferenceToken {\n  return {\n    type: 'Backreference',\n    raw,\n  };\n}\n\ntype CharacterToken = {\n  type: 'Character';\n  value: number;\n  raw: string;\n};\nfunction createCharacterToken(value: number, raw: string): CharacterToken {\n  return {\n    type: 'Character',\n    value,\n    raw,\n  };\n}\n\ntype CharacterClassCloseToken = {\n  type: 'CharacterClassClose';\n  raw: ']';\n};\nfunction createCharacterClassCloseToken(raw: ']'): CharacterClassCloseToken {\n  return {\n    type: 'CharacterClassClose',\n    raw,\n  };\n}\n\ntype CharacterClassHyphenToken = {\n  type: 'CharacterClassHyphen';\n  raw: '-';\n};\nfunction createCharacterClassHyphenToken(raw: '-'): CharacterClassHyphenToken {\n  return {\n    type: 'CharacterClassHyphen',\n    raw,\n  };\n}\n\ntype CharacterClassIntersectorToken = {\n  type: 'CharacterClassIntersector';\n  raw: '&&';\n};\nfunction createCharacterClassIntersectorToken(raw: '&&'): CharacterClassIntersectorToken {\n  return {\n    type: 'CharacterClassIntersector',\n    raw,\n  };\n}\n\ntype CharacterClassOpenToken = {\n  type: 'CharacterClassOpen';\n  negate: boolean;\n  raw: CharacterClassOpener;\n};\ntype CharacterClassOpener = '[' | '[^';\nfunction createCharacterClassOpenToken(negate: boolean, raw: CharacterClassOpener): CharacterClassOpenToken {\n  return {\n    type: 'CharacterClassOpen',\n    negate,\n    raw,\n  };\n}\n\ntype CharacterSetToken = {\n  type: 'CharacterSet';\n  kind: TokenCharacterSetKind;\n  value?: string;\n  negate?: boolean;\n  raw: string;\n};\nfunction createCharacterSetToken(\n  kind: TokenCharacterSetKind,\n  raw: string,\n  options: {\n    value?: string;\n    negate?: boolean;\n  } = {}\n): CharacterSetToken {\n  return {\n    type: 'CharacterSet',\n    kind,\n    ...options,\n    raw,\n  };\n}\n\ntype DirectiveToken = {\n  type: 'Directive';\n  raw: string;\n} & ({\n  kind: 'keep';\n  flags?: never;\n} | {\n  kind: 'flags';\n  flags: FlagGroupModifiers;\n});\nfunction createDirectiveToken(kind: TokenDirectiveKind, raw: string, options: {flags?: FlagGroupModifiers} = {}): DirectiveToken {\n  if (kind === 'keep') {\n    return {\n      type: 'Directive',\n      kind,\n      raw,\n    };\n  }\n  return {\n    type: 'Directive',\n    kind,\n    flags: throwIfNullish(options.flags),\n    raw,\n  };\n}\n\ntype EscapedNumberToken = {\n  type: 'EscapedNumber';\n  inCharClass: boolean;\n  raw: string;\n};\n/**\nIntermediate representation only; will become a `Backreference` or one or more `Character`s.\n*/\nfunction createEscapedNumberToken(inCharClass: boolean, raw: string): EscapedNumberToken {\n  return {\n    type: 'EscapedNumber',\n    inCharClass,\n    raw,\n  };\n}\n\ntype GroupCloseToken = {\n  type: 'GroupClose';\n  raw: ')';\n};\nfunction createGroupCloseToken(raw: ')'): GroupCloseToken {\n  return {\n    type: 'GroupClose',\n    raw,\n  };\n}\n\ntype GroupOpenToken = {\n  type: 'GroupOpen';\n  kind: TokenGroupOpenKind;\n  flags?: FlagGroupModifiers;\n  name?: string;\n  number?: number;\n  negate?: boolean;\n  raw: string;\n};\nfunction createGroupOpenToken(\n  kind: TokenGroupOpenKind,\n  raw: string,\n  options: {\n    flags?: FlagGroupModifiers;\n    name?: string;\n    number?: number;\n    negate?: boolean;\n  } = {}\n): GroupOpenToken {\n  return {\n    type: 'GroupOpen',\n    kind,\n    ...options,\n    raw,\n  };\n}\n\ntype NamedCalloutToken = {\n  type: 'NamedCallout';\n  kind: TokenNamedCalloutKind;\n  tag: string | null;\n  arguments: Array<string | number> | null;\n  raw: string;\n};\nfunction createNamedCalloutToken(\n  kind: TokenNamedCalloutKind,\n  tag: string | null,\n  args: Array<string | number> | null,\n  raw: string\n): NamedCalloutToken {\n  return {\n    type: 'NamedCallout',\n    kind,\n    tag,\n    arguments: args,\n    raw,\n  };\n};\n\ntype QuantifierToken = {\n  type: 'Quantifier';\n  kind: TokenQuantifierKind;\n  min: number;\n  max: number;\n  raw: string;\n};\nfunction createQuantifierToken(\n  kind: TokenQuantifierKind,\n  min: number,\n  max: number,\n  raw: string\n): QuantifierToken {\n  return {\n    type: 'Quantifier',\n    kind,\n    min,\n    max,\n    raw,\n  };\n}\n\ntype SubroutineToken = {\n  type: 'Subroutine';\n  raw: string;\n};\nfunction createSubroutineToken(raw: string): SubroutineToken {\n  return {\n    type: 'Subroutine',\n    raw,\n  };\n}\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\ntype FlagProperties = {\n  ignoreCase: boolean;\n  dotAll: boolean;\n  extended: boolean;\n  digitIsAscii: boolean;\n  posixIsAscii: boolean;\n  spaceIsAscii: boolean;\n  wordIsAscii: boolean;\n  textSegmentMode: 'grapheme' | 'word' | null;\n};\n\ntype FlagGroupModifiers = {\n  enable?: FlagGroupSwitches;\n  disable?: FlagGroupSwitches;\n};\n\ntype FlagGroupSwitches = {\n  ignoreCase?: true;\n  dotAll?: true;\n  extended?: true;\n};\n\nconst CalloutNames = new Set<Uppercase<Exclude<TokenNamedCalloutKind, 'custom'>>>([\n  'COUNT',\n  'CMP',\n  'ERROR',\n  'FAIL',\n  'MAX',\n  'MISMATCH',\n  'SKIP',\n  'TOTAL_COUNT',\n]);\n\nconst EscapeCharCodes = new Map([\n  ['a',  7], // alert/bell (Not available in JS)\n  ['b',  8], // backspace (only in char classes)\n  ['e', 27], // escape (Not available in JS)\n  ['f', 12], // form feed\n  ['n', 10], // line feed\n  ['r', 13], // carriage return\n  ['t',  9], // horizontal tab\n  ['v', 11], // vertical tab\n]);\n\n// Expects `\\cx` or `\\C-x`\nfunction tokenizeControlCharacter(raw: string): CharacterToken {\n  const char = raw[1] === 'c' ? raw[2] : raw[3];\n  if (!char || !/[A-Za-z]/.test(char)) {\n    // Unlike JS, Onig allows any char to follow `\\c` or `\\C-`, but this is an extreme edge case\n    // TODO: Add support. See:\n    // - <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#11-onig_syn_op2_esc_capital_c_bar_control-enable-c-x>\n    // - <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>\n    throw new Error(`Unsupported control character \"${raw}\"`);\n  }\n  return createCharacterToken(cpOf(char.toUpperCase()) - 64, raw);\n}\n\nfunction tokenizeFlagModifier(raw: string, context: Context): DirectiveToken | GroupOpenToken {\n  // Allows multiple `-` and solo `-` without `on` or `off` flags\n  let {on, off} = /^\\(\\?(?<on>[imx]*)(?:-(?<off>[-imx]*))?/.exec(raw)!.groups as {on: string, off: string | undefined};\n  off ??= '';\n  // Flag x is used directly by the tokenizer since it changes how to interpret the pattern\n  const isXOn = (context.getCurrentModX() || on.includes('x')) && !off.includes('x');\n  const enabledFlags = getFlagGroupSwitches(on);\n  const disabledFlags = getFlagGroupSwitches(off);\n  const flagChanges: FlagGroupModifiers = {};\n  enabledFlags && (flagChanges.enable = enabledFlags);\n  disabledFlags && (flagChanges.disable = disabledFlags);\n  // Flag directive; ex: `(?im-x)`\n  if (raw.endsWith(')')) {\n    // Replace flag x value until the end of the current group\n    context.replaceCurrentModX(isXOn);\n    // Can't remove flag directives without flags like `(?-)`; they affect following quantifiers\n    return createDirectiveToken('flags', raw, {\n      flags: flagChanges,\n    });\n  }\n  // Flag group opener; ex: `(?im-x:`\n  if (raw.endsWith(':')) {\n    context.pushModX(isXOn);\n    context.numOpenGroups++;\n    return createGroupOpenToken('group', raw, {\n      ...((enabledFlags || disabledFlags) && {flags: flagChanges}),\n    });\n  }\n  throw new Error(`Unexpected flag modifier \"${raw}\"`);\n}\n\nfunction tokenizeNamedCallout(raw: string): NamedCalloutToken {\n  const callout = /\\(\\*(?<name>[A-Za-z_]\\w*)?(?:\\[(?<tag>(?:[A-Za-z_]\\w*)?)\\])?(?:\\{(?<args>[^}]*)\\})?\\)/.exec(raw);\n  if (!callout) {\n    throw new Error(`Incomplete or invalid named callout \"${raw}\"`);\n  }\n  const {name, tag, args} = callout.groups as Partial<{\n    name: string;\n    tag: string;\n    args: string;\n  }>;\n  if (!name) {\n    throw new Error(`Invalid named callout \"${raw}\"`);\n  }\n  if (tag === '') {\n    throw new Error(`Named callout tag with empty value not allowed \"${raw}\"`);\n  }\n  const argsArray: Array<string | number> = args ?\n    args.split(',').\n      // Onig skips over/ignores redundant/unnecessary commas\n      filter(arg => arg !== '').\n      map(arg => /^[+-]?\\d+$/.test(arg) ? +arg : arg) :\n    [];\n  const [arg0, arg1, arg2] = argsArray;\n  const kind: TokenNamedCalloutKind = CalloutNames.has(name as Uppercase<Exclude<TokenNamedCalloutKind, 'custom'>>) ?\n    name.toLowerCase() as TokenNamedCalloutKind :\n    'custom';\n  switch (kind) {\n    case 'fail':\n    case 'mismatch':\n    case 'skip':\n      if (argsArray.length > 0) {\n        throw new Error(`Named callout arguments not allowed \"${argsArray}\"`);\n      }\n      break;\n    case 'error':\n      if (argsArray.length > 1) {\n        throw new Error(`Named callout allows only one argument \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string') {\n        throw new Error(`Named callout argument must be a number \"${arg0}\"`);\n      }\n      break;\n    case 'max':\n      if (!argsArray.length || argsArray.length > 2) {\n        throw new Error(`Named callout must have one or two arguments \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg0)) {\n        throw new Error(`Named callout argument one must be a tag or number \"${arg0}\"`);\n      }\n      if (argsArray.length === 2 && (typeof arg1 === 'number' || !/^[<>X]$/.test(arg1))) {\n        throw new Error(`Named callout optional argument two must be '<', '>', or 'X' \"${arg1}\"`);\n      }\n      break;\n    case 'count':\n    case 'total_count':\n      if (argsArray.length > 1) {\n        throw new Error(`Named callout allows only one argument \"${argsArray}\"`);\n      }\n      if (argsArray.length === 1 && (typeof arg0 === 'number' || !/^[<>X]$/.test(arg0))) {\n        throw new Error(`Named callout optional argument must be '<', '>', or 'X' \"${arg0}\"`);\n      }\n      break;\n    case 'cmp':\n      if (argsArray.length !== 3) {\n        throw new Error(`Named callout must have three arguments \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg0)) {\n        throw new Error(`Named callout argument one must be a tag or number \"${arg0}\"`);\n      }\n      if (typeof arg1 === 'number' || !/^(?:[<>!=]=|[<>])$/.test(arg1)) {\n        throw new Error(`Named callout argument two must be '==', '!=', '>', '<', '>=', or '<=' \"${arg1}\"`);\n      }\n      if (typeof arg2 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg2)) {\n        throw new Error(`Named callout argument three must be a tag or number \"${arg2}\"`);\n      }\n      break;\n    case 'custom':\n      // TODO: Can support custom callout names via a new option that allows providing a list of\n      // allowed, non-built-in names\n      throw new Error(`Undefined callout name \"${name}\"`);\n    default:\n      throw new Error(`Unexpected named callout kind \"${kind}\"`);\n  }\n  // TODO: If supporting custom callout names in the future (with an added `name` property for\n  // `NamedCalloutToken`), will need to set `name` to `null` if `kind` isn't `'custom'`\n  return createNamedCalloutToken(kind, tag ?? null, args?.split(',') ?? null, raw);\n}\n\nfunction tokenizeQuantifier(raw: string): QuantifierToken {\n  let kind: TokenQuantifierKind = null!;\n  let min: number;\n  let max: number;\n  if (raw[0] === '{') {\n    const {minStr, maxStr} =\n      /^\\{(?<minStr>\\d*)(?:,(?<maxStr>\\d*))?/.exec(raw)!.groups as {minStr: string, maxStr: string | undefined};\n    const limit = 100_000;\n    if (+minStr > limit || (maxStr && +maxStr > limit)) {\n      throw new Error('Quantifier value unsupported in Oniguruma');\n    }\n    min = +minStr;\n    max = maxStr === undefined ? +minStr : (maxStr === '' ? Infinity : +maxStr);\n    // By default, Onig doesn't support making interval quantifiers possessive with a `+` suffix;\n    // uses reversed range instead\n    if (min > max) {\n      kind = 'possessive';\n      [min, max] = [max, min];\n    }\n    if (raw.endsWith('?')) {\n      if (kind === 'possessive') {\n        // TODO: <github.com/slevithan/oniguruma-parser/issues/10>\n        throw new Error('Unsupported possessive interval quantifier chain with \"?\"');\n      }\n      kind = 'lazy';\n    } else if (!kind) {\n      kind = 'greedy';\n    }\n  } else {\n    min = raw[0] === '+' ? 1 : 0;\n    max = raw[0] === '?' ? 1 : Infinity;\n    kind = raw[1] === '+' ? 'possessive' : (raw[1] === '?' ? 'lazy' : 'greedy');\n  }\n  return createQuantifierToken(kind, min, max, raw);\n}\n\nfunction tokenizeShorthand(raw: string): CharacterSetToken {\n  const lower = raw[1].toLowerCase();\n  return createCharacterSetToken({\n    'd': 'digit',\n    'h': 'hex',\n    's': 'space',\n    'w': 'word',\n  }[lower] as TokenCharacterSetKind, raw, {\n    negate: raw[1] !== lower,\n  });\n}\n\nfunction tokenizeUnicodeProperty(raw: string): CharacterSetToken {\n  const {p, neg, value} = /^\\\\(?<p>[pP])\\{(?<neg>\\^?)(?<value>[^}]+)/.exec(raw)!.groups!;\n  const negate = (p === 'P' && !neg) || (p === 'p' && !!neg);\n  return createCharacterSetToken('property', raw, {\n    value,\n    negate,\n  });\n}\n\nfunction getFlagGroupSwitches(flags: string): FlagGroupSwitches | null {\n  // Don't include `false` for flags that aren't included\n  const obj: FlagGroupSwitches = {};\n  if (flags.includes('i')) {\n    obj.ignoreCase = true;\n  }\n  if (flags.includes('m')) {\n    // Onig flag m is equivalent to JS flag s\n    obj.dotAll = true;\n  }\n  if (flags.includes('x')) {\n    obj.extended = true;\n  }\n  return Object.keys(obj).length ? obj : null;\n}\n\nfunction getFlagProperties(flags: string): FlagProperties {\n  const flagProperties: FlagProperties = {\n    ignoreCase: false,\n    dotAll: false,\n    extended: false,\n    digitIsAscii: false,\n    posixIsAscii: false,\n    spaceIsAscii: false,\n    wordIsAscii: false,\n    textSegmentMode: null,\n  };\n  for (let i = 0; i < flags.length; i++) {\n    const char = flags[i];\n    if (!'imxDPSWy'.includes(char)) {\n      throw new Error(`Invalid flag \"${char}\"`);\n    }\n    // Flags y{g}, y{w} are currently only supported via the top-level `flags` option\n    if (char === 'y') {\n      if (!/^y{[gw]}/.test(flags.slice(i))) {\n        throw new Error('Invalid or unspecified flag \"y\" mode');\n      }\n      // If text segment mode flags appear multiple times, use the last one\n      flagProperties.textSegmentMode = flags[i + 2] === 'g' ? 'grapheme' : 'word';\n      i += 3;\n      continue;\n    }\n    flagProperties[{\n      i: 'ignoreCase',\n      // Flag m is called `multiline` in Onig, but that has a different meaning in JS. Onig flag m\n      // is equivalent to JS flag s\n      m: 'dotAll',\n      // Flag x is fully handled during tokenization\n      x: 'extended',\n      // Flags D, P, S, W are currently only supported via the top-level `flags` option\n      D: 'digitIsAscii',\n      P: 'posixIsAscii',\n      S: 'spaceIsAscii',\n      W: 'wordIsAscii',\n    }[char] as Exclude<keyof FlagProperties, 'textSegmentMode'>] = true;\n  }\n  return flagProperties;\n}\n\n// - Unenclosed `\\xNN` above 0x7F is handled elsewhere as a UTF-8 encoded byte sequence\n// - Enclosed `\\x{}` with value above 0x10FFFF is allowed here; handled in the parser\nfunction getValidatedHexCharCode(raw: string): number {\n  // Note: Onig 6.9.10 and earlier have a bug where pattern-terminating `\\u` and `\\x` are treated\n  // as identity escapes; see <github.com/kkos/oniguruma/issues/343>. Don't emulate these bugs.\n  // Additionally, Onig treats bare `\\x` as equivalent to `\\0`, and treats incomplete `\\x{` (with\n  // the brace but not immediately followed by a hex digit) as an identity escape, so e.g. `\\x{`\n  // matches `x{` and `^\\x{,2}$` matches `xx`, but `\\x{2,}` and `\\x{0,2}` are errors. Currently,\n  // this library treats all such cases as errors\n  if (/^(?:\\\\u(?!\\p{AHex}{4})|\\\\x(?!\\p{AHex}{1,2}|\\{\\p{AHex}{1,8}\\}))/u.test(raw)) {\n    throw new Error(`Incomplete or invalid escape \"${raw}\"`);\n  }\n  // Might include leading 0s\n  const hex = raw[2] === '{' ?\n    /^\\\\x\\{\\s*(?<hex>\\p{AHex}+)/u.exec(raw)!.groups!.hex :\n    raw.slice(2);\n  return parseInt(hex, 16);\n}\n\n// Value is 1-3 digits, which can be a backref (possibly invalid), null, octal, or identity escape,\n// possibly followed by 1-2 literal digits\nfunction splitEscapedNumberToken(token: EscapedNumberToken, numCaptures: number): Array<BackreferenceToken> | Array<CharacterToken> {\n  const {raw, inCharClass} = token;\n  // Keep any leading 0s since they indicate octal\n  const value = raw.slice(1);\n  // Backref (possibly invalid)\n  if (\n    !inCharClass &&\n    ( // Single digit 1-9 outside a char class is always treated as a backref\n      (value !== '0' && value.length === 1) ||\n      // Leading 0 makes it octal; backrefs can't include following literal digits\n      (value[0] !== '0' && +value <= numCaptures)\n    )\n  ) {\n    return [createBackreferenceToken(raw)];\n  }\n  const tokens: Array<CharacterToken> = [];\n  // Returns 1-3 matches; the first (only) might be octal\n  const matches = value.match(/^[0-7]+|\\d/g)!;\n  for (let i = 0; i < matches.length; i++) {\n    const m = matches[i];\n    let value: number;\n    // Octal digits are 0-7\n    if (i === 0 && m !== '8' && m !== '9') {\n      value = parseInt(m, 8);\n      if (value > 0o177) {\n        // Octal UTF-8 encoded byte sequence; not yet supported\n        throw new Error(r`Octal encoded byte above 177 unsupported \"${raw}\"`);\n      }\n    } else {\n      value = cpOf(m);\n    }\n    tokens.push(createCharacterToken(value, (i === 0 ? '\\\\' : '') + m));\n  }\n  return tokens;\n}\n\nfunction splitQuantifierMatch(str: string): Array<QuantifierToken> {\n  const tokens: Array<QuantifierToken> = [];\n  // `str` is one or more quantifiers in a chain. It can't be split by a regex because of one edge\n  // case where we have to compare numeric values: although `{1,2}?` is a single, lazy quantifier,\n  // a reversed (possessive) interval quantifier like `{2,1}` can't be both possessive and lazy, so\n  // any following `?`, `??`, or `?+` is a second, chained quantifier (i.e., `{2,1}?` is equivalent\n  // to `{2,1}{0,1}` or `{2,0}`)\n  const withG = new RegExp(quantifierRe, 'gy');\n  let match: RegExpExecArray | null;\n  while ((match = withG.exec(str))) {\n    const m = match[0];\n    if (m[0] === '{') {\n      // Doesn't need to handle fixed `{n}`, infinite max `{n,}`, or implicit zero min `{,n}`\n      // since, according to Onig syntax rules, those can't be possessive\n      const parts = /^\\{(?<min>\\d+),(?<max>\\d+)\\}\\??$/.exec(m);\n      if (parts) {\n        const {min, max} = parts.groups as {min: string, max: string};\n        if (+min > +max && m.endsWith('?')) {\n          // Leave the trailing `?` for the next match\n          withG.lastIndex--;\n          tokens.push(tokenizeQuantifier(m.slice(0, -1)));\n          continue;\n        }\n      }\n    }\n    tokens.push(tokenizeQuantifier(m));\n  }\n  return tokens;\n}\n\nexport {\n  type AlternatorToken,\n  type AssertionToken,\n  type BackreferenceToken,\n  type CharacterToken,\n  type CharacterClassCloseToken,\n  type CharacterClassHyphenToken,\n  type CharacterClassIntersectorToken,\n  type CharacterClassOpenToken,\n  type CharacterSetToken,\n  type DirectiveToken,\n  type FlagGroupModifiers,\n  type FlagProperties,\n  type GroupCloseToken,\n  type GroupOpenToken,\n  type NamedCalloutToken,\n  type QuantifierToken,\n  type SubroutineToken,\n  type Token,\n  type TokenCharacterSetKind,\n  type TokenDirectiveKind,\n  type TokenNamedCalloutKind,\n  type TokenQuantifierKind,\n  tokenize,\n};\n"], "names": ["cpOf", "PosixClassNames", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charClassOpenPattern", "sharedEscapesPattern", "quantifierRe", "tokenRe", "charClassTokenRe", "tokenize", "pattern", "options", "opts", "flagProperties", "getFlagProperties", "xStack", "context", "isXOn", "tokens", "match", "result", "getTokenWithDetails", "potentialUnnamedCaptureTokens", "numNamedAndOptInUnnamedCaptures", "t", "i", "numCaptures", "splitEscapedNumberToken", "m", "lastIndex", "m0", "m1", "getAllTokensForCharClass", "createAssertionToken", "createSubroutineToken", "createBackreferenceToken", "createDirectiveToken", "createCharacterSetToken", "tokenizeSharedEscape", "tokenizeNamedCallout", "tokenizeFlagModifier", "createGroupOpenToken", "createGroupCloseToken", "end", "re", "kind", "createAlternatorToken", "splitQuantifierMatch", "createCharacterToken", "opener", "createCharacterClassOpenToken", "numCharClassesOpen", "createCharacterClassCloseToken", "tokenizeAnyTokenWithinCharClass", "raw", "posix", "createCharacterClassHyphenToken", "createCharacterClassIntersectorToken", "inCharClass", "char1", "tokenizeControlCharacter", "tokenizeS<PERSON>thand", "tokenizeUnicodeProperty", "bytes", "hex", "decoded", "encoder", "char", "byte", "getValidatedHexCharCode", "EscapeCharCodes", "createEscapedNumberToken", "value", "negate", "createNamedCalloutToken", "tag", "args", "createQuantifierToken", "min", "max", "CalloutNames", "on", "off", "enabledFlags", "getFlagGroupSwitches", "disabledFlags", "flagChanges", "callout", "name", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON>", "arg", "arg0", "arg1", "arg2", "tokenizeQuantifier", "minStr", "maxStr", "limit", "lower", "p", "neg", "flags", "obj", "token", "matches", "str", "withG", "parts"], "mappings": ";;;;;AAAA,OAAQ,QAAAA,EAAM,mBAAAC,EAAiB,KAAAC,EAAG,kBAAAC,MAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DvD,MAAMC,QAAuBF,4JAAAA,sBACvBG,IAAuB,8BAM3BH,4JAAAA,uBACF,gBAEEA,MAFF,sJAEEA,uBACF,gBAEEA,QAFF,oJAEEA,uBACF,gBAEEA,YAFF,gJAEEA,cACF,SAAA,KAGA,WADEA,4JAAAA,SACF,eAGMI,IAAe,2CACfC,IAAU,IAAI,OAAOL,gKAAAA,sBAErBG,CAAoB,EAiBjBC,EAAa,MAAM,EACtBF,CAAoB,EAExB,OAAA,CAAQ,QAAQ,EAAE,GAAG,KAAK,GACtBI,IAAmB,IAAI,WAAON,4JAAAA,sBAE9BG,CAAoB,EAIpBD,CAAoB,EAGxB,OAAA,CAAQ,QAAQ,EAAE,GAAG,KAAK;AAoB5B,SAASK,EAASC,CAAAA;QAAiBC,qEAA2B,CAAC,EAG7D;IACA,MAAMC,IAAO;QACX,OAAO;QACP,GAAGD,CAAAA;QACH,OAAO;YACL,cAAc,CAAA;YACd,YAAY,CAAA;YACZ,GAAGA,EAAQ,KACb;;IACF;IACA,IAAI,OAAOD,KAAY,UACrB,MAAM,IAAI,MAAM,4BAA4B;IAE9C,MAAMG,IAAiBC,EAAkBF,EAAK,KAAK,GAC7CG,IAAS;QAACF,EAAe,QAAQ;KAAA,EACjCG,IAAmB;QACvB,cAAcJ,EAAK,KAAA,CAAM,YAAA;QAEzB,gBAAiB;YAAC,OAAOG,EAAO,EAAA,CAAG,CAAA,CAAE;QAAE;QACvC,eAAe;QACf,SAAU;YAACA,EAAO,GAAA,CAAI;QAAC;QACvB,UAASE,CAAAA,CAAO;YAACF,EAAO,IAAA,CAAKE,CAAK;QAAC;QACnC,oBAAmBA,CAAAA,CAAO;YAACF,CAAAA,CAAOA,EAAO,MAAA,GAAS,CAAC,CAAA,GAAIE;QAAK;QAC5D,YAAYL,EAAK,KAAA,CAAM;IACzB;IACA,IAAIM,IAA2C,CAAC,CAAA,EAC5CC;IAEJ,IADAZ,EAAQ,SAAA,GAAY,GACZY,IAAQZ,EAAQ,IAAA,CAAKG,CAAO,GAAI;QACtC,MAAMU,IAASC,EAAoBL,GAASN,GAASS,CAAAA,CAAM,CAAC,CAAA,EAAGZ,EAAQ,SAAS;QAC5Ea,EAAO,MAAA,GACTF,EAAO,IAAA,CAAK,GAAGE,EAAO,MAAM,IACnBA,EAAO,KAAA,IAChBF,EAAO,IAAA,CAAKE,EAAO,KAAK,GAEtBA,EAAO,SAAA,KAAc,KAAA,KAAA,CACvBb,EAAQ,SAAA,GAAYa,EAAO,SAAA;IAE/B;IAEA,MAAME,IAAuD,CAAC,CAAA;IAC9D,IAAIC,IAAkC;IACtCL,EAAO,MAAA,EAAOM,IAAKA,EAAE,IAAA,KAAS,WAAW,EAAE,OAAA,CAAQA,GAAK;QAClDA,EAAE,IAAA,KAAS,cACbA,EAAE,MAAA,GAAS,EAAED,IACJC,EAAE,GAAA,KAAQ,OACnBF,EAA8B,IAAA,CAAKE,CAAC;IAExC,CAAC,GAEID,KACHD,EAA8B,OAAA,CAAQ,CAACE,GAAGC,IAAM;QAC9CD,EAAE,IAAA,GAAO,aACTA,EAAE,MAAA,GAASC,IAAI;IACjB,CAAC;IAEH,MAAMC,IAAcH,KAAmCD,EAA8B,MAAA;IAMrF,OAAO;QACL,QALgCJ,EAAO,GAAA,CACvCM,KAAKA,EAAE,IAAA,KAAS,kBAAkBG,GAAwBH,GAAGE,CAAW,IAAIF,CAC9E,EAAE,IAAA,CAAK;QAIL,OAAOX;IACT;AACF;AAEA,SAASQ,EAAoBL,CAAAA,EAAkBN,CAAAA,EAAiBkB,CAAAA,EAAWC,CAAAA,CAYzE;IACA,MAAM,CAACC,GAAIC,CAAE,CAAA,GAAIH;IAEjB,IAAIA,MAAM,OAAOA,MAAM,MAAM;QAC3B,MAAMR,IAASY,EAAyBtB,GAASkB,GAAGC,CAAS;QAC7D,OAAO;YAEL,QAAQT,EAAO,MAAA;YAEf,WAAWA,EAAO;QACpB;IACF;IAEA,IAAIU,MAAO,MAAM;QACf,IAAI,WAAW,QAAA,CAASC,CAAE,GACxB,OAAO;YACL,OAAOE,EAAqBL,GAAGA,CAAC;QAClC;QAEF,IAAI,WAAW,IAAA,CAAKA,CAAC,GAAG;YACtB,IAAI,CAAC,2BAA2B,IAAA,CAAKA,CAAC,GACpC,MAAM,IAAI,MAAM,uBAAwB,OAADA,CAAC,EAAA,EAAG;YAE7C,OAAO;gBACL,OAAOM,EAAsBN,CAAC;YAChC;QACF;QACA,IAAI,WAAW,IAAA,CAAKA,CAAC,GAAG;YACtB,IAAI,CAAC,2BAA2B,IAAA,CAAKA,CAAC,GACpC,MAAM,IAAI,MAAM,uBAAwB,OAADA,CAAC,EAAA,EAAG;YAE7C,OAAO;gBACL,OAAOO,EAAyBP,CAAC;YACnC;QACF;QACA,IAAIG,MAAO,KACT,OAAO;YACL,OAAOK,EAAqB,QAAQR,CAAC;QACvC;QAEF,IAAIG,MAAO,OAAOA,MAAO,KACvB,OAAO;YACL,OAAOM,EAAwB,WAAWT,GAAG;gBAE3C,QAAQG,MAAO;YACjB,CAAC;QACH;QAEF,IAAIA,MAAO,KACT,OAAO;YACL,OAAOM,EAAwB,OAAOT,CAAC;QACzC;QAEF,IAAIG,MAAO,KACT,OAAO;YACL,OAAOM,EAAwB,gBAAgBT,CAAC;QAClD;QAGF,MAAMR,IAASkB,EAAqBV,GAAG;YAAC,aAAa,CAAA;QAAK,CAAC;QAC3D,OAAO,MAAM,OAAA,CAAQR,CAAM,IAAI;YAAC,QAAQA;QAAM,IAAI;YAAC,OAAOA;QAAM;IAClE;IAEA,IAAIU,MAAO,KAAK;QACd,IAAIC,MAAO,KACT,OAAO;YACL,OAAOQ,EAAqBX,CAAC;QAC/B;QAEF,IAAIA,MAAM,OACR,MAAM,IAAI,MAAM,wBAAyB,OAADA,CAAC,EAAA,EAAG;QAG9C,IAAIA,EAAE,UAAA,CAAW,KAAK,GAAG;YAEvB,IAAIlB,CAAAA,CAAQmB,CAAS,CAAA,KAAM,KACzB,MAAM,IAAI,MAAM,8BAA8B;YAEhD,OAAO;gBAEL,WAAWA,IAAY;YACzB;QACF;QAEA,IAAI,oBAAoB,IAAA,CAAKD,CAAC,GAC5B,OAAO;YACL,OAAOY,EAAqBZ,GAAGZ,CAAO;QACxC;QAKF,IAFAA,EAAQ,QAAA,CAASA,EAAQ,cAAA,CAAe,CAAC,GACzCA,EAAQ,aAAA,IAILY,MAAM,OAAO,CAACZ,EAAQ,YAAA,IAEvBY,MAAM,OAEN,OAAO;YAEL,OAAOa,EAAqB,SAASb,CAAC;QACxC;QAGF,IAAIA,MAAM,OACR,OAAO;YACL,OAAOa,EAAqB,UAAUb,CAAC;QACzC;QAGF,IAAIA,MAAM,SAASA,MAAM,SAASA,MAAM,UAAUA,MAAM,QACtD,OAAO;YACL,OAAOa,EAAqBb,CAAAA,CAAE,CAAC,CAAA,KAAM,MAAM,eAAe,aAAaA,GAAG;gBACxE,QAAQA,EAAE,QAAA,CAAS,GAAG;YACxB,CAAC;QACH;QAIF,IACGA,MAAM,OAAOZ,EAAQ,YAAA,IACrBY,EAAE,UAAA,CAAW,KAAK,KAAKA,EAAE,QAAA,CAAS,GAAG,KACrCA,EAAE,UAAA,CAAW,KAAK,KAAKA,EAAE,QAAA,CAAS,GAAG,GAEtC,OAAO;YACL,OAAOa,EAAqB,aAAab,GAAG;gBAE1C,GAAIA,MAAM,OAAO;oBAAC,MAAMA,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE;gBAAC;YACxC,CAAC;QACH;QAEF,IAAIA,EAAE,UAAA,CAAW,KAAK,GAAG;YACvB,IAAIA,MAAM,QACR,MAAM,IAAI,MAAM,sCAAuC,OAADA,CAAC,EAAA,EAAG;YAE5D,OAAO;gBACL,OAAOa,EAAqB,oBAAoBb,CAAC;YACnD;QACF;QACA,MAAIA,MAAM,QAEF,IAAI,MAAM,4BAA6B,OAADA,CAAC,EAAA,EAAG,MAE5C,IAAI,MAAM,wCAAyC,EAAG,KAAJA,CAAC,EAAA;IAC3D;IACA,IAAIA,MAAM,KAAK;QAGb,IAFAZ,EAAQ,OAAA,CAAQ,GAChBA,EAAQ,aAAA,IACJA,EAAQ,aAAA,GAAgB,GAC1B,MAAM,IAAI,MAAM,eAAe;QAEjC,OAAO;YACL,OAAO0B,EAAsBd,CAAC;QAChC;IACF;IAEA,IAAIZ,EAAQ,cAAA,CAAe,GAAG;QAC5B,IAAIY,MAAM,KAAK;YAEb,MAAMe,IAAMjC,EAAQ,OAAA,CAAQ,MAAMmB,CAAS;YAC3C,OAAO;gBAEL,WAAWc,MAAQ,CAAA,IAAKjC,EAAQ,MAAA,GAASiC;YAC3C;QACF;QACA,IAAI,OAAO,IAAA,CAAKf,CAAC,GAAG;YAClB,MAAMgB,IAAK;YACX,OAAAA,EAAG,SAAA,GAAYf,GAER;gBAEL,WAHWe,EAAG,IAAA,CAAKlC,CAAO,IAGRkC,EAAG,SAAA,GAAYf;YACnC;QACF;IACF;IAEA,IAAID,MAAM,KACR,OAAO;QACL,OAAOS,EAAwB,OAAOT,CAAC;IACzC;IAGF,IAAIA,MAAM,OAAOA,MAAM,KAAK;QAC1B,MAAMiB,IAAO7B,EAAQ,UAAA,IAAa;YAChC,GAAA,MAAKd,4JAAAA;YACL,CAAA,MAAKA,4JAAAA;QACP,EAAA,CAAE0B,CAAC,CAAA,GAAIA;QACP,OAAO;YACL,OAAOK,EAAqBY,GAAMjB,CAAC;QACrC;IACF;IAEA,OAAIA,MAAM,MACD;QACL,OAAOkB,EAAsBlB,CAAC;IAChC,IAGEtB,EAAa,IAAA,CAAKsB,CAAC,IACd;QACL,QAAQmB,GAAqBnB,CAAC;IAChC,IAIK;QACL,OAAOoB,MAAqBhD,+JAAAA,EAAK4B,CAAC,GAAGA,CAAC;IACxC;AACF;AAEA,SAASI,EAAyBtB,CAAAA,EAAiBuC,CAAAA,EAA8BpB,CAAAA,CAG/E;IACA,MAAMX,IAA2C;QAACgC,EAA8BD,CAAAA,CAAO,CAAC,CAAA,KAAM,KAAKA,CAAM,CAAC;KAAA;IAC1G,IAAIE,IAAqB,GACrBhC;IAEJ,IADAX,EAAiB,SAAA,GAAYqB,GACrBV,IAAQX,EAAiB,IAAA,CAAKE,CAAO,GAAI;QAC/C,MAAMkB,IAAIT,CAAAA,CAAM,CAAC,CAAA;QAGjB,IAAIS,CAAAA,CAAE,CAAC,CAAA,KAAM,OAAOA,CAAAA,CAAE,CAAC,CAAA,KAAM,KAC3BuB,KACAjC,EAAO,IAAA,CAAKgC,EAA8BtB,CAAAA,CAAE,CAAC,CAAA,KAAM,KAAKA,CAAyB,CAAC;aAAA,IACzEA,MAAM,KAAA;YAEf,IAAIV,EAAO,EAAA,CAAG,CAAA,CAAE,EAAG,IAAA,KAAS,sBAE1BA,EAAO,IAAA,CAAK8B,EAAqB,IAAIpB,CAAC,CAAC;iBAAA,IAEvCuB,KACAjC,EAAO,IAAA,CAAKkC,EAA+BxB,CAAC,CAAC,GACzC,CAACuB,GACH;QAAA,OAGC;YACL,MAAM/B,IAASiC,EAAgCzB,CAAC;YAC5C,MAAM,OAAA,CAAQR,CAAM,IACtBF,EAAO,IAAA,CAAK,GAAGE,CAAM,IAErBF,EAAO,IAAA,CAAKE,CAAM;QAEtB;IACF;IACA,OAAO;QACL,QAAAF;QACA,WAAWV,EAAiB,SAAA,IAAaE,EAAQ;IACnD;AACF;AAEA,SAAS2C,EAAgCC,CAAAA,CAAuD;IAC9F,IAAIA,CAAAA,CAAI,CAAC,CAAA,KAAM,MAEb,OAAOhB,EAAqBgB,GAAK;QAAC,aAAa,CAAA;IAAI,CAAC;IAGtD,IAAIA,CAAAA,CAAI,CAAC,CAAA,KAAM,KAAK;QAClB,MAAMC,IAAQ,iDAAsC,IAAA,CAAKD,CAAG;QAC5D,IAAI,CAACC,KAAS,CAACtD,0KAAAA,CAAgB,GAAA,CAAIsD,EAAM,MAAA,CAAQ,IAAI,GACnD,MAAM,IAAI,MAAM,wBAA2B,OAAHD,CAAG,EAAA,EAAG;QAEhD,OAAOjB,EAAwB,SAASiB,GAAK;YAC3C,OAAOC,EAAM,MAAA,CAAQ,IAAA;YACrB,QAAQ,CAAC,CAACA,EAAM,MAAA,CAAQ;QAC1B,CAAC;IACH;IAEA,OAAID,MAAQ,MACHE,EAAgCF,CAAG,IAExCA,MAAQ,OACHG,EAAqCH,CAAG,IAG1CN,MAAqBhD,+JAAAA,EAAKsD,CAAG,GAAGA,CAAG;AAC5C;AAGA,SAAShB,EAAqBgB,CAAAA,OAAyB;UAAX,aAAAI,CAAW,EAAA,CAAqE,EAAjF;IACzC,MAAMC,IAAQL,CAAAA,CAAI,CAAC,CAAA;IACnB,IAAIK,MAAU,OAAOA,MAAU,KAC7B,OAAOC,EAAyBN,CAAG;IAErC,IAAI,WAAW,QAAA,CAASK,CAAK,GAC3B,OAAOE,EAAkBP,CAAG;IAE9B,IAAIA,EAAI,UAAA,KAAWpD,4JAAAA,KAAM,oBACvB,MAAM,IAAI,MAAM,yDAA4D,EAAG,KAANoD,CAAG,EAAA;IAE9E,IAAI,YAAY,IAAA,CAAKA,CAAG,GAAG;QACzB,IAAIA,EAAI,MAAA,KAAW,GACjB,MAAM,IAAI,MAAM,2CAA8C,EAAG,KAANA,CAAG,EAAA;QAEhE,OAAOQ,EAAwBR,CAAG;IACpC;IAEA,IAAI,yCAA0B,IAAA,CAAKA,CAAG,GACpC,IAAI;QACF,MAAMS,IAAQT,EAAI,KAAA,CAAM,KAAK,EAAE,KAAA,CAAM,CAAC,EAAE,GAAA,EAAIU,IAAO,SAASA,GAAK,EAAE,CAAC,GAC9DC,IAAU,IAAI,YAAY,SAAS;YACvC,WAAW,CAAA;YACX,OAAO,CAAA;QACT,CAAC,EAAE,MAAA,CAAO,IAAI,WAAWF,CAAK,CAAC,GACzBG,IAAU,IAAI;QAMpB,OALe,CAAC;eAAGD,CAAO;SAAA,CAAE,GAAA,EAAIE,GAAQ;YAEtC,MAAMb,IAAM,CAAC;mBAAGY,EAAQ,MAAA,CAAOC,CAAI,CAAC;aAAA,CAAE,GAAA,EAAIC,IAAQ,MAAuB,CAAE,MAAnBA,EAAK,QAAA,CAAS,EAAE,CAAC,GAAI,IAAA,CAAK,EAAE;YACpF,OAAOpB,MAAqBhD,+JAAAA,EAAKmE,CAAI,GAAGb,CAAG;QAC7C,CAAC;IAEH,EAAA,WAAQ;QACN,MAAM,IAAI,MAAM,mBAAsB,OAAHA,CAAG,EAAA,qCAAsC;IAC9E;IAEF,IAAIK,MAAU,OAAOA,MAAU,KAC7B,OAAOX,EAAqBqB,EAAwBf,CAAG,GAAGA,CAAG;IAE/D,IAAIgB,EAAgB,GAAA,CAAIX,CAAK,GAC3B,OAAOX,EAAqBsB,EAAgB,GAAA,CAAIX,CAAK,GAAIL,CAAG;IAI9D,IAAI,KAAK,IAAA,CAAKK,CAAK,GACjB,OAAOY,EAAyBb,GAAaJ,CAAG;IAElD,IAAIA,MAAQ,MACV,MAAM,IAAI,UAAMpD,4JAAAA,uBAAwB;IAG1C,IAAIyD,MAAU,KAKZ,MAAM,IAAI,MAAM,qBAAwB,OAAHL,CAAG,EAAA,EAAG;IAG7C,IAAI,CAAC;WAAGA,CAAG;KAAA,CAAE,MAAA,KAAW,GACtB,OAAON,EAAqBM,EAAI,WAAA,CAAY,CAAC,GAAIA,CAAG;IAEtD,MAAM,IAAI,MAAM,sBAAyB,EAAG,KAANA,CAAG,EAAA;AAC3C;AAUA,SAASR,EAAsBQ,CAAAA,CAA2B;IACxD,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAOA,SAASrB,EAAqBY,CAAAA,EAAcS,CAAAA,CAA6B;IACvE,OAAO;QACL,MAAM;QACN,MAAAT;QACA,KAAAS;IACF;AACF;AAMA,SAASnB,EAAyBmB,CAAAA,CAAiC;IACjE,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAOA,SAASN,EAAqBwB,CAAAA,EAAelB,CAAAA,CAA6B;IACxE,OAAO;QACL,MAAM;QACN,OAAAkB;QACA,KAAAlB;IACF;AACF;AAMA,SAASF,EAA+BE,CAAAA,CAAoC;IAC1E,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAMA,SAASE,EAAgCF,CAAAA,CAAqC;IAC5E,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAMA,SAASG,EAAqCH,CAAAA,CAA2C;IACvF,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAQA,SAASJ,EAA8BuB,CAAAA,EAAiBnB,CAAAA,CAAoD;IAC1G,OAAO;QACL,MAAM;QACN,QAAAmB;QACA,KAAAnB;IACF;AACF;AASA,SAASjB,EACPQ,CAAAA,EACAS,CAAAA;YACA3C,iEAGI,CAAC,EACc;IACnB,OAAO;QACL,MAAM;QACN,MAAAkC;QACA,GAAGlC,CAAAA;QACH,KAAA2C;IACF;AACF;AAYA,SAASlB,EAAqBS,CAAAA,EAA0BS,CAAAA;YAAa3C,iEAAwC,CAAC,EAAmB;IAC/H,OAAIkC,MAAS,SACJ;QACL,MAAM;QACN,MAAAA;QACA,KAAAS;IACF,IAEK;QACL,MAAM;QACN,MAAAT;QACA,WAAO1C,yKAAAA,EAAeQ,EAAQ,KAAK;QACnC,KAAA2C;IACF;AACF;AAUA,SAASiB,EAAyBb,CAAAA,EAAsBJ,CAAAA,CAAiC;IACvF,OAAO;QACL,MAAM;QACN,aAAAI;QACA,KAAAJ;IACF;AACF;AAMA,SAASZ,EAAsBY,CAAAA,CAA2B;IACxD,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAWA,SAASb,EACPI,CAAAA,EACAS,CAAAA;YACA3C,iEAKI,CAAC,EACW;IAChB,OAAO;QACL,MAAM;QACN,MAAAkC;QACA,GAAGlC,CAAAA;QACH,KAAA2C;IACF;AACF;AASA,SAASoB,EACP7B,CAAAA,EACA8B,CAAAA,EACAC,CAAAA,EACAtB,CAAAA,CACmB;IACnB,OAAO;QACL,MAAM;QACN,MAAAT;QACA,KAAA8B;QACA,WAAWC;QACX,KAAAtB;IACF;AACF;AASA,SAASuB,EACPhC,CAAAA,EACAiC,CAAAA,EACAC,CAAAA,EACAzB,CAAAA,CACiB;IACjB,OAAO;QACL,MAAM;QACN,MAAAT;QACA,KAAAiC;QACA,KAAAC;QACA,KAAAzB;IACF;AACF;AAMA,SAASpB,EAAsBoB,CAAAA,CAA8B;IAC3D,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AA4BA,MAAM0B,IAAe,IAAI,IAAyD;IAChF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aACF;CAAC,GAEKV,IAAkB,IAAI,IAAI;IAC9B;QAAC;QAAM,CAAC;KAAA;IACR;QAAC;QAAM,CAAC;KAAA;IACR;QAAC;QAAK,EAAE;KAAA;IACR;QAAC;QAAK,EAAE;KAAA;IACR;QAAC;QAAK,EAAE;KAAA;IACR;QAAC;QAAK,EAAE;KAAA;IACR;QAAC;QAAM,CAAC;KAAA;IACR;QAAC;QAAK,EAAE;KACV;CAAC;AAGD,SAASV,EAAyBN,CAAAA,CAA6B;IAC7D,MAAMa,IAAOb,CAAAA,CAAI,CAAC,CAAA,KAAM,MAAMA,CAAAA,CAAI,CAAC,CAAA,GAAIA,CAAAA,CAAI,CAAC,CAAA;IAC5C,IAAI,CAACa,KAAQ,CAAC,WAAW,IAAA,CAAKA,CAAI,GAKhC,MAAM,IAAI,MAAM,kCAAqC,OAAHb,CAAG,EAAA,EAAG;IAE1D,OAAON,MAAqBhD,+JAAAA,EAAKmE,EAAK,WAAA,CAAY,CAAC,IAAI,IAAIb,CAAG;AAChE;AAEA,SAASd,EAAqBc,CAAAA,EAAatC,CAAAA,CAAmD;IAE5F,IAAI,EAAC,IAAAiE,CAAAA,EAAI,KAAAC,CAAG,EAAA,GAAI,oDAA0C,IAAA,CAAK5B,CAAG,EAAG,MAAA;qCACrE4B,IAAQ;IAER,MAAMjE,IAAAA,CAASD,EAAQ,cAAA,CAAe,KAAKiE,EAAG,QAAA,CAAS,GAAG,CAAA,KAAM,CAACC,EAAI,QAAA,CAAS,GAAG,GAC3EC,IAAeC,EAAqBH,CAAE,GACtCI,IAAgBD,EAAqBF,CAAG,GACxCI,IAAkC,CAAC;IAIzC,IAHAH,KAAAA,CAAiBG,EAAY,MAAA,GAASH,CAAAA,GACtCE,KAAAA,CAAkBC,EAAY,OAAA,GAAUD,CAAAA,GAEpC/B,EAAI,QAAA,CAAS,GAAG,GAElB,OAAAtC,EAAQ,kBAAA,CAAmBC,CAAK,GAEzBmB,EAAqB,SAASkB,GAAK;QACxC,OAAOgC;IACT,CAAC;IAGH,IAAIhC,EAAI,QAAA,CAAS,GAAG,GAClB,OAAAtC,EAAQ,QAAA,CAASC,CAAK,GACtBD,EAAQ,aAAA,IACDyB,EAAqB,SAASa,GAAK;QACxC,GAAA,CAAK6B,KAAgBE,CAAAA,KAAkB;YAAC,OAAOC;QAAW,CAC5D,CAAC;;IAEH,MAAM,IAAI,MAAM,6BAAgC,OAAHhC,CAAG,EAAA,EAAG;AACrD;AAEA,SAASf,EAAqBe,CAAAA,CAAgC;IAC5D,MAAMiC,IAAU,yGAAwF,IAAA,CAAKjC,CAAG;IAChH,IAAI,CAACiC,GACH,MAAM,IAAI,MAAM,wCAA2C,OAAHjC,CAAG,EAAA,EAAG;IAEhE,MAAM,EAAC,MAAAkC,CAAAA,EAAM,KAAAb,CAAAA,EAAK,MAAAC,CAAI,EAAA,GAAIW,EAAQ,MAAA;IAKlC,IAAI,CAACC,GACH,MAAM,IAAI,MAAM,0BAA6B,OAAHlC,CAAG,EAAA,EAAG;IAElD,IAAIqB,MAAQ,IACV,MAAM,IAAI,MAAM,mDAAsD,EAAG,KAANrB,CAAG,EAAA;IAExE,MAAMmC,IAAoCb,IACxCA,EAAK,KAAA,CAAM,GAAG,EAEZ,MAAA,EAAOc,IAAOA,MAAQ,EAAE,EACxB,GAAA,EAAIA,IAAO,aAAa,IAAA,CAAKA,CAAG,IAAI,CAACA,IAAMA,CAAG,IAChD,CAAC,CAAA,EACG,CAACC,GAAMC,GAAMC,CAAI,CAAA,GAAIJ,GACrB5C,IAA8BmC,EAAa,GAAA,CAAIQ,CAA2D,IAC9GA,EAAK,WAAA,CAAY,IACjB;IACF,OAAQ3C,EAAM;QACZ,KAAK;QACL,KAAK;QACL,KAAK;YACH,IAAI4C,EAAU,MAAA,GAAS,GACrB,MAAM,IAAI,MAAM,wCAAiD,OAATA,CAAS,EAAA,EAAG;YAEtE;QACF,KAAK;YACH,IAAIA,EAAU,MAAA,GAAS,GACrB,MAAM,IAAI,MAAM,2CAAoD,OAATA,CAAS,EAAA,EAAG;YAEzE,IAAI,OAAOE,KAAS,UAClB,MAAM,IAAI,MAAM,4CAAgD,EAAG,KAAPA,CAAI,EAAA;YAElE;QACF,KAAK;YACH,IAAI,CAACF,EAAU,MAAA,IAAUA,EAAU,MAAA,GAAS,GAC1C,MAAM,IAAI,MAAM,iDAA0D,OAATA,CAAS,EAAA,EAAG;YAE/E,IAAI,OAAOE,KAAS,YAAY,CAAC,iBAAiB,IAAA,CAAKA,CAAI,GACzD,MAAM,IAAI,MAAM,uDAA2D,OAAJA,CAAI,EAAA,EAAG;YAEhF,IAAIF,EAAU,MAAA,KAAW,KAAA,CAAM,OAAOG,KAAS,YAAY,CAAC,UAAU,IAAA,CAAKA,CAAI,CAAA,GAC7E,MAAM,IAAI,MAAM,kEAAqE,OAAJA,CAAI,EAAA,EAAG;YAE1F;QACF,KAAK;QACL,KAAK;YACH,IAAIH,EAAU,MAAA,GAAS,GACrB,MAAM,IAAI,MAAM,2CAAoD,OAATA,CAAS,EAAA,EAAG;YAEzE,IAAIA,EAAU,MAAA,KAAW,KAAA,CAAM,OAAOE,KAAS,YAAY,CAAC,UAAU,IAAA,CAAKA,CAAI,CAAA,GAC7E,MAAM,IAAI,MAAM,8DAAiE,EAAG,KAAPA,CAAI,EAAA;YAEnF;QACF,KAAK;YACH,IAAIF,EAAU,MAAA,KAAW,GACvB,MAAM,IAAI,MAAM,4CAAqD,OAATA,CAAS,EAAA,EAAG;YAE1E,IAAI,OAAOE,KAAS,YAAY,CAAC,iBAAiB,IAAA,CAAKA,CAAI,GACzD,MAAM,IAAI,MAAM,uDAA2D,OAAJA,CAAI,EAAA,EAAG;YAEhF,IAAI,OAAOC,KAAS,YAAY,CAAC,qBAAqB,IAAA,CAAKA,CAAI,GAC7D,MAAM,IAAI,MAAM,4EAA+E,OAAJA,CAAI,EAAA,EAAG;YAEpG,IAAI,OAAOC,KAAS,YAAY,CAAC,iBAAiB,IAAA,CAAKA,CAAI,GACzD,MAAM,IAAI,MAAM,yDAA6D,OAAJA,CAAI,EAAA,EAAG;YAElF;QACF,KAAK;YAGH,MAAM,IAAI,MAAM,2BAA+B,OAAJL,CAAI,EAAA,EAAG;QACpD;YACE,MAAM,IAAI,MAAM,kCAAsC,OAAJ3C,CAAI,EAAA,EAAG;IAC7D;QAGkD+B;IAAlD,OAAOF,EAAwB7B,GAAM8B,iCAAO,0DAAY,KAAA,CAAM,GAAG,gDAAK,MAAMrB,CAAG;AACjF;AAEA,SAASwC,EAAmBxC,CAAAA,CAA8B;IACxD,IAAIT,IAA4B,MAC5BiC,GACAC;IACJ,IAAIzB,CAAAA,CAAI,CAAC,CAAA,KAAM,KAAK;QAClB,MAAM,EAAC,QAAAyC,CAAAA,EAAQ,QAAAC,CAAM,EAAA,GACnB,mDAAwC,IAAA,CAAK1C,CAAG,EAAG,MAAA,EAC/C2C,IAAQ;QACd,IAAI,CAACF,IAASE,KAAUD,KAAU,CAACA,IAASC,GAC1C,MAAM,IAAI,MAAM,2CAA2C;QAU7D,IARAnB,IAAM,CAACiB,GACPhB,IAAMiB,MAAW,KAAA,IAAY,CAACD,IAAUC,MAAW,KAAK,IAAA,IAAW,CAACA,GAGhElB,IAAMC,KAAAA,CACRlC,IAAO,cACP,CAACiC,GAAKC,CAAG,CAAA,GAAI;YAACA;YAAKD,CAAG;SAAA,GAEpBxB,EAAI,QAAA,CAAS,GAAG,GAAG;YACrB,IAAIT,MAAS,cAEX,MAAM,IAAI,MAAM,2DAA2D;YAE7EA,IAAO;QACT,OAAYA,KAAAA,CACVA,IAAO,QAAA;IAEX,OACEiC,IAAMxB,CAAAA,CAAI,CAAC,CAAA,KAAM,MAAM,IAAI,GAC3ByB,IAAMzB,CAAAA,CAAI,CAAC,CAAA,KAAM,MAAM,IAAI,IAAA,GAC3BT,IAAOS,CAAAA,CAAI,CAAC,CAAA,KAAM,MAAM,eAAgBA,CAAAA,CAAI,CAAC,CAAA,KAAM,MAAM,SAAS;IAEpE,OAAOuB,EAAsBhC,GAAMiC,GAAKC,GAAKzB,CAAG;AAClD;AAEA,SAASO,EAAkBP,CAAAA,CAAgC;IACzD,MAAM4C,IAAQ5C,CAAAA,CAAI,CAAC,CAAA,CAAE,WAAA,CAAY;IACjC,OAAOjB,EAAwB;QAC7B,GAAK;QACL,GAAK;QACL,GAAK;QACL,GAAK;IACP,CAAA,CAAE6D,CAAK,CAAA,EAA4B5C,GAAK;QACtC,QAAQA,CAAAA,CAAI,CAAC,CAAA,KAAM4C;IACrB,CAAC;AACH;AAEA,SAASpC,EAAwBR,CAAAA,CAAgC;IAC/D,MAAM,EAAC,GAAA6C,CAAAA,EAAG,KAAAC,CAAAA,EAAK,OAAA5B,CAAK,EAAA,GAAI,wDAA4C,IAAA,CAAKlB,CAAG,EAAG,MAAA;IAE/E,OAAOjB,EAAwB,YAAYiB,GAAK;QAC9C,OAAAkB;QACA,QAHc2B,MAAM,OAAO,CAACC,KAASD,MAAM,OAAO,CAAC,CAACC;IAItD,CAAC;AACH;AAEA,SAAShB,EAAqBiB,CAAAA,CAAyC;IAErE,MAAMC,IAAyB,CAAC;IAChC,OAAID,EAAM,QAAA,CAAS,GAAG,KAAA,CACpBC,EAAI,UAAA,GAAa,CAAA,CAAA,GAEfD,EAAM,QAAA,CAAS,GAAG,KAAA,CAEpBC,EAAI,MAAA,GAAS,CAAA,CAAA,GAEXD,EAAM,QAAA,CAAS,GAAG,KAAA,CACpBC,EAAI,QAAA,GAAW,CAAA,CAAA,GAEV,OAAO,IAAA,CAAKA,CAAG,EAAE,MAAA,GAASA,IAAM;AACzC;AAEA,SAASxF,EAAkBuF,CAAAA,CAA+B;IACxD,MAAMxF,IAAiC;QACrC,YAAY,CAAA;QACZ,QAAQ,CAAA;QACR,UAAU,CAAA;QACV,cAAc,CAAA;QACd,cAAc,CAAA;QACd,cAAc,CAAA;QACd,aAAa,CAAA;QACb,iBAAiB;IACnB;IACA,IAAA,IAASY,IAAI,GAAGA,IAAI4E,EAAM,MAAA,EAAQ5E,IAAK;QACrC,MAAM0C,IAAOkC,CAAAA,CAAM5E,CAAC,CAAA;QACpB,IAAI,CAAC,WAAW,QAAA,CAAS0C,CAAI,GAC3B,MAAM,IAAI,MAAM,iBAAqB,EAAG,KAAPA,CAAI,EAAA;QAGvC,IAAIA,MAAS,KAAK;YAChB,IAAI,CAAC,WAAW,IAAA,CAAKkC,EAAM,KAAA,CAAM5E,CAAC,CAAC,GACjC,MAAM,IAAI,MAAM,sCAAsC;YAGxDZ,EAAe,eAAA,GAAkBwF,CAAAA,CAAM5E,IAAI,CAAC,CAAA,KAAM,MAAM,aAAa,QACrEA,KAAK;YACL;QACF;QACAZ,CAAAA,EAAe;YACb,GAAG;YAGH,GAAG;YAEH,GAAG;YAEH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;SACL,CAAA,CAAEsD,CAAI,CAAqD,CAAA,GAAI,CAAA;IACjE;IACA,OAAOtD;AACT;AAIA,SAASwD,EAAwBf,CAAAA,CAAqB;IAOpD,IAAI,uFAAkE,IAAA,CAAKA,CAAG,GAC5E,MAAM,IAAI,MAAM,iCAAoC,OAAHA,CAAG,EAAA,EAAG;IAGzD,MAAMU,IAAMV,CAAAA,CAAI,CAAC,CAAA,KAAM,MACrB,+CAA8B,IAAA,CAAKA,CAAG,EAAG,MAAA,CAAQ,GAAA,GACjDA,EAAI,KAAA,CAAM,CAAC;IACb,OAAO,SAASU,GAAK,EAAE;AACzB;AAIA,SAASrC,GAAwB4E,CAAAA,EAA2B7E,CAAAA,CAAwE;IAClI,MAAM,EAAC,KAAA4B,CAAAA,EAAK,aAAAI,CAAW,EAAA,GAAI6C,GAErB/B,IAAQlB,EAAI,KAAA,CAAM,CAAC;IAEzB,IACE,CAACI,KAAAA,CAEEc,MAAU,OAAOA,EAAM,MAAA,KAAW,KAElCA,CAAAA,CAAM,CAAC,CAAA,KAAM,OAAO,CAACA,KAAS9C,CAAAA,GAGjC,OAAO;QAACS,EAAyBmB,CAAG,CAAC;KAAA;IAEvC,MAAMpC,IAAgC,CAAC,CAAA,EAEjCsF,IAAUhC,EAAM,KAAA,CAAM,aAAa;IACzC,IAAA,IAAS,IAAI,GAAG,IAAIgC,EAAQ,MAAA,EAAQ,IAAK;QACvC,MAAM5E,IAAI4E,CAAAA,CAAQ,CAAC,CAAA;QACnB,IAAIhC;QAEJ,IAAI,MAAM,KAAK5C,MAAM,OAAOA,MAAM,KAAA;YAEhC,IADA4C,IAAQ,SAAS5C,GAAG,CAAC,GACjB4C,IAAQ,KAEV,MAAM,IAAI,UAAMtE,4JAAAA,uBAA8CoD,CAAG,GAAG;QAAA,OAGtEkB,QAAQxE,+JAAAA,EAAK4B,CAAC;QAEhBV,EAAO,IAAA,CAAK8B,EAAqBwB,GAAAA,CAAQ,MAAM,IAAI,OAAO,EAAA,IAAM5C,CAAC,CAAC;IACpE;IACA,OAAOV;AACT;AAEA,SAAS6B,GAAqB0D,CAAAA,CAAqC;IACjE,MAAMvF,IAAiC,CAAC,CAAA,EAMlCwF,IAAQ,IAAI,OAAOpG,GAAc,IAAI;IAC3C,IAAIa;IACJ,MAAQA,IAAQuF,EAAM,IAAA,CAAKD,CAAG,GAAI;QAChC,MAAM7E,IAAIT,CAAAA,CAAM,CAAC,CAAA;QACjB,IAAIS,CAAAA,CAAE,CAAC,CAAA,KAAM,KAAK;YAGhB,MAAM+E,IAAQ,gDAAmC,IAAA,CAAK/E,CAAC;YACvD,IAAI+E,GAAO;gBACT,MAAM,EAAC,KAAA7B,CAAAA,EAAK,KAAAC,CAAG,EAAA,GAAI4B,EAAM,MAAA;gBACzB,IAAI,CAAC7B,IAAM,CAACC,KAAOnD,EAAE,QAAA,CAAS,GAAG,GAAG;oBAElC8E,EAAM,SAAA,IACNxF,EAAO,IAAA,CAAK4E,EAAmBlE,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;oBAC9C;gBACF;YACF;QACF;QACAV,EAAO,IAAA,CAAK4E,EAAmBlE,CAAC,CAAC;IACnC;IACA,OAAOV;AACT,CAEA,OAuBET,KAAA", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-parser/src/parser/node-utils.ts"], "sourcesContent": ["import type {AlternativeContainerNode, Node, ParentNode, QuantifiableNode} from './parse.js';\n\ntype KeysOfUnion<T> = T extends T ? keyof T: never;\ntype Props = {[key in KeysOfUnion<Node>]?: any} & {type?: Node['type']};\n\nfunction hasOnlyChild(node: ParentNode & {body: Array<Node>}, props?: Props): boolean {\n  if (!Array.isArray(node.body)) {\n    throw new Error('Expected node with body array');\n  }\n  if (node.body.length !== 1) {\n    return false;\n  }\n  const kid = node.body[0] as Props;\n  return !props || Object.keys(props).every(key => props[key as keyof Props] === kid[key as keyof Props]);\n}\n\nfunction isAlternativeContainer(node: Node): node is AlternativeContainerNode {\n  if (\n    !alternativeContainerTypes.has(node.type) ||\n    (node.type === 'AbsenceFunction' && node.kind !== 'repeater')\n  ) {\n    return false;\n  }\n  return true;\n}\nconst alternativeContainerTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'CapturingGroup',\n  'Group',\n  'LookaroundAssertion',\n  'Regex',\n]);\n\nfunction isQuantifiable(node: Node): node is QuantifiableNode {\n  return quantifiableTypes.has(node.type);\n}\nconst quantifiableTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'Backreference',\n  'CapturingGroup',\n  'Character',\n  'CharacterClass',\n  'CharacterSet',\n  'Group',\n  'Quantifier',\n  'Subroutine',\n]);\n\nexport {\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "props", "kid", "key", "isAlternativeContainer", "alternativeContainerTypes", "isQuantifiable", "quantifiableTypes"], "mappings": ";;;;;;;;;AAKA,SAASA,EAAaC,CAAAA,EAAwCC,CAAAA,CAAwB;IACpF,IAAI,CAAC,MAAM,OAAA,CAAQD,EAAK,IAAI,GAC1B,MAAM,IAAI,MAAM,+BAA+B;IAEjD,IAAIA,EAAK,IAAA,CAAK,MAAA,KAAW,GACvB,OAAO,CAAA;IAET,MAAME,IAAMF,EAAK,IAAA,CAAK,CAAC,CAAA;IACvB,OAAO,CAACC,KAAS,OAAO,IAAA,CAAKA,CAAK,EAAE,KAAA,EAAME,IAAOF,CAAAA,CAAME,CAAkB,CAAA,KAAMD,CAAAA,CAAIC,CAAkB,CAAC;AACxG;AAEA,SAASC,EAAuBJ,CAAAA,CAA8C;IAC5E,OACE,CAAA,CAAA,CAACK,EAA0B,GAAA,CAAIL,EAAK,IAAI,KACvCA,EAAK,IAAA,KAAS,qBAAqBA,EAAK,IAAA,KAAS,UAAA;AAKtD;AACA,MAAMK,IAA4B,IAAI,IAAkB;IACtD;IACA;IACA;IACA;IACA,OACF;CAAC;AAED,SAASC,EAAeN,CAAAA,CAAsC;IAC5D,OAAOO,EAAkB,GAAA,CAAIP,EAAK,IAAI;AACxC;AACA,MAAMO,IAAoB,IAAI,IAAkB;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YACF;CAAC,EAED,OACER,KAAA,aACAK,KAAA,uBACAE,KAAA", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-parser/src/parser/parse.ts"], "sourcesContent": ["import type {AssertionToken, BackreferenceToken, CharacterClassHyphenToken, CharacterClassOpenToken, CharacterSetToken, FlagGroupModifiers, FlagProperties, GroupOpenToken, QuantifierToken, SubroutineToken, Token, TokenCharacterSetKind, TokenDirectiveKind, TokenNamedCalloutKind, TokenQuantifierKind} from '../tokenizer/tokenize.js';\nimport {tokenize} from '../tokenizer/tokenize.js';\nimport {cpOf, getOrInsert, PosixClassNames, r, throwIfNullish} from '../utils.js';\nimport {hasOnlyChild, isAlternativeContainer, isQuantifiable} from './node-utils.js';\n\n// Watch out for the DOM `Node` interface!\ntype Node =\n  AbsenceFunctionNode |\n  AlternativeNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode |\n  DirectiveNode |\n  FlagsNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  RegexNode |\n  SubroutineNode;\n\ntype OnigurumaAst = RegexNode;\n\ntype ParentNode =\n  AlternativeContainerNode |\n  AlternativeNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  QuantifierNode;\n\n// See also `isAlternativeContainer`\ntype AlternativeContainerNode =\n  AbsenceFunctionNode | // Some sub-kinds aren't alternative containers\n  CapturingGroupNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  RegexNode;\n\ntype AlternativeElementNode =\n  AbsenceFunctionNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  DirectiveNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  SubroutineNode;\n\ntype CharacterClassElementNode =\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode;\n\n// See also `isQuantifiable`\ntype QuantifiableNode =\n  AbsenceFunctionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  GroupNode |\n  QuantifierNode |\n  SubroutineNode;\n\n// TODO: Support remaining kinds; see <github.com/slevithan/oniguruma-to-es/issues/13>\ntype NodeAbsenceFunctionKind =\n  'repeater';\n\ntype NodeAssertionKind =\n  'line_end' |\n  'line_start' |\n  'search_start' |\n  'string_end' |\n  'string_end_newline' |\n  'string_start' |\n  'text_segment_boundary' |\n  'word_boundary';\n\ntype NodeCharacterClassKind =\n  'union' |\n  'intersection';\n\ntype NodeCharacterSetKind = TokenCharacterSetKind;\n\ntype NodeDirectiveKind = TokenDirectiveKind;\n\ntype NodeLookaroundAssertionKind =\n  'lookahead' |\n  'lookbehind';\n\ntype NodeNamedCalloutKind = TokenNamedCalloutKind;\n\ntype NodeQuantifierKind = TokenQuantifierKind;\n\ntype UnicodePropertyMap = Map<string, string>;\n\ntype Context = {\n  capturingGroups: Array<CapturingGroupNode>;\n  hasNumberedRef: boolean;\n  namedGroupsByName: Map<string, Array<CapturingGroupNode>>;\n  nextIndex: number;\n  normalizeUnknownPropertyNames: boolean;\n  parent: ParentNode;\n  skipBackrefValidation: boolean;\n  skipLookbehindValidation: boolean;\n  skipPropertyNameValidation: boolean;\n  subroutines: Array<SubroutineNode>;\n  tokens: Array<Token>;\n  unicodePropertyMap: UnicodePropertyMap | null;\n  walk: (parent: ParentNode, state: State) => Node;\n};\n\n// Top-level `walk` calls are given empty state; nested calls can add data specific to their `walk`\ntype State = {\n  isCheckingRangeEnd?: boolean;\n  isInAbsenceFunction?: boolean;\n  isInLookbehind?: boolean;\n  isInNegLookbehind?: boolean;\n};\n\ntype ParseOptions = {\n  flags?: string;\n  normalizeUnknownPropertyNames?: boolean;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n  skipBackrefValidation?: boolean;\n  skipLookbehindValidation?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\n\nfunction parse(pattern: string, options: ParseOptions = {}): OnigurumaAst {\n  const opts: Required<ParseOptions> = {\n    flags: '',\n    normalizeUnknownPropertyNames: false,\n    skipBackrefValidation: false,\n    skipLookbehindValidation: false,\n    skipPropertyNameValidation: false,\n    // `toOnigurumaAst` provides `OnigUnicodePropertyMap`, but it can be custom or `null`\n    unicodePropertyMap: null,\n    ...options,\n    rules: {\n      captureGroup: false, // `ONIG_OPTION_CAPTURE_GROUP`\n      singleline: false, // `ONIG_OPTION_SINGLELINE`\n      ...options.rules,\n    },\n  };\n  const tokenized = tokenize(pattern, {\n    // Limit to the tokenizer's options\n    flags: opts.flags,\n    rules: {\n      captureGroup: opts.rules.captureGroup,\n      singleline: opts.rules.singleline,\n    },\n  });\n  const walk: Context['walk'] = (parent, state) => {\n    const token = tokenized.tokens[context.nextIndex];\n    context.parent = parent;\n    // Advance for the next iteration\n    context.nextIndex++;\n    switch (token.type) {\n      case 'Alternator':\n        // Top-level only; groups handle their own alternators\n        return createAlternative();\n      case 'Assertion':\n        return parseAssertion(token);\n      case 'Backreference':\n        return parseBackreference(token, context);\n      case 'Character':\n        return createCharacter(token.value, {useLastValid: !!state.isCheckingRangeEnd});\n      case 'CharacterClassHyphen':\n        return parseCharacterClassHyphen(token, context, state);\n      case 'CharacterClassOpen':\n        return parseCharacterClassOpen(token, context, state);\n      case 'CharacterSet':\n        return parseCharacterSet(token, context);\n      case 'Directive':\n        return createDirective(token.kind, {flags: token.flags});\n      case 'GroupOpen':\n        return parseGroupOpen(token, context, state);\n      case 'NamedCallout':\n        return createNamedCallout(token.kind, token.tag, token.arguments);\n      case 'Quantifier':\n        return parseQuantifier(token, context);\n      case 'Subroutine':\n        return parseSubroutine(token, context);\n      default:\n        throw new Error(`Unexpected token type \"${token.type}\"`);\n    }\n  }\n  const context: Context = {\n    capturingGroups: [],\n    hasNumberedRef: false,\n    namedGroupsByName: new Map(),\n    nextIndex: 0,\n    normalizeUnknownPropertyNames: opts.normalizeUnknownPropertyNames,\n    parent: null!, // Assigned by `walk`\n    skipBackrefValidation: opts.skipBackrefValidation,\n    skipLookbehindValidation: opts.skipLookbehindValidation,\n    skipPropertyNameValidation: opts.skipPropertyNameValidation,\n    subroutines: [],\n    tokens: tokenized.tokens,\n    unicodePropertyMap: opts.unicodePropertyMap,\n    walk,\n  };\n\n  // ## AST construction from tokens\n  const ast = createRegex(createFlags(tokenized.flags));\n  let top = ast.body[0]; // First alt\n  while (context.nextIndex < tokenized.tokens.length) {\n    const node = walk(top, {});\n    if (node.type === 'Alternative') {\n      ast.body.push(node);\n      top = node;\n    } else {\n      top.body.push(node as AlternativeElementNode);\n    }\n  }\n\n  // ## Validation that requires knowledge about the complete pattern\n  // `context` updated by the preceding `walk` loop\n  const {capturingGroups, hasNumberedRef, namedGroupsByName, subroutines} = context;\n  if (hasNumberedRef && namedGroupsByName.size && !opts.rules.captureGroup) {\n    throw new Error('Numbered backref/subroutine not allowed when using named capture');\n  }\n  for (const {ref} of subroutines) {\n    if (typeof ref === 'number') {\n      // Relative nums are already resolved\n      if (ref > capturingGroups.length) {\n        throw new Error(`Subroutine uses a group number that's not defined`);\n      }\n      if (ref) {\n        capturingGroups[ref - 1].isSubroutined = true;\n      }\n    } else if (!namedGroupsByName.has(ref)) {\n      throw new Error(r`Subroutine uses a group name that's not defined \"\\g<${ref}>\"`);\n    } else if (namedGroupsByName.get(ref)!.length > 1) {\n      throw new Error(r`Subroutine uses a duplicate group name \"\\g<${ref}>\"`);\n    } else {\n      namedGroupsByName.get(ref)![0].isSubroutined = true;\n    }\n  }\n\n  return ast;\n}\n\nfunction parseAssertion({kind}: AssertionToken): AssertionNode {\n  return createAssertion(\n    throwIfNullish({\n      '^': 'line_start',\n      '$': 'line_end',\n      '\\\\A': 'string_start',\n      '\\\\b': 'word_boundary',\n      '\\\\B': 'word_boundary',\n      '\\\\G': 'search_start',\n      '\\\\y': 'text_segment_boundary',\n      '\\\\Y': 'text_segment_boundary',\n      '\\\\z': 'string_end',\n      '\\\\Z': 'string_end_newline',\n    }[kind], `Unexpected assertion kind \"${kind}\"`) as NodeAssertionKind,\n    {negate: kind === r`\\B` || kind === r`\\Y`}\n  );\n}\n\n// Supported (if the backref appears to the right of the reffed capture's opening paren):\n// - `\\k<name>`, `\\k'name'`\n// - When named capture not used:\n//   - `\\n`, `\\nn`, `\\nnn`\n//   - `\\k<n>`, `\\k'n'\n//   - `\\k<-n>`, `\\k'-n'`\n// Unsupported:\n// - `\\k<+n>`, `\\k'+n'` - Note that, Unlike Oniguruma, Onigmo doesn't support this as special\n//   syntax and therefore considers it a valid group name.\n// - Backref with recursion level (with num or name): `\\k<n+level>`, `\\k<n-level>`, etc.\n//   (Onigmo also supports `\\k<-n+level>`, `\\k<-n-level>`, etc.)\n// Backrefs in Onig use multiplexing for duplicate group names (the rules can be complicated when\n// overlapping with subroutines), but a `Backreference`'s simple `ref` prop doesn't capture these\n// details so multiplexed ref pointers need to be derived when working with the AST\nfunction parseBackreference({raw}: BackreferenceToken, context: Context): BackreferenceNode {\n  const hasKWrapper = /^\\\\k[<']/.test(raw);\n  const ref = hasKWrapper ? raw.slice(3, -1) : raw.slice(1);\n  const fromNum = (num: number, isRelative = false) => {\n    const numCapturesToLeft = context.capturingGroups.length;\n    let orphan = false;\n    // Note: It's not an error for numbered backrefs to come before their referenced group in Onig,\n    // but it's currently an error in this library.\n    // - Most such placements are mistakes and can never match, due to Onig's behavior for backrefs\n    //   to nonparticipating groups.\n    //   - The edge cases where they're matchable rely on rules for backref resetting within\n    //     quantified groups that are different in JS (thus not emulatable in `oniguruma-to-es`).\n    // - Erroring matches the correct behavior of named backrefs.\n    // - For unenclosed backrefs, this only affects `\\1`-`\\9` since it's not a backref in the first\n    //   place if using `\\10` or higher and not as many capturing groups are defined to the left\n    //   (it's an octal or identity escape).\n    // TODO: Ideally this would be refactored to include the backref in the AST when it's not an\n    // error in Onig (due to the reffed group being defined to the right), and the error handling\n    // would move to the `oniguruma-to-es` transformer\n    if (num > numCapturesToLeft) {\n      // Skipping the error breaks assumptions and might create edge case issues, since backrefs\n      // are required to come after their captures; unfortunately this option is needed for\n      // TextMate grammars\n      if (context.skipBackrefValidation) {\n        orphan = true;\n      } else {\n        throw new Error(`Not enough capturing groups defined to the left \"${raw}\"`);\n      }\n    }\n    context.hasNumberedRef = true;\n    return createBackreference(isRelative ? numCapturesToLeft + 1 - num : num, {orphan});\n  };\n  if (hasKWrapper) {\n    const numberedRef = /^(?<sign>-?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n    if (numberedRef) {\n      return fromNum(+numberedRef.groups!.num, !!numberedRef.groups!.sign);\n    }\n    // Invalid in a backref name even when valid in a group name\n    if (/[-+]/.test(ref)) {\n      throw new Error(`Invalid backref name \"${raw}\"`);\n    }\n    if (!context.namedGroupsByName.has(ref)) {\n      throw new Error(`Group name not defined to the left \"${raw}\"`);\n    }\n    return createBackreference(ref);\n  }\n  return fromNum(+ref);\n}\n\nfunction parseCharacterClassHyphen(_: CharacterClassHyphenToken, context: Context, state: State): CharacterNode | CharacterClassRangeNode {\n  const {tokens, walk} = context;\n  const parent = context.parent as CharacterClassNode;\n  const prevSiblingNode = parent.body.at(-1);\n  const nextToken = tokens[context.nextIndex];\n  if (\n    !state.isCheckingRangeEnd &&\n    prevSiblingNode &&\n    prevSiblingNode.type !== 'CharacterClass' &&\n    prevSiblingNode.type !== 'CharacterClassRange' &&\n    nextToken &&\n    nextToken.type !== 'CharacterClassOpen' &&\n    nextToken.type !== 'CharacterClassClose' &&\n    nextToken.type !== 'CharacterClassIntersector'\n  ) {\n    const nextNode = walk(parent, {\n      ...state,\n      isCheckingRangeEnd: true,\n    });\n    if (prevSiblingNode.type === 'Character' && nextNode.type === 'Character') {\n      parent.body.pop();\n      return createCharacterClassRange(prevSiblingNode, nextNode);\n    }\n    throw new Error('Invalid character class range');\n  }\n  return createCharacter(cpOf('-'));\n}\n\nfunction parseCharacterClassOpen({negate}: CharacterClassOpenToken, context: Context, state: State): CharacterClassNode {\n  const {tokens, walk} = context;\n  const firstClassToken = tokens[context.nextIndex];\n  const intersections = [createCharacterClass()];\n  let nextToken = throwIfUnclosedCharacterClass(firstClassToken);\n  while (nextToken.type !== 'CharacterClassClose') {\n    if (nextToken.type === 'CharacterClassIntersector') {\n      intersections.push(createCharacterClass());\n      // Skip the intersector\n      context.nextIndex++;\n    } else {\n      const cc = intersections.at(-1)!; // Always at least one\n      cc.body.push(walk(cc, state) as CharacterClassElementNode);\n    }\n    nextToken = throwIfUnclosedCharacterClass(tokens[context.nextIndex], firstClassToken);\n  }\n  const node = createCharacterClass({negate});\n  if (intersections.length === 1) {\n    node.body = intersections[0].body;\n  } else {\n    node.kind = 'intersection';\n    node.body = intersections.map(cc => cc.body.length === 1 ? cc.body[0] : cc);\n  }\n  // Skip the closing square bracket\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseCharacterSet({kind, negate, value}: CharacterSetToken, context: Context): CharacterSetNode {\n  const {normalizeUnknownPropertyNames, skipPropertyNameValidation, unicodePropertyMap} = context;\n  if (kind === 'property') {\n    const normalized = slug(value!);\n    // Don't treat as POSIX if it's in the provided list of Unicode property names\n    if (PosixClassNames.has(normalized) && !unicodePropertyMap?.has(normalized)) {\n      kind = 'posix';\n      value = normalized;\n    } else {\n      return createUnicodeProperty(value!, {\n        negate,\n        normalizeUnknownPropertyNames,\n        skipPropertyNameValidation,\n        unicodePropertyMap,\n      });\n    }\n  }\n  if (kind === 'posix') {\n    return createPosixClass(value!, {negate});\n  }\n  return createCharacterSet(kind, {negate});\n}\n\nfunction parseGroupOpen(token: GroupOpenToken, context: Context, state: State): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  const {tokens, capturingGroups, namedGroupsByName, skipLookbehindValidation, walk} = context;\n  const node = createByGroupKind(token);\n  const isThisAbsenceFunction = node.type === 'AbsenceFunction';\n  const isThisLookbehind = isLookbehind(node);\n  const isThisNegLookbehind = isThisLookbehind && node.negate;\n  // Track capturing group details for backrefs and subroutines (before parsing the group's\n  // contents so nested groups with the same name are tracked in order)\n  if (node.type === 'CapturingGroup') {\n    capturingGroups.push(node);\n    if (node.name) {\n      getOrInsert(namedGroupsByName, node.name, []).push(node);\n    }\n  }\n  // Don't allow nested absence functions\n  if (isThisAbsenceFunction && state.isInAbsenceFunction) {\n    // Is officially unsupported in Onig but doesn't throw, gives strange results\n    throw new Error('Nested absence function not supported by Oniguruma');\n  }\n  let nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  while (nextToken.type !== 'GroupClose') {\n    if (nextToken.type === 'Alternator') {\n      node.body.push(createAlternative());\n      // Skip the alternator\n      context.nextIndex++;\n    } else {\n      const alt = node.body.at(-1)!; // Always at least one\n      const child = walk(alt, {\n        ...state,\n        isInAbsenceFunction: state.isInAbsenceFunction || isThisAbsenceFunction,\n        isInLookbehind: state.isInLookbehind || isThisLookbehind,\n        isInNegLookbehind: state.isInNegLookbehind || isThisNegLookbehind,\n      }) as AlternativeElementNode;\n      alt.body.push(child);\n      // Centralized validation of lookbehind contents\n      if ((isThisLookbehind || state.isInLookbehind) && !skipLookbehindValidation) {\n        // JS supports all features within lookbehind, but Onig doesn't. Absence functions of form\n        // `(?~|)` and `(?~|…)` are also invalid in lookbehind (the `(?~…)` and `(?~|…|…)` forms\n        // are allowed), but all forms with `(?~|` throw since they aren't yet supported\n        const msg = 'Lookbehind includes a pattern not allowed by Oniguruma';\n        if (isThisNegLookbehind || state.isInNegLookbehind) {\n          // - Invalid: `(?=…)`, `(?!…)`, capturing groups\n          // - Valid: `(?<=…)`, `(?<!…)`\n          if (isLookahead(child) || child.type === 'CapturingGroup') {\n            throw new Error(msg);\n          }\n        } else {\n          // - Invalid: `(?=…)`, `(?!…)`, `(?<!…)`\n          // - Valid: `(?<=…)`, capturing groups\n          if (isLookahead(child) || (isLookbehind(child) && child.negate)) {\n            throw new Error(msg);\n          }\n        }\n      }\n    }\n    nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  }\n  // Skip the closing parenthesis\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseQuantifier({kind, min, max}: QuantifierToken, context: Context): QuantifierNode {\n  const parent = context.parent as AlternativeNode;\n  const quantifiedNode = parent.body.at(-1);\n  if (!quantifiedNode || !isQuantifiable(quantifiedNode)) {\n    throw new Error('Quantifier requires a repeatable token');\n  }\n  const node = createQuantifier(kind, min, max, quantifiedNode);\n  parent.body.pop();\n  return node;\n}\n\n// Onig subroutine behavior:\n// - Subroutines can appear before the groups they reference; ex: `\\g<1>(a)` is valid.\n// - Multiple subroutines can reference the same group.\n// - Subroutines can reference groups that themselves contain subroutines, followed to any depth.\n// - Subroutines can be used recursively, and `\\g<0>` recursively references the whole pattern.\n// - Subroutines can use relative references (backward or forward); ex: `\\g<+1>(.)\\g<-1>`.\n// - Subroutines don't get their own capturing group numbers; ex: `(.)\\g<1>\\2` is invalid.\n// - Subroutines use the flags that apply to their referenced group, so e.g.\n//   `(?-i)(?<a>a)(?i)\\g<a>` is fully case sensitive.\n// - Differences from PCRE/Perl/Regex+ subroutines:\n//   - Subroutines can't reference duplicate group names (though duplicate names are valid if no\n//     subroutines reference them).\n//   - Subroutines can't use absolute or relative numbers if named capture is used anywhere.\n//   - Named backrefs must be to the right of their group definition, so the backref in\n//     `\\g<a>\\k<a>(?<a>)` is invalid (not directly related to subroutines).\n//   - Subroutines don't restore capturing group match values (for backrefs) upon exit, so e.g.\n//     `(?<a>(?<b>[ab]))\\g<a>\\k<b>` matches `abb` but not `aba`; same for numbered.\n// The interaction of backref multiplexing (an Onig-specific feature) and subroutines is complex:\n// - Only the most recent value matched by a capturing group and its subroutines is considered for\n//   backref multiplexing, and this also applies to capturing groups nested within a group that's\n//   referenced by a subroutine.\n// - Although a subroutine can't reference a group with a duplicate name, it can reference a group\n//   with a nested capture whose name is duplicated (e.g. outside of the referenced group).\n//   - These duplicate names can then multiplex; but only the most recent value matched from within\n//     the outer group (or the subroutines that reference it) is available for multiplexing.\n//   - Ex: With `(?<a>(?<b>[123]))\\g<a>\\g<a>(?<b>0)\\k<b>`, the backref `\\k<b>` can only match `0`\n//     or whatever was matched by the most recently matched subroutine. If you took out `(?<b>0)`,\n//     no multiplexing would occur.\nfunction parseSubroutine({raw}: SubroutineToken, context: Context): SubroutineNode {\n  const {capturingGroups, subroutines} = context;\n  let ref: string | number = raw.slice(3, -1);\n  const numberedRef = /^(?<sign>[-+]?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n  if (numberedRef) {\n    const num = +numberedRef.groups!.num;\n    const numCapturesToLeft = capturingGroups.length;\n    context.hasNumberedRef = true;\n    ref = {\n      '': num,\n      '+': numCapturesToLeft + num,\n      '-': numCapturesToLeft + 1 - num,\n    }[numberedRef.groups!.sign]!;\n    if (ref < 1) {\n      throw new Error('Invalid subroutine number');\n    }\n  // Special case for full-pattern recursion; can't be `+0`, `-0`, `00`, etc.\n  } else if (ref === '0') {\n    ref = 0;\n  }\n  const node = createSubroutine(ref);\n  subroutines.push(node);\n  return node;\n}\n\n// -------------------------------\n// --- Node creation and types ---\n// -------------------------------\n\ntype AbsenceFunctionNode = {\n  type: 'AbsenceFunction';\n  kind: NodeAbsenceFunctionKind;\n  body: Array<AlternativeNode>;\n};\nfunction createAbsenceFunction(kind: NodeAbsenceFunctionKind, options?: {\n  body?: Array<AlternativeNode>;\n}): AbsenceFunctionNode {\n  if (kind !== 'repeater') {\n    throw new Error(`Unexpected absence function kind \"${kind}\"`);\n  }\n  return {\n    type: 'AbsenceFunction',\n    kind,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype AlternativeNode = {\n  type: 'Alternative';\n  body: Array<AlternativeElementNode>;\n};\nfunction createAlternative(options?: {\n  body?: Array<AlternativeElementNode>;\n}): AlternativeNode {\n  return {\n    type: 'Alternative',\n    body: getBodyForElementContainer(options?.body) as Array<AlternativeElementNode>,\n  };\n}\n\ntype AssertionNode = {\n  type: 'Assertion';\n  kind: NodeAssertionKind;\n  negate?: boolean;\n};\nfunction createAssertion(kind: NodeAssertionKind, options?: {\n  negate?: boolean;\n}): AssertionNode {\n  const node: AssertionNode = {\n    type: 'Assertion',\n    kind,\n  };\n  if (kind === 'word_boundary' || kind === 'text_segment_boundary') {\n    node.negate = !!options?.negate;\n  }\n  return node;\n}\n\ntype BackreferenceNode = {\n  type: 'Backreference';\n  ref: string | number;\n  orphan?: boolean;\n};\nfunction createBackreference(ref: string | number, options?: {\n  orphan?: boolean;\n}): BackreferenceNode {\n  const orphan = !!options?.orphan;\n  return {\n    type: 'Backreference',\n    ref,\n    ...(orphan && {orphan}),\n  };\n}\n\ntype CapturingGroupNode = {\n  type: 'CapturingGroup';\n  kind?: never;\n  number: number;\n  name?: string;\n  // One or more subroutines in the regex reference this group\n  isSubroutined?: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createCapturingGroup(number: number, options?: {\n  name?: string;\n  isSubroutined?: boolean;\n  body?: Array<AlternativeNode>;\n}): CapturingGroupNode {\n  const opts = {\n    name: undefined,\n    isSubroutined: false,\n    ...options,\n  };\n  if (opts.name !== undefined && !isValidGroupName(opts.name)) {\n    throw new Error(`Group name \"${opts.name}\" invalid in Oniguruma`);\n  }\n  return {\n    type: 'CapturingGroup',\n    number,\n    ...(opts.name && {name: opts.name}),\n    ...(opts.isSubroutined && {isSubroutined: opts.isSubroutined}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype CharacterNode = {\n  type: 'Character';\n  value: number;\n};\nfunction createCharacter(charCode: number, options?: {\n  useLastValid?: boolean;\n}): CharacterNode {\n  const opts = {\n    useLastValid: false,\n    ...options,\n  };\n  if (charCode > 0x10FFFF) {\n    const hex = charCode.toString(16);\n    if (opts.useLastValid) {\n      charCode = 0x10FFFF;\n    } else if (charCode > 0x13FFFF) {\n      throw new Error(`Invalid code point out of range \"\\\\x{${hex}}\"`);\n    } else {\n      throw new Error(`Invalid code point out of range in JS \"\\\\x{${hex}}\"`);\n    }\n  }\n  return {\n    type: 'Character',\n    value: charCode,\n  };\n}\n\ntype CharacterClassNode = {\n  type: 'CharacterClass';\n  kind: NodeCharacterClassKind;\n  negate: boolean;\n  body: Array<CharacterClassElementNode>;\n};\nfunction createCharacterClass(options?: {\n  kind?: NodeCharacterClassKind;\n  negate?: boolean;\n  body?: Array<CharacterClassElementNode>;\n}): CharacterClassNode {\n  const opts = {\n    kind: 'union' as NodeCharacterClassKind,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'CharacterClass',\n    kind: opts.kind,\n    negate: opts.negate,\n    body: getBodyForElementContainer(options?.body) as Array<CharacterClassElementNode>,\n  };\n}\n\ntype CharacterClassRangeNode = {\n  type: 'CharacterClassRange';\n  min: CharacterNode;\n  max: CharacterNode;\n};\nfunction createCharacterClassRange(min: CharacterNode, max: CharacterNode): CharacterClassRangeNode {\n  if (max.value < min.value) {\n    throw new Error('Character class range out of order');\n  }\n  return {\n    type: 'CharacterClassRange',\n    min,\n    max,\n  };\n}\n\ntype NamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: 'posix' | 'property';\n  value: string;\n  negate: boolean;\n  variableLength?: never;\n};\ntype UnnamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: Exclude<NodeCharacterSetKind, NamedCharacterSetNode['kind']>;\n  value?: never;\n  negate?: boolean;\n  variableLength?: boolean;\n};\ntype CharacterSetNode = NamedCharacterSetNode | UnnamedCharacterSetNode;\n/**\nUse `createUnicodeProperty` and `createPosixClass` for `kind` values `'property'` and `'posix'`.\n*/\nfunction createCharacterSet(kind: UnnamedCharacterSetNode['kind'], options?: {\n  negate?: boolean;\n}): UnnamedCharacterSetNode {\n  const negate = !!options?.negate;\n  const node: UnnamedCharacterSetNode = {\n    type: 'CharacterSet',\n    kind,\n  };\n  if (\n    kind === 'digit' ||\n    kind === 'hex' ||\n    kind === 'newline' ||\n    kind === 'space' ||\n    kind === 'word'\n  ) {\n    node.negate = negate;\n  }\n  if (\n    kind === 'text_segment' ||\n    (kind === 'newline' && !negate)\n  ) {\n    node.variableLength = true;\n  }\n  return node;\n}\n\ntype DirectiveNode = {\n  type: 'Directive';\n} & ({\n  kind: 'keep';\n  flags?: never;\n} | {\n  kind: 'flags';\n  flags: FlagGroupModifiers;\n});\nfunction createDirective(kind: NodeDirectiveKind, options: {flags?: FlagGroupModifiers} = {}): DirectiveNode {\n  if (kind === 'keep') {\n    return {\n      type: 'Directive',\n      kind,\n    };\n  }\n  if (kind === 'flags') {\n    // Note: Flag effects might extend across alternation; ex: `a(?i)b|c` is equivalent to\n    // `a(?i:b)|(?i:c)`, not `a(?i:b|c)`\n    return {\n      type: 'Directive',\n      kind,\n      flags: throwIfNullish(options.flags),\n    };\n  }\n  throw new Error(`Unexpected directive kind \"${kind}\"`);\n}\n\ntype FlagsNode = {\n  type: 'Flags';\n} & FlagProperties;\nfunction createFlags(flags: FlagProperties): FlagsNode {\n  return {\n    type: 'Flags',\n    ...flags,\n  };\n}\n\ntype GroupNode = {\n  type: 'Group';\n  kind?: never;\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body: Array<AlternativeNode>;\n};\nfunction createGroup(options?: {\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body?: Array<AlternativeNode>;\n}): GroupNode {\n  const atomic = options?.atomic;\n  const flags = options?.flags;\n  if (atomic && flags) {\n    throw new Error('Atomic group cannot have flags');\n  }\n  return {\n    type: 'Group',\n    ...(atomic && {atomic}),\n    ...(flags && {flags}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype LookaroundAssertionNode = {\n  type: 'LookaroundAssertion';\n  kind: NodeLookaroundAssertionKind;\n  negate: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createLookaroundAssertion(options?: {\n  behind?: boolean;\n  negate?: boolean;\n  body?: Array<AlternativeNode>;\n}): LookaroundAssertionNode {\n  const opts = {\n    behind: false,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'LookaroundAssertion',\n    kind: opts.behind ? 'lookbehind' : 'lookahead',\n    negate: opts.negate,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype NamedCalloutNode = {\n  type: 'NamedCallout';\n  kind: NodeNamedCalloutKind;\n  tag: string | null;\n  arguments: Array<string | number> | null;\n};\nfunction createNamedCallout(\n  kind: NodeNamedCalloutKind,\n  tag: string | null,\n  args: Array<string | number> | null\n): NamedCalloutNode {\n  return {\n    type: 'NamedCallout',\n    kind,\n    tag,\n    arguments: args,\n  };\n}\n\nfunction createPosixClass(name: string, options?: {\n  negate?: boolean;\n}): NamedCharacterSetNode & {kind: 'posix'} {\n  const negate = !!options?.negate;\n  if (!PosixClassNames.has(name)) {\n    throw new Error(`Invalid POSIX class \"${name}\"`);\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'posix',\n    value: name,\n    negate,\n  };\n}\n\ntype QuantifierNode = {\n  type: 'Quantifier';\n  kind: NodeQuantifierKind;\n  min: number;\n  max: number;\n  body: QuantifiableNode;\n};\nfunction createQuantifier(kind: NodeQuantifierKind, min: number, max: number, body: QuantifiableNode): QuantifierNode {\n  if (min > max) {\n    throw new Error('Invalid reversed quantifier range');\n  }\n  return {\n    type: 'Quantifier',\n    kind,\n    min,\n    max,\n    body,\n  };\n}\n\ntype RegexNode = {\n  type: 'Regex';\n  body: Array<AlternativeNode>;\n  flags: FlagsNode;\n};\nfunction createRegex(flags: FlagsNode, options?: {\n  body?: Array<AlternativeNode>;\n}): RegexNode {\n  return {\n    type: 'Regex',\n    body: getBodyForAlternativeContainer(options?.body),\n    flags,\n  };\n}\n\ntype SubroutineNode = {\n  type: 'Subroutine';\n  ref: string | number;\n};\nfunction createSubroutine(ref: string | number): SubroutineNode {\n  return {\n    type: 'Subroutine',\n    ref,\n  };\n}\n\ntype CreateUnicodePropertyOptions = {\n  negate?: boolean;\n  normalizeUnknownPropertyNames?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\nfunction createUnicodeProperty(name: string, options?: CreateUnicodePropertyOptions): NamedCharacterSetNode & {kind: 'property'} {\n  const opts: Required<CreateUnicodePropertyOptions> = {\n    negate: false,\n    normalizeUnknownPropertyNames: false,\n    skipPropertyNameValidation: false,\n    unicodePropertyMap: null,\n    ...options,\n  };\n  let normalized = opts.unicodePropertyMap?.get(slug(name));\n  if (!normalized) {\n    if (opts.normalizeUnknownPropertyNames) {\n      normalized = normalizeUnicodePropertyName(name);\n    // Let the name through as-is if no map provided and normalization not requested\n    } else if (opts.unicodePropertyMap && !opts.skipPropertyNameValidation) {\n      throw new Error(r`Invalid Unicode property \"\\p{${name}}\"`);\n    }\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'property',\n    value: normalized ?? name,\n    negate: opts.negate,\n  };\n}\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nfunction createByGroupKind({flags, kind, name, negate, number}: GroupOpenToken): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  switch (kind) {\n    case 'absence_repeater':\n      return createAbsenceFunction('repeater');\n    case 'atomic':\n      return createGroup({atomic: true});\n    case 'capturing':\n      return createCapturingGroup(number!, {name});\n    case 'group':\n      return createGroup({flags});\n    case 'lookahead':\n    case 'lookbehind':\n      return createLookaroundAssertion({\n        behind: kind === 'lookbehind',\n        negate,\n      });\n    default:\n      throw new Error(`Unexpected group kind \"${kind}\"`);\n  }\n}\n\nfunction getBodyForAlternativeContainer(body: unknown): Array<AlternativeNode> {\n  if (body === undefined) {\n    body = [createAlternative()];\n  } else if (!Array.isArray(body) || !body.length || !body.every(node => (node as Node).type === 'Alternative')) {\n    throw new Error('Invalid body; expected array of one or more Alternative nodes');\n  }\n  return body as Array<AlternativeNode>;\n}\n\nfunction getBodyForElementContainer(body: unknown): Array<Node> {\n  if (body === undefined) {\n    body = [];\n  } else if (!Array.isArray(body) || !body.every(node => !!(node as Node).type)) {\n    throw new Error('Invalid body; expected array of nodes');\n  }\n  return body as Array<Node>;\n}\n\nfunction isLookahead(node: Node): node is (LookaroundAssertionNode & {kind: 'lookahead'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookahead';\n}\n\nfunction isLookbehind(node: Node): node is (LookaroundAssertionNode & {kind: 'lookbehind'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookbehind';\n}\n\nfunction isValidGroupName(name: string): boolean {\n  // Note that backrefs and subroutines might contextually use `-` and `+` to indicate relative\n  // index or recursion level\n  return /^[\\p{Alpha}\\p{Pc}][^)]*$/u.test(name);\n}\n\nfunction normalizeUnicodePropertyName(name: string): string {\n  // In Onig, Unicode property names ignore case, spaces, hyphens, and underscores. Use best effort\n  // to reformat the name to follow official values (covers a lot, but isn't able to map for all\n  // possible formatting differences)\n  return name.\n    trim().\n    replace(/[- _]+/g, '_').\n    replace(/[A-Z][a-z]+(?=[A-Z])/g, '$&_'). // `PropertyName` to `Property_Name`\n    replace(/[A-Za-z]+/g, m => m[0].toUpperCase() + m.slice(1).toLowerCase());\n}\n\n/**\nGenerates a Unicode property lookup name: lowercase, without spaces, hyphens, or underscores.\n*/\nfunction slug(name: string): string {\n  return name.replace(/[- _]+/g, '').toLowerCase();\n}\n\nfunction throwIfUnclosedCharacterClass<T>(token: T, firstClassToken?: Token): NonNullable<T> {\n  return throwIfNullish(\n    token,\n    // Easier to understand the error if it says \"empty\" when the unclosed class starts with\n    // literal `]`; ex: `[]` or `[]a`\n    `${firstClassToken?.type === 'Character' && firstClassToken.value === 93 ?\n      'Empty' : 'Unclosed'} character class`\n  );\n}\n\nfunction throwIfUnclosedGroup<T>(token: T): NonNullable<T> {\n  return throwIfNullish(token, 'Unclosed group');\n}\n\nexport {\n  type AbsenceFunctionNode,\n  type AlternativeNode,\n  type AlternativeContainerNode,\n  type AlternativeElementNode,\n  type AssertionNode,\n  type BackreferenceNode,\n  type CapturingGroupNode,\n  type CharacterClassElementNode,\n  type CharacterClassNode,\n  type CharacterClassRangeNode,\n  type CharacterNode,\n  type CharacterSetNode,\n  type DirectiveNode,\n  type FlagsNode,\n  type GroupNode,\n  type LookaroundAssertionNode,\n  type NamedCalloutNode,\n  type Node,\n  type NodeAbsenceFunctionKind,\n  type NodeAssertionKind,\n  type NodeCharacterClassKind,\n  type NodeCharacterSetKind,\n  type NodeDirectiveKind,\n  type NodeLookaroundAssertionKind,\n  type NodeQuantifierKind,\n  type OnigurumaAst,\n  type ParentNode,\n  type ParseOptions,\n  type QuantifiableNode,\n  type QuantifierNode,\n  type RegexNode,\n  type SubroutineNode,\n  type UnicodePropertyMap,\n  createAbsenceFunction,\n  createAlternative,\n  createAssertion,\n  createBackreference,\n  createCapturingGroup,\n  createCharacter,\n  createCharacterClass,\n  createCharacterClassRange,\n  createCharacterSet,\n  createDirective,\n  createFlags,\n  createGroup,\n  createLookaroundAssertion,\n  createNamedCallout,\n  createPosixClass,\n  createQuantifier,\n  createRegex,\n  createSubroutine,\n  createUnicodeProperty,\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n  parse,\n  slug,\n};\n"], "names": ["tokenize", "cpOf", "getOrInsert", "PosixClassNames", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAlternativeContainer", "isQuantifiable", "parse", "pattern", "options", "opts", "tokenized", "walk", "parent", "state", "token", "context", "createAlternative", "parseAssertion", "parseBackreference", "createCharacter", "parseCharacterClassHyphen", "parseCharacterClassOpen", "parseCharacterSet", "createDirective", "parseGroupOpen", "createNamedCallout", "parseQuantifier", "parseSubroutine", "ast", "createRegex", "createFlags", "top", "node", "capturingGroups", "hasNumberedRef", "namedGroupsByName", "subroutines", "ref", "kind", "createAssertion", "raw", "hasKWrapper", "fromNum", "num", "isRelative", "numCapturesToLeft", "orphan", "createBackreference", "numberedRef", "_", "tokens", "prevSiblingNode", "nextToken", "nextNode", "createCharacterClassRange", "negate", "firstClassToken", "intersections", "createCharacterClass", "throwIfUnclosedCharacterClass", "cc", "value", "normalizeUnknownPropertyNames", "skipPropertyNameValidation", "unicodePropertyMap", "normalized", "slug", "createUnicodeProperty", "createPosixClass", "createCharacterSet", "skipLookbehindValidation", "createByGroupKind", "isThisAbsenceFunction", "isThisLookbehind", "isLookbehind", "isThisNegLookbehind", "throwIfUnclosedGroup", "alt", "child", "msg", "isLookahead", "min", "max", "quantifiedNode", "createQuantifier", "createSubroutine", "createAbsenceFunction", "getBodyForAlternativeContainer", "getBodyForElementContainer", "createCapturingGroup", "number", "isValidGroupName", "charCode", "hex", "flags", "createGroup", "atomic", "createLookaroundAssertion", "tag", "args", "name", "body", "normalizeUnicodePropertyName", "m"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,OAAQ,YAAAA,MAAe;AACvB,OAAQ,QAAAC,EAAM,eAAAC,EAAa,mBAAAC,EAAiB,KAAAC,EAAG,kBAAAC,MAAqB;AACpE,OAAQ,gBAAAC,EAAc,0BAAAC,EAAwB,kBAAAC,MAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6InE,SAASC,EAAMC,CAAAA;YAAiBC,iEAAwB,CAAC,EAAiB;IACxE,MAAMC,IAA+B;QACnC,OAAO;QACP,+BAA+B,CAAA;QAC/B,uBAAuB,CAAA;QACvB,0BAA0B,CAAA;QAC1B,4BAA4B,CAAA;QAE5B,oBAAoB;QACpB,GAAGD,CAAAA;QACH,OAAO;YACL,cAAc,CAAA;YACd,YAAY,CAAA;YACZ,GAAGA,EAAQ,KACb;;IACF,GACME,QAAYb,mLAAAA,EAASU,GAAS;QAElC,OAAOE,EAAK,KAAA;QACZ,OAAO;YACL,cAAcA,EAAK,KAAA,CAAM,YAAA;YACzB,YAAYA,EAAK,KAAA,CAAM;QACzB;IACF,CAAC,GACKE,IAAwB,CAACC,GAAQC,IAAU;QAC/C,MAAMC,IAAQJ,EAAU,MAAA,CAAOK,EAAQ,SAAS,CAAA;QAIhD,OAHAA,EAAQ,MAAA,GAASH,GAEjBG,EAAQ,SAAA,IACAD,EAAM,IAAA,CAAM;YAClB,KAAK;gBAEH,OAAOE,EAAkB;YAC3B,KAAK;gBACH,OAAOC,EAAeH,CAAK;YAC7B,KAAK;gBACH,OAAOI,EAAmBJ,GAAOC,CAAO;YAC1C,KAAK;gBACH,OAAOI,EAAgBL,EAAM,KAAA,EAAO;oBAAC,cAAc,CAAC,CAACD,EAAM;gBAAkB,CAAC;YAChF,KAAK;gBACH,OAAOO,GAA0BN,GAAOC,GAASF,CAAK;YACxD,KAAK;gBACH,OAAOQ,GAAwBP,GAAOC,GAASF,CAAK;YACtD,KAAK;gBACH,OAAOS,GAAkBR,GAAOC,CAAO;YACzC,KAAK;gBACH,OAAOQ,EAAgBT,EAAM,IAAA,EAAM;oBAAC,OAAOA,EAAM;gBAAK,CAAC;YACzD,KAAK;gBACH,OAAOU,GAAeV,GAAOC,GAASF,CAAK;YAC7C,KAAK;gBACH,OAAOY,EAAmBX,EAAM,IAAA,EAAMA,EAAM,GAAA,EAAKA,EAAM,SAAS;YAClE,KAAK;gBACH,OAAOY,GAAgBZ,GAAOC,CAAO;YACvC,KAAK;gBACH,OAAOY,GAAgBb,GAAOC,CAAO;YACvC;gBACE,MAAM,IAAI,MAAM,0BAAoC,OAAVD,EAAM,IAAI,EAAA,EAAG;QAC3D;IACF,GACMC,IAAmB;QACvB,iBAAiB,CAAC,CAAA;QAClB,gBAAgB,CAAA;QAChB,mBAAmB,IAAI;QACvB,WAAW;QACX,+BAA+BN,EAAK,6BAAA;QACpC,QAAQ;QACR,uBAAuBA,EAAK,qBAAA;QAC5B,0BAA0BA,EAAK,wBAAA;QAC/B,4BAA4BA,EAAK,0BAAA;QACjC,aAAa,CAAC,CAAA;QACd,QAAQC,EAAU,MAAA;QAClB,oBAAoBD,EAAK,kBAAA;QACzB,MAAAE;IACF,GAGMiB,IAAMC,EAAYC,EAAYpB,EAAU,KAAK,CAAC;IACpD,IAAIqB,IAAMH,EAAI,IAAA,CAAK,CAAC,CAAA;IACpB,MAAOb,EAAQ,SAAA,GAAYL,EAAU,MAAA,CAAO,MAAA,EAAQ;QAClD,MAAMsB,IAAOrB,EAAKoB,GAAK,CAAC,CAAC;QACrBC,EAAK,IAAA,KAAS,gBAAA,CAChBJ,EAAI,IAAA,CAAK,IAAA,CAAKI,CAAI,GAClBD,IAAMC,CAAAA,IAEND,EAAI,IAAA,CAAK,IAAA,CAAKC,CAA8B;IAEhD;IAIA,MAAM,EAAC,iBAAAC,CAAAA,EAAiB,gBAAAC,CAAAA,EAAgB,mBAAAC,CAAAA,EAAmB,aAAAC,CAAW,EAAA,GAAIrB;IAC1E,IAAImB,KAAkBC,EAAkB,IAAA,IAAQ,CAAC1B,EAAK,KAAA,CAAM,YAAA,EAC1D,MAAM,IAAI,MAAM,kEAAkE;IAEpF,KAAA,MAAW,EAAC,KAAA4B,CAAG,EAAA,IAAKD,EAClB,IAAI,OAAOC,KAAQ,UAAU;QAE3B,IAAIA,IAAMJ,EAAgB,MAAA,EACxB,MAAM,IAAI,MAAM,mDAAmD;QAEjEI,KAAAA,CACFJ,CAAAA,CAAgBI,IAAM,CAAC,CAAA,CAAE,aAAA,GAAgB,CAAA,CAAA;IAE7C,OAAA,IAAYF,EAAkB,GAAA,CAAIE,CAAG,GAE9B;QAAA,IAAIF,EAAkB,GAAA,CAAIE,CAAG,EAAG,MAAA,GAAS,GAC9C,MAAM,IAAI,UAAMpC,4JAAAA,qBAA+CoC,CAAG,IAAI;QAEtEF,EAAkB,GAAA,CAAIE,CAAG,CAAA,CAAG,CAAC,CAAA,CAAE,aAAA,GAAgB,CAAA;IAAA,KAJ/C,EAAA,MAAM,IAAI,UAAMpC,4JAAAA,sBAAwDoC,CAAG,IAAI;IAQnF,OAAOT;AACT;AAEA,SAASX,EAAe,KAAK;UAAJ,MAAAqB,CAAI,EAAA,CAAkC;IAC7D,OAAOC,MACLrC,yKAAAA,EAAe;QACb,KAAK;QACL,GAAK;QACL,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT,CAAA,CAAEoC,CAAI,CAAA,EAAG,8BAAkC,OAAJA,CAAI,EAAA,EAAG,KAC9C;QAAC,QAAQA,UAASrC,4JAAAA,yBAASqC,UAASrC,4JAAAA;IAAK,CAC3C;AACF;AAgBA,SAASiB,OAAuB,EAAuBH,CAAAA,CAAqC;UAA/D,KAAAyB,CAAG,EAAA,GAAJ;IAC1B,MAAMC,IAAc,WAAW,IAAA,CAAKD,CAAG,GACjCH,IAAMI,IAAcD,EAAI,KAAA,CAAM,GAAG,CAAA,CAAE,IAAIA,EAAI,KAAA,CAAM,CAAC,GAClDE,IAAU,SAACC;YAAaC,qEAAa,CAAA,IAAU;QACnD,MAAMC,IAAoB9B,EAAQ,eAAA,CAAgB,MAAA;QAClD,IAAI+B,IAAS,CAAA;QAcb,IAAIH,IAAME,GAIR,IAAI9B,EAAQ,qBAAA,EACV+B,IAAS,CAAA,MAET;aAAA,MAAM,IAAI,MAAM,oDAAuD,EAAG,KAANN,CAAG,EAAA;QAG3E,OAAAzB,EAAQ,cAAA,GAAiB,CAAA,GAClBgC,EAAoBH,IAAaC,IAAoB,IAAIF,IAAMA,GAAK;YAAC,QAAAG;QAAM,CAAC;IACrF;IACA,IAAIL,GAAa;QACf,MAAMO,IAAc,2CAAkC,IAAA,CAAKX,CAAG;QAC9D,IAAIW,GACF,OAAON,EAAQ,CAACM,EAAY,MAAA,CAAQ,GAAA,EAAK,CAAC,CAACA,EAAY,MAAA,CAAQ,IAAI;QAGrE,IAAI,OAAO,IAAA,CAAKX,CAAG,GACjB,MAAM,IAAI,MAAM,yBAA4B,OAAHG,CAAG,EAAA,EAAG;QAEjD,IAAI,CAACzB,EAAQ,iBAAA,CAAkB,GAAA,CAAIsB,CAAG,GACpC,MAAM,IAAI,MAAM,uCAA0C,OAAHG,CAAG,EAAA,EAAG;QAE/D,OAAOO,EAAoBV,CAAG;IAChC;IACA,OAAOK,EAAQ,CAACL,CAAG;AACrB;AAEA,SAASjB,GAA0B6B,CAAAA,EAA8BlC,CAAAA,EAAkBF,CAAAA,CAAuD;IACxI,MAAM,EAAC,QAAAqC,CAAAA,EAAQ,MAAAvC,CAAI,EAAA,GAAII,GACjBH,IAASG,EAAQ,MAAA,EACjBoC,IAAkBvC,EAAO,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE,GACnCwC,IAAYF,CAAAA,CAAOnC,EAAQ,SAAS,CAAA;IAC1C,IACE,CAACF,EAAM,kBAAA,IACPsC,KACAA,EAAgB,IAAA,KAAS,oBACzBA,EAAgB,IAAA,KAAS,yBACzBC,KACAA,EAAU,IAAA,KAAS,wBACnBA,EAAU,IAAA,KAAS,yBACnBA,EAAU,IAAA,KAAS,6BACnB;QACA,MAAMC,IAAW1C,EAAKC,GAAQ;YAC5B,GAAGC,CAAAA;YACH,oBAAoB,CAAA;QACtB,CAAC;QACD,IAAIsC,EAAgB,IAAA,KAAS,eAAeE,EAAS,IAAA,KAAS,aAC5D,OAAAzC,EAAO,IAAA,CAAK,GAAA,CAAI,GACT0C,EAA0BH,GAAiBE,CAAQ;QAE5D,MAAM,IAAI,MAAM,+BAA+B;IACjD;IACA,OAAOlC,MAAgBrB,+JAAAA,EAAK,GAAG,CAAC;AAClC;AAEA,SAASuB,QAA+B,EAA4BN,CAAAA,EAAkBF,CAAAA,CAAkC;UAAtF,QAAA0C,CAAM,EAAA,GAAP;IAC/B,MAAM,EAAC,QAAAL,CAAAA,EAAQ,MAAAvC,CAAI,EAAA,GAAII,GACjByC,IAAkBN,CAAAA,CAAOnC,EAAQ,SAAS,CAAA,EAC1C0C,IAAgB;QAACC,EAAqB,CAAC;KAAA;IAC7C,IAAIN,IAAYO,EAA8BH,CAAe;IAC7D,MAAOJ,EAAU,IAAA,KAAS,uBAAuB;QAC/C,IAAIA,EAAU,IAAA,KAAS,6BACrBK,EAAc,IAAA,CAAKC,EAAqB,CAAC,GAEzC3C,EAAQ,SAAA;aACH;YACL,MAAM6C,IAAKH,EAAc,EAAA,CAAG,CAAA,CAAE;YAC9BG,EAAG,IAAA,CAAK,IAAA,CAAKjD,EAAKiD,GAAI/C,CAAK,CAA8B;QAC3D;QACAuC,IAAYO,EAA8BT,CAAAA,CAAOnC,EAAQ,SAAS,CAAA,EAAGyC,CAAe;IACtF;IACA,MAAMxB,IAAO0B,EAAqB;QAAC,QAAAH;IAAM,CAAC;IAC1C,OAAIE,EAAc,MAAA,KAAW,IAC3BzB,EAAK,IAAA,GAAOyB,CAAAA,CAAc,CAAC,CAAA,CAAE,IAAA,GAAA,CAE7BzB,EAAK,IAAA,GAAO,gBACZA,EAAK,IAAA,GAAOyB,EAAc,GAAA,EAAIG,IAAMA,EAAG,IAAA,CAAK,MAAA,KAAW,IAAIA,EAAG,IAAA,CAAK,CAAC,CAAA,GAAIA,CAAE,CAAA,GAG5E7C,EAAQ,SAAA,IACDiB;AACT;AAEA,SAASV,QAAsC,EAAsBP,CAAAA,CAAoC;UAA7E,MAAAuB,CAAAA,EAAM,QAAAiB,CAAAA,EAAQ,OAAAM,CAAK,EAAA,GAApB;IACzB,MAAM,EAAC,+BAAAC,CAAAA,EAA+B,4BAAAC,CAAAA,EAA4B,oBAAAC,CAAkB,EAAA,GAAIjD;IACxF,IAAIuB,MAAS,YAAY;QACvB,MAAM2B,IAAaC,EAAKL,CAAM;QAE9B,IAAI7D,0KAAAA,CAAgB,GAAA,CAAIiE,CAAU,KAAK,wCAACD,EAAoB,GAAA,CAAIC,CAAU,IACxE3B,IAAO,SACPuB,IAAQI,MAER;aAAA,OAAOE,EAAsBN,GAAQ;YACnC,QAAAN;YACA,+BAAAO;YACA,4BAAAC;YACA,oBAAAC;QACF,CAAC;IAEL;IACA,OAAI1B,MAAS,UACJ8B,EAAiBP,GAAQ;QAAC,QAAAN;IAAM,CAAC,IAEnCc,EAAmB/B,GAAM;QAAC,QAAAiB;IAAM,CAAC;AAC1C;AAEA,SAAS/B,GAAeV,CAAAA,EAAuBC,CAAAA,EAAkBF,CAAAA,CAA8F;IAC7J,MAAM,EAAC,QAAAqC,CAAAA,EAAQ,iBAAAjB,CAAAA,EAAiB,mBAAAE,CAAAA,EAAmB,0BAAAmC,CAAAA,EAA0B,MAAA3D,CAAI,EAAA,GAAII,GAC/EiB,IAAOuC,GAAkBzD,CAAK,GAC9B0D,IAAwBxC,EAAK,IAAA,KAAS,mBACtCyC,IAAmBC,EAAa1C,CAAI,GACpC2C,IAAsBF,KAAoBzC,EAAK,MAAA;IAUrD,IAPIA,EAAK,IAAA,KAAS,oBAAA,CAChBC,EAAgB,IAAA,CAAKD,CAAI,GACrBA,EAAK,IAAA,QACPjC,sKAAAA,EAAYoC,GAAmBH,EAAK,IAAA,EAAM,CAAC,CAAC,EAAE,IAAA,CAAKA,CAAI,CAAA,GAIvDwC,KAAyB3D,EAAM,mBAAA,EAEjC,MAAM,IAAI,MAAM,oDAAoD;IAEtE,IAAIuC,IAAYwB,EAAqB1B,CAAAA,CAAOnC,EAAQ,SAAS,CAAC;IAC9D,MAAOqC,EAAU,IAAA,KAAS,cAAc;QACtC,IAAIA,EAAU,IAAA,KAAS,cACrBpB,EAAK,IAAA,CAAK,IAAA,CAAKhB,EAAkB,CAAC,GAElCD,EAAQ,SAAA;aACH;YACL,MAAM8D,IAAM7C,EAAK,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE,GACrB8C,IAAQnE,EAAKkE,GAAK;gBACtB,GAAGhE,CAAAA;gBACH,qBAAqBA,EAAM,mBAAA,IAAuB2D;gBAClD,gBAAgB3D,EAAM,cAAA,IAAkB4D;gBACxC,mBAAmB5D,EAAM,iBAAA,IAAqB8D;YAChD,CAAC;YAGD,IAFAE,EAAI,IAAA,CAAK,IAAA,CAAKC,CAAK,GAAA,CAEdL,KAAoB5D,EAAM,cAAA,KAAmB,CAACyD,GAA0B;gBAI3E,MAAMS,IAAM;gBACZ,IAAIJ,KAAuB9D,EAAM,iBAAA,EAAA;oBAG/B,IAAImE,EAAYF,CAAK,KAAKA,EAAM,IAAA,KAAS,kBACvC,MAAM,IAAI,MAAMC,CAAG;gBAAA,OAAA,IAKjBC,EAAYF,CAAK,KAAMJ,EAAaI,CAAK,KAAKA,EAAM,MAAA,EACtD,MAAM,IAAI,MAAMC,CAAG;YAGzB;QACF;QACA3B,IAAYwB,EAAqB1B,CAAAA,CAAOnC,EAAQ,SAAS,CAAC;IAC5D;IAEA,OAAAA,EAAQ,SAAA,IACDiB;AACT;AAEA,SAASN,GAAgB,KAAe,EAAoBX,CAAAA,CAAkC;UAApE,MAAAuB,CAAAA,EAAM,KAAA2C,CAAAA,EAAK,KAAAC,CAAG,EAAA;IACtC,MAAMtE,IAASG,EAAQ,MAAA,EACjBoE,IAAiBvE,EAAO,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE;IACxC,IAAI,CAACuE,KAAkB,KAAC9E,2LAAAA,EAAe8E,CAAc,GACnD,MAAM,IAAI,MAAM,wCAAwC;IAE1D,MAAMnD,IAAOoD,EAAiB9C,GAAM2C,GAAKC,GAAKC,CAAc;IAC5D,OAAAvE,EAAO,IAAA,CAAK,GAAA,CAAI,GACToB;AACT;AA8BA,SAASL,GAAgB,KAAI,EAAoBZ,CAAAA,CAAkC;UAAzD,KAAAyB,CAAG,EAAA;IAC3B,MAAM,EAAC,iBAAAP,CAAAA,EAAiB,aAAAG,CAAW,EAAA,GAAIrB;IACvC,IAAIsB,IAAuBG,EAAI,KAAA,CAAM,GAAG,CAAA,CAAE;IAC1C,MAAMQ,IAAc,8CAAqC,IAAA,CAAKX,CAAG;IACjE,IAAIW,GAAa;QACf,MAAML,IAAM,CAACK,EAAY,MAAA,CAAQ,GAAA,EAC3BH,IAAoBZ,EAAgB,MAAA;QAO1C,IANAlB,EAAQ,cAAA,GAAiB,CAAA,GACzBsB,IAAM;YACJ,IAAIM;YACJ,KAAKE,IAAoBF;YACzB,KAAKE,IAAoB,IAAIF;SAC/B,CAAA,CAAEK,EAAY,MAAA,CAAQ,IAAI,CAAA,EACtBX,IAAM,GACR,MAAM,IAAI,MAAM,2BAA2B;IAG/C,OAAWA,MAAQ,OAAA,CACjBA,IAAM,CAAA;IAER,MAAML,IAAOqD,EAAiBhD,CAAG;IACjC,OAAAD,EAAY,IAAA,CAAKJ,CAAI,GACdA;AACT;AAWA,SAASsD,EAAsBhD,CAAAA,EAA+B9B,CAAAA,CAEtC;IACtB,IAAI8B,MAAS,YACX,MAAM,IAAI,MAAM,qCAAyC,EAAG,KAAPA,CAAI,EAAA;IAE3D,OAAO;QACL,MAAM;QACN,MAAAA;QACA,MAAMiD,wCAA+B/E,EAAS,IAAI;IACpD;AACF;AAMA,SAASQ,EAAkBR,CAAAA,CAEP;IAClB,OAAO;QACL,MAAM;QACN,MAAMgF,wCAA2BhF,EAAS,IAAI;IAChD;AACF;AAOA,SAAS+B,EAAgBD,CAAAA,EAAyB9B,CAAAA,CAEhC;IAChB,MAAMwB,IAAsB;QAC1B,MAAM;QACN,MAAAM;IACF;IACA,OAAA,CAAIA,MAAS,mBAAmBA,MAAS,uBAAA,KAAA,CACvCN,EAAK,MAAA,GAAS,CAAC,wCAACxB,EAAS,MAAA,CAAA,GAEpBwB;AACT;AAOA,SAASe,EAAoBV,CAAAA,EAAsB7B,CAAAA,CAE7B;IACpB,MAAMsC,IAAS,CAAC,wCAACtC,EAAS,MAAA;IAC1B,OAAO;QACL,MAAM;QACN,KAAA6B;QACA,GAAIS,KAAU;YAAC,QAAAA;QAAM,CACvB;;AACF;AAWA,SAAS2C,EAAqBC,CAAAA,EAAgBlF,CAAAA,CAIvB;IACrB,MAAMC,IAAO;QACX,MAAM,KAAA;QACN,eAAe,CAAA;QACf,GAAGD;IACL;IACA,IAAIC,EAAK,IAAA,KAAS,KAAA,KAAa,CAACkF,GAAiBlF,EAAK,IAAI,GACxD,MAAM,IAAI,MAAM,eAAwB,OAATA,EAAK,IAAI,EAAA,QAAwB;IAElE,OAAO;QACL,MAAM;QACN,QAAAiF;QACA,GAAIjF,EAAK,IAAA,IAAQ;YAAC,MAAMA,EAAK,IAAI;SAAA;QACjC,GAAIA,EAAK,aAAA,IAAiB;YAAC,eAAeA,EAAK;QAAa,CAAA;QAC5D,MAAM8E,wCAA+B/E,EAAS,IAAI;IACpD;AACF;AAMA,SAASW,EAAgByE,CAAAA,EAAkBpF,CAAAA,CAEzB;IAChB,MAAMC,IAAO;QACX,cAAc,CAAA;QACd,GAAGD;IACL;IACA,IAAIoF,IAAW,SAAU;QACvB,MAAMC,IAAMD,EAAS,QAAA,CAAS,EAAE;QAChC,IAAInF,EAAK,YAAA,EACPmF,IAAW,YACN;aAAA,MAAIA,IAAW,UACd,IAAI,MAAM,wCAA2C,GAAI,IAAPC,CAAG,EAAA,SAErD,IAAI,MAAM,8CAAiD,OAAHA,CAAG,EAAA,GAAI;IAEzE;IACA,OAAO;QACL,MAAM;QACN,OAAOD;IACT;AACF;AAQA,SAASlC,EAAqBlD,CAAAA,CAIP;IACrB,MAAMC,IAAO;QACX,MAAM;QACN,QAAQ,CAAA;QACR,GAAGD;IACL;IACA,OAAO;QACL,MAAM;QACN,MAAMC,EAAK,IAAA;QACX,QAAQA,EAAK,MAAA;QACb,MAAM+E,wCAA2BhF,EAAS,IAAI;IAChD;AACF;AAOA,SAAS8C,EAA0B2B,CAAAA,EAAoBC,CAAAA,CAA6C;IAClG,IAAIA,EAAI,KAAA,GAAQD,EAAI,KAAA,EAClB,MAAM,IAAI,MAAM,oCAAoC;IAEtD,OAAO;QACL,MAAM;QACN,KAAAA;QACA,KAAAC;IACF;AACF;AAoBA,SAASb,EAAmB/B,CAAAA,EAAuC9B,CAAAA,CAEvC;IAC1B,MAAM+C,IAAS,CAAC,wCAAC/C,EAAS,MAAA,GACpBwB,IAAgC;QACpC,MAAM;QACN,MAAAM;IACF;IACA,OAAA,CACEA,MAAS,WACTA,MAAS,SACTA,MAAS,aACTA,MAAS,WACTA,MAAS,MAAA,KAAA,CAETN,EAAK,MAAA,GAASuB,CAAAA,GAAAA,CAGdjB,MAAS,kBACRA,MAAS,aAAa,CAACiB,CAAAA,KAAAA,CAExBvB,EAAK,cAAA,GAAiB,CAAA,CAAA,GAEjBA;AACT;AAWA,SAAST,EAAgBe,CAAAA;QAAyB9B,qEAAwC,CAAC,EAAkB;IAC3G,IAAI8B,MAAS,QACX,OAAO;QACL,MAAM;QACN,MAAAA;IACF;IAEF,IAAIA,MAAS,SAGX,OAAO;QACL,MAAM;QACN,MAAAA;QACA,WAAOpC,yKAAAA,EAAeM,EAAQ,KAAK;IACrC;IAEF,MAAM,IAAI,MAAM,8BAAkC,OAAJ8B,CAAI,EAAA,EAAG;AACvD;AAKA,SAASR,EAAYgE,CAAAA,CAAkC;IACrD,OAAO;QACL,MAAM;QACN,GAAGA;IACL;AACF;AASA,SAASC,EAAYvF,CAAAA,CAIP;IACZ,MAAMwF,0CAASxF,EAAS,MAAA,EAClBsF,0CAAQtF,EAAS,KAAA;IACvB,IAAIwF,KAAUF,GACZ,MAAM,IAAI,MAAM,gCAAgC;IAElD,OAAO;QACL,MAAM;QACN,GAAIE,KAAU;YAAC,QAAAA;QAAM,CAAA;QACrB,GAAIF,KAAS;YAAC,OAAAA;QAAK,CAAA;QACnB,MAAMP,wCAA+B/E,EAAS,IAAI;IACpD;AACF;AAQA,SAASyF,EAA0BzF,CAAAA,CAIP;IAC1B,MAAMC,IAAO;QACX,QAAQ,CAAA;QACR,QAAQ,CAAA;QACR,GAAGD;IACL;IACA,OAAO;QACL,MAAM;QACN,MAAMC,EAAK,MAAA,GAAS,eAAe;QACnC,QAAQA,EAAK,MAAA;QACb,MAAM8E,wCAA+B/E,EAAS,IAAI;IACpD;AACF;AAQA,SAASiB,EACPa,CAAAA,EACA4D,CAAAA,EACAC,CAAAA,CACkB;IAClB,OAAO;QACL,MAAM;QACN,MAAA7D;QACA,KAAA4D;QACA,WAAWC;IACb;AACF;AAEA,SAAS/B,EAAiBgC,CAAAA,EAAc5F,CAAAA,CAEI;IAC1C,MAAM+C,IAAS,CAAC,wCAAC/C,EAAS,MAAA;IAC1B,IAAI,CAACR,0KAAAA,CAAgB,GAAA,CAAIoG,CAAI,GAC3B,MAAM,IAAI,MAAM,wBAA4B,OAAJA,CAAI,EAAA,EAAG;IAEjD,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAOA;QACP,QAAA7C;IACF;AACF;AASA,SAAS6B,EAAiB9C,CAAAA,EAA0B2C,CAAAA,EAAaC,CAAAA,EAAamB,CAAAA,CAAwC;IACpH,IAAIpB,IAAMC,GACR,MAAM,IAAI,MAAM,mCAAmC;IAErD,OAAO;QACL,MAAM;QACN,MAAA5C;QACA,KAAA2C;QACA,KAAAC;QACA,MAAAmB;IACF;AACF;AAOA,SAASxE,EAAYiE,CAAAA,EAAkBtF,CAAAA,CAEzB;IACZ,OAAO;QACL,MAAM;QACN,MAAM+E,wCAA+B/E,EAAS,IAAI;QAClD,OAAAsF;IACF;AACF;AAMA,SAAST,EAAiBhD,CAAAA,CAAsC;IAC9D,OAAO;QACL,MAAM;QACN,KAAAA;IACF;AACF;AAQA,SAAS8B,EAAsBiC,CAAAA,EAAc5F,CAAAA,CAAoF;;IAC/H,MAAMC,IAA+C;QACnD,QAAQ,CAAA;QACR,+BAA+B,CAAA;QAC/B,4BAA4B,CAAA;QAC5B,oBAAoB;QACpB,GAAGD;IACL;IACA,IAAIyD,6BAAaxD,EAAK,kBAAA,gFAAoB,GAAA,CAAIyD,EAAKkC,CAAI,CAAC;IACxD,IAAI,CAACnC,GAAAA;QACH,IAAIxD,EAAK,6BAAA,EACPwD,IAAaqC,GAA6BF,CAAI;aAAA,IAErC3F,EAAK,kBAAA,IAAsB,CAACA,EAAK,0BAAA,EAC1C,MAAM,IAAI,UAAMR,4JAAAA,sBAAiCmG,CAAI,IAAI;IAAA;IAG7D,OAAO;QACL,MAAM;QACN,MAAM;QACN,oCAAOnC,IAAcmC;QACrB,QAAQ3F,EAAK;IACf;AACF;AAMA,SAAS8D,QAAoD,CAAmG;UAApI,OAAAuB,CAAAA,EAAO,MAAAxD,CAAAA,EAAM,MAAA8D,CAAAA,EAAM,QAAA7C,CAAAA,EAAQ,QAAAmC,CAAM,EAAA,GAAlC;IACzB,OAAQpD,EAAM;QACZ,KAAK;YACH,OAAOgD,EAAsB,UAAU;QACzC,KAAK;YACH,OAAOS,EAAY;gBAAC,QAAQ,CAAA;YAAI,CAAC;QACnC,KAAK;YACH,OAAON,EAAqBC,GAAS;gBAAC,MAAAU;YAAI,CAAC;QAC7C,KAAK;YACH,OAAOL,EAAY;gBAAC,OAAAD;YAAK,CAAC;QAC5B,KAAK;QACL,KAAK;YACH,OAAOG,EAA0B;gBAC/B,QAAQ3D,MAAS;gBACjB,QAAAiB;YACF,CAAC;QACH;YACE,MAAM,IAAI,MAAM,0BAA8B,OAAJjB,CAAI,EAAA,EAAG;IACrD;AACF;AAEA,SAASiD,EAA+Bc,CAAAA,CAAuC;IAC7E,IAAIA,MAAS,KAAA,GACXA,IAAO;QAACrF,EAAkB,CAAC;KAAA;SAAA,IAClB,CAAC,MAAM,OAAA,CAAQqF,CAAI,KAAK,CAACA,EAAK,MAAA,IAAU,CAACA,EAAK,KAAA,EAAMrE,IAASA,EAAc,IAAA,KAAS,aAAa,GAC1G,MAAM,IAAI,MAAM,+DAA+D;IAEjF,OAAOqE;AACT;AAEA,SAASb,EAA2Ba,CAAAA,CAA4B;IAC9D,IAAIA,MAAS,KAAA,GACXA,IAAO,CAAC,CAAA;SAAA,IACC,CAAC,MAAM,OAAA,CAAQA,CAAI,KAAK,CAACA,EAAK,KAAA,EAAMrE,IAAQ,CAAC,CAAEA,EAAc,IAAI,GAC1E,MAAM,IAAI,MAAM,uCAAuC;IAEzD,OAAOqE;AACT;AAEA,SAASrB,EAAYhD,CAAAA,CAAqE;IACxF,OAAOA,EAAK,IAAA,KAAS,yBAAyBA,EAAK,IAAA,KAAS;AAC9D;AAEA,SAAS0C,EAAa1C,CAAAA,CAAsE;IAC1F,OAAOA,EAAK,IAAA,KAAS,yBAAyBA,EAAK,IAAA,KAAS;AAC9D;AAEA,SAAS2D,GAAiBS,CAAAA,CAAuB;IAG/C,OAAO,0CAA4B,IAAA,CAAKA,CAAI;AAC9C;AAEA,SAASE,GAA6BF,CAAAA,CAAsB;IAI1D,OAAOA,EACL,IAAA,CAAK,EACL,OAAA,CAAQ,WAAW,GAAG,EACtB,OAAA,CAAQ,yBAAyB,KAAK,EACtC,OAAA,CAAQ,eAAcG,IAAKA,CAAAA,CAAE,CAAC,CAAA,CAAE,WAAA,CAAY,IAAIA,EAAE,KAAA,CAAM,CAAC,EAAE,WAAA,CAAY,CAAC;AAC5E;AAKA,SAASrC,EAAKkC,CAAAA,CAAsB;IAClC,OAAOA,EAAK,OAAA,CAAQ,WAAW,EAAE,EAAE,WAAA,CAAY;AACjD;AAEA,SAASzC,EAAiC7C,CAAAA,EAAU0C,CAAAA,CAAyC;IAC3F,WAAOtD,yKAAAA,EACLY,GAGA,GACsB,8CADnB0C,EAAiB,IAAA,MAAS,eAAeA,EAAgB,KAAA,KAAU,KACpE,UAAU,UAAU,EAAA,iBACxB;AACF;AAEA,SAASoB,EAAwB9D,CAAAA,CAA0B;IACzD,WAAOZ,yKAAAA,EAAeY,GAAO,gBAAgB;AAC/C,CAEA,OAkCEwE,KAAA,sBACAtE,KAAA,kBACAuB,KAAA,gBACAQ,KAAA,oBACA0C,KAAA,qBACAtE,KAAA,gBACAuC,KAAA,qBACAJ,KAAA,0BACAe,KAAA,mBACA9C,KAAA,gBACAO,KAAA,YACAiE,KAAA,YACAE,KAAA,0BACAxE,KAAA,mBACA2C,KAAA,iBACAgB,KAAA,iBACAvD,KAAA,YACAwD,KAAA,iBACAlB,KAAA,sBACAhE,KAAA,aACAC,KAAA,uBACAC,KAAA,eACAC,KAAA,MACA4D,KAAA", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-parser/src/traverser/traverse.ts"], "sourcesContent": ["import type {AlternativeElementNode, AlternativeNode, CharacterClassElementNode, Node, ParentNode, RegexNode} from '../parser/parse.js';\nimport {throwIfNullish} from '../utils.js';\n\ntype ContainerElementNode =\n  // Used within the `body` container of any `AlternativeContainerNode`\n  AlternativeNode |\n  // Any node type used within the `body` container of an `AlternativeNode`\n  AlternativeElementNode |\n  // Any node type used within the `body` container of a `CharacterClassNode`\n  CharacterClassElementNode;\n\ntype Path<N = Node, Root = RegexNode> = {\n  // The current node being traversed\n  node: N;\n  // Parent node of the current node\n  parent: N extends RegexNode ? null : ParentNode;\n  // String property where the current node in held by the parent node, or numeric index in the\n  // parent's `container` array\n  key: N extends RegexNode ? null : number | string;\n  // Container array holding the current node in the parent node; `null` if the parent isn't a type\n  // that contains a list of nodes\n  container: N extends RegexNode ? null : Array<ContainerElementNode> | null;\n  // Starting node of the AST being traversed; usually a `RegexNode` but can be any node type if\n  // traversing from a midpoint\n  root: Root;\n  // Removes the current node; its kids won't be traversed\n  remove: () => void;\n  // Removes all siblings to the right of the current node, without traversing them; returns the\n  // removed nodes\n  removeAllNextSiblings: () => Array<Node>;\n  // Removes all siblings to the left of the current node, which have already been traversed;\n  // returns the removed nodes\n  removeAllPrevSiblings: () => Array<Node>;\n  // Replaces the current node with a new node; kids of the replaced node won't be traversed;\n  // optionally traverses the new node\n  replaceWith: (newNode: Node, options?: {traverse?: boolean}) => void;\n  // Replaces the current node with multiple new nodes; kids of the replaced node won't be\n  // traversed; optionally traverses the new nodes\n  replaceWithMultiple: (newNodes: Array<Node>, options?: {traverse?: boolean}) => void;\n  // Skips traversing kids of the current node\n  skip: () => void;\n};\n\n// `VisitorNodeFn() {…}` is shorthand for `VisitorNodeFn: {enter() {…}}`.\ntype Visitor<State extends object | null = null, Root extends Node = RegexNode> = {\n  [N in Node as N['type']]?: VisitorNodeFn<Path<N, Root>, State> | {\n    enter?: VisitorNodeFn<Path<N, Root>, State>;\n    exit?: VisitorNodeFn<Path<N, Root>, State>;\n  };\n} & {\n  '*'?: VisitorNodeFn<Path<Node, Root>, State> | {\n    enter?: VisitorNodeFn<Path<Node, Root>, State>;\n    exit?: VisitorNodeFn<Path<Node, Root>, State>;\n  };\n};\n\ntype VisitorNodeFn<P, State> = (path: P, state: State) => void;\n\n/**\nTraverses an AST and calls the provided `visitor`'s node function for each node. Returns the same\nobject, possibly modified.\n\nVisitor node functions can modify the AST in place and use methods on the `path` (provided as their\nfirst argument) to help modify the AST. Provided `state` is passed through to all visitor node\nfunctions as their second argument.\n\nVisitor node functions are called in the following order:\n1. `enter` function of the `'*'` node type (if any)\n2. `enter` function of the given node's type (if any)\n3. [The node's kids (if any) are traversed recursively, unless `skip` is called]\n4. `exit` function of the given node's type (if any)\n5. `exit` function of the `'*'` node type (if any)\n*/\nfunction traverse<State extends object | null = null, Root extends Node = RegexNode>(\n  root: Root,\n  visitor: Visitor<State, Root>,\n  state: State | null = null\n): Root {\n  function traverseArray(array: NonNullable<Path['container']>, parent: Path['parent']) {\n    for (let i = 0; i < array.length; i++) {\n      const keyShift = traverseNode(array[i], parent, i, array);\n      i = Math.max(-1, i + keyShift);\n    }\n  }\n  function traverseNode(\n    node: Path['node'],\n    parent: Path['parent'] = null,\n    key: Path['key'] = null,\n    container: Path['container'] = null\n  ): number {\n    let keyShift = 0;\n    let skipTraversingKidsOfPath = false;\n    const path: Path = {\n      node,\n      parent,\n      key,\n      container,\n      root: root as RegexNode,\n      remove() {\n        arrayContainer(container).splice(Math.max(0, numericKey(key) + keyShift), 1);\n        keyShift--;\n        skipTraversingKidsOfPath = true;\n      },\n      removeAllNextSiblings() {\n        return arrayContainer(container).splice(numericKey(key) + 1);\n      },\n      removeAllPrevSiblings() {\n        const shifted = numericKey(key) + keyShift;\n        keyShift -= shifted;\n        return arrayContainer(container).splice(0, Math.max(0, shifted));\n      },\n      replaceWith(newNode, options = {}) {\n        const traverseNew = !!options.traverse;\n        if (container) {\n          container[Math.max(0, numericKey(key) + keyShift)] = newNode as ContainerElementNode;\n        } else {\n          // `key` will be one of:\n          // - For `CharacterClassRangeNode`: 'min', 'max'\n          // - For `QuantifierNode`: 'body'\n          // - For `RegexNode`: 'flags'\n          // @ts-expect-error\n          throwIfNullish(parent, `Can't replace root node`)[key as string] = newNode;\n        }\n        if (traverseNew) {\n          traverseNode(newNode, parent, key, container);\n        }\n        skipTraversingKidsOfPath = true;\n      },\n      replaceWithMultiple(newNodes, options = {}) {\n        const traverseNew = !!options.traverse;\n        arrayContainer(container).splice(Math.max(0, numericKey(key) + keyShift), 1, ...newNodes);\n        keyShift += newNodes.length - 1;\n        if (traverseNew) {\n          let keyShiftInLoop = 0;\n          for (let i = 0; i < newNodes.length; i++) {\n            keyShiftInLoop += traverseNode(newNodes[i], parent, numericKey(key) + i + keyShiftInLoop, container);\n          }\n        }\n        skipTraversingKidsOfPath = true;\n      },\n      skip() {\n        skipTraversingKidsOfPath = true;\n      },\n    };\n\n    const {type} = node;\n    const anyTypeVisitor = visitor['*'];\n    const thisTypeVisitor = visitor[type];\n    const enterAllFn = typeof anyTypeVisitor === 'function' ? anyTypeVisitor : anyTypeVisitor?.enter;\n    const enterThisFn = typeof thisTypeVisitor === 'function' ? thisTypeVisitor : thisTypeVisitor?.enter;\n    // Type args are too complex to avoid TS errors here, but `VisitorNodeFn`s get correct types\n    // @ts-expect-error\n    enterAllFn?.(path, state);\n    // @ts-expect-error\n    enterThisFn?.(path, state);\n\n    if (!skipTraversingKidsOfPath) {\n      switch (type) {\n        case 'AbsenceFunction':\n        case 'CapturingGroup':\n        case 'Group':\n          traverseArray(node.body, node);\n          break;\n        case 'Alternative':\n        case 'CharacterClass':\n          traverseArray(node.body, node);\n          break;\n        case 'Assertion':\n        case 'Backreference':\n        case 'Character':\n        case 'CharacterSet':\n        case 'Directive':\n        case 'Flags':\n        case 'NamedCallout':\n        case 'Subroutine':\n          break;\n        case 'CharacterClassRange':\n          traverseNode(node.min, node, 'min');\n          traverseNode(node.max, node, 'max');\n          break;\n        case 'LookaroundAssertion':\n          traverseArray(node.body, node);\n          break;\n        case 'Quantifier':\n          traverseNode(node.body, node, 'body');\n          break;\n        case 'Regex':\n          traverseArray(node.body, node);\n          traverseNode(node.flags, node, 'flags');\n          break;\n        default:\n          throw new Error(`Unexpected node type \"${type}\"`);\n      }\n    }\n\n    // @ts-expect-error\n    (thisTypeVisitor as Exclude<typeof thisTypeVisitor, Function>)?.exit?.(path, state);\n    // @ts-expect-error\n    (anyTypeVisitor as Exclude<typeof anyTypeVisitor, Function>)?.exit?.(path, state);\n    return keyShift;\n  }\n  traverseNode(root);\n  return root;\n}\n\nfunction arrayContainer(value: unknown): Array<Node> {\n  if (!Array.isArray(value)) {\n    throw new Error('Container expected');\n  }\n  return value;\n}\n\nfunction numericKey(value: unknown): number {\n  if (typeof value !== 'number') {\n    throw new Error('Numeric key expected');\n  }\n  return value;\n}\n\nexport {\n  type Path,\n  type Visitor,\n  traverse,\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traverse", "root", "visitor", "state", "traverseArray", "array", "parent", "i", "keyShift", "traverseNode", "node", "key", "container", "skipTraversingKidsOfPath", "path", "arrayContainer", "numericKey", "shifted", "newNode", "options", "traverseNew", "newNodes", "keyShiftInLoop", "type", "anyTypeVisitor", "thisTypeVisitor", "enterAllFn", "enterThisFn", "value"], "mappings": ";;;;AACA,OAAQ,kBAAAA,MAAqB;;;AAwE7B,SAASC,EACPC,CAAAA,EACAC,CAAAA;YACAC,iEAAsB,KAChB;IACN,SAASC,EAAcC,CAAAA,EAAuCC,CAAAA,CAAwB;QACpF,IAAA,IAASC,IAAI,GAAGA,IAAIF,EAAM,MAAA,EAAQE,IAAK;YACrC,MAAMC,IAAWC,EAAaJ,CAAAA,CAAME,CAAC,CAAA,EAAGD,GAAQC,GAAGF,CAAK;YACxDE,IAAI,KAAK,GAAA,CAAI,CAAA,GAAIA,IAAIC,CAAQ;QAC/B;IACF;IACA,SAASC,EACPC,CAAAA;gBACAJ,iEAAyB,UACzBK,iEAAmB,UACnBC,iEAA+B,KACvB;YA2GPa;QA1GD,IAAIjB,IAAW,GACXK,IAA2B,CAAA;QAC/B,MAAMC,IAAa;YACjB,MAAAJ;YACA,QAAAJ;YACA,KAAAK;YACA,WAAAC;YACA,MAAMX;YACN,QAAS;gBACPc,EAAeH,CAAS,EAAE,MAAA,CAAO,KAAK,GAAA,CAAI,GAAGI,EAAWL,CAAG,IAAIH,CAAQ,GAAG,CAAC,GAC3EA,KACAK,IAA2B,CAAA;YAC7B;YACA,uBAAwB;gBACtB,OAAOE,EAAeH,CAAS,EAAE,MAAA,CAAOI,EAAWL,CAAG,IAAI,CAAC;YAC7D;YACA,uBAAwB;gBACtB,MAAMM,IAAUD,EAAWL,CAAG,IAAIH;gBAClC,OAAAA,KAAYS,GACLF,EAAeH,CAAS,EAAE,MAAA,CAAO,GAAG,KAAK,GAAA,CAAI,GAAGK,CAAO,CAAC;YACjE;YACA,aAAYC,CAAAA;wBAASC,iEAAU,CAAC,EAAG;gBACjC,MAAMC,IAAc,CAAC,CAACD,EAAQ,QAAA;gBAC1BP,IACFA,CAAAA,CAAU,KAAK,GAAA,CAAI,GAAGI,EAAWL,CAAG,IAAIH,CAAQ,CAAC,CAAA,GAAIU,QAOrDnB,yKAAAA,EAAeO,GAAQ,yBAAyB,CAAA,CAAEK,CAAa,CAAA,GAAIO,GAEjEE,KACFX,EAAaS,GAASZ,GAAQK,GAAKC,CAAS,GAE9CC,IAA2B,CAAA;YAC7B;YACA,qBAAoBQ,CAAAA;wBAAUF,iEAAU,CAAC,EAAG;gBAC1C,MAAMC,IAAc,CAAC,CAACD,EAAQ,QAAA;gBAG9B,IAFAJ,EAAeH,CAAS,EAAE,MAAA,CAAO,KAAK,GAAA,CAAI,GAAGI,EAAWL,CAAG,IAAIH,CAAQ,GAAG,EAAG,IAAGa,CAAQ,GACxFb,KAAYa,EAAS,MAAA,GAAS,GAC1BD,GAAa;oBACf,IAAIE,IAAiB;oBACrB,IAAA,IAASf,IAAI,GAAGA,IAAIc,EAAS,MAAA,EAAQd,IACnCe,KAAkBb,EAAaY,CAAAA,CAASd,CAAC,CAAA,EAAGD,GAAQU,EAAWL,CAAG,IAAIJ,IAAIe,GAAgBV,CAAS;gBAEvG;gBACAC,IAA2B,CAAA;YAC7B;YACA,MAAO;gBACLA,IAA2B,CAAA;YAC7B;QACF,GAEM,EAAC,MAAAU,CAAI,EAAA,GAAIb,GACTc,IAAiBtB,CAAAA,CAAQ,GAAG,CAAA,EAC5BuB,IAAkBvB,CAAAA,CAAQqB,CAAI,CAAA,EAC9BG,IAAa,OAAOF,KAAmB,aAAaA,0CAAiBA,EAAgB,KAAA,EACrFG,IAAc,OAAOF,KAAoB,aAAaA,IAAkBA,wCAAiB,KAAA;QAO/F,0CAJAC,EAAaZ,GAAMX,CAAK,yCAExBwB,EAAcb,GAAMX,CAAK,GAErB,CAACU,GACH,OAAQU,EAAM;YACZ,KAAK;YACL,KAAK;YACL,KAAK;gBACHnB,EAAcM,EAAK,IAAA,EAAMA,CAAI;gBAC7B;YACF,KAAK;YACL,KAAK;gBACHN,EAAcM,EAAK,IAAA,EAAMA,CAAI;gBAC7B;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF,KAAK;gBACHD,EAAaC,EAAK,GAAA,EAAKA,GAAM,KAAK,GAClCD,EAAaC,EAAK,GAAA,EAAKA,GAAM,KAAK;gBAClC;YACF,KAAK;gBACHN,EAAcM,EAAK,IAAA,EAAMA,CAAI;gBAC7B;YACF,KAAK;gBACHD,EAAaC,EAAK,IAAA,EAAMA,GAAM,MAAM;gBACpC;YACF,KAAK;gBACHN,EAAcM,EAAK,IAAA,EAAMA,CAAI,GAC7BD,EAAaC,EAAK,KAAA,EAAOA,GAAM,OAAO;gBACtC;YACF;gBACE,MAAM,IAAI,MAAM,yBAA6B,OAAJa,CAAI,EAAA,EAAG;QACpD;QAIF,0DAAgE,IAAA,4DAAOT,GAAMX,CAAK,sDAEpB,IAAA,yDAA7DqB,GAAoEV,GAAMX,CAAK,GACzEK;IACT;IACA,OAAAC,EAAaR,CAAI,GACVA;AACT;AAEA,SAASc,EAAea,CAAAA,CAA6B;IACnD,IAAI,CAAC,MAAM,OAAA,CAAQA,CAAK,GACtB,MAAM,IAAI,MAAM,oBAAoB;IAEtC,OAAOA;AACT;AAEA,SAASZ,EAAWY,CAAAA,CAAwB;IAC1C,IAAI,OAAOA,KAAU,UACnB,MAAM,IAAI,MAAM,sBAAsB;IAExC,OAAOA;AACT,CAEA,OAGE5B,KAAA", "debugId": null}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex/src/utils-internals.js"], "sourcesContent": ["// Separating some utils for improved tree shaking of the `./internals` export\n\nconst noncapturingDelim = String.raw`\\(\\?(?:[:=!>A-Za-z\\-]|<[=!]|\\(DEFINE\\))`;\n\n/**\nUpdates the array in place by incrementing each value greater than or equal to the threshold.\n@param {Array<number>} arr\n@param {number} threshold\n*/\nfunction incrementIfAtLeast(arr, threshold) {\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] >= threshold) {\n      arr[i]++;\n    }\n  }\n}\n\n/**\n@param {string} str\n@param {number} pos\n@param {string} oldValue\n@param {string} newValue\n@returns {string}\n*/\nfunction spliceStr(str, pos, oldValue, newValue) {\n  return str.slice(0, pos) + newValue + str.slice(pos + oldValue.length);\n}\n\nexport {\n  incrementIfAtLeast,\n  noncapturingDelim,\n  spliceStr,\n};\n"], "names": [], "mappings": "AAAA,8EAA8E;;;;;;;;;;;;;;;;;;;;;;AAE9E,MAAM,oBAAoB,OAAO,GAAG;AAEpC;;;;AAIA,GACA,SAAS,mBAAmB,GAAG,EAAE,SAAS;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,GAAG,CAAC,EAAE,IAAI,WAAW;YACvB,GAAG,CAAC,EAAE;QACR;IACF;AACF;AAEA;;;;;;AAMA,GACA,SAAS,UAAU,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ;IAC7C,OAAO,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,IAAI,KAAK,CAAC,MAAM,SAAS,MAAM;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex-utilities/src/index.js"], "sourcesContent": ["// Constant properties for tracking regex syntax context\nexport const Context = Object.freeze({\n  DEFAULT: 'DEFAULT',\n  CHAR_CLASS: 'CHAR_CLASS',\n});\n\n/**\nReplaces all unescaped instances of a regex pattern in the given context, using a replacement\nstring or callback.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {string | (match: RegExpExecArray, details: {\n  context: 'DEFAULT' | 'CHAR_CLASS';\n  negated: boolean;\n}) => string} replacement\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {string} Updated expression\n@example\nconst str = '.\\\\.\\\\\\\\.[[\\\\.].].';\nreplaceUnescaped(str, '\\\\.', '@');\n// → '@\\\\.\\\\\\\\@[[\\\\.]@]@'\nreplaceUnescaped(str, '\\\\.', '@', Context.DEFAULT);\n// → '@\\\\.\\\\\\\\@[[\\\\.].]@'\nreplaceUnescaped(str, '\\\\.', '@', Context.CHAR_CLASS);\n// → '.\\\\.\\\\\\\\.[[\\\\.]@].'\n*/\nexport function replaceUnescaped(expression, needle, replacement, context) {\n  const re = new RegExp(String.raw`${needle}|(?<$skip>\\[\\^?|\\\\?.)`, 'gsu');\n  const negated = [false];\n  let numCharClassesOpen = 0;\n  let result = '';\n  for (const match of expression.matchAll(re)) {\n    const {0: m, groups: {$skip}} = match;\n    if (!$skip && (!context || (context === Context.DEFAULT) === !numCharClassesOpen)) {\n      if (replacement instanceof Function) {\n        result += replacement(match, {\n          context: numCharClassesOpen ? Context.CHAR_CLASS : Context.DEFAULT,\n          negated: negated[negated.length - 1],\n        });\n      } else {\n        result += replacement;\n      }\n      continue;\n    }\n    if (m[0] === '[') {\n      numCharClassesOpen++;\n      negated.push(m[1] === '^');\n    } else if (m === ']' && numCharClassesOpen) {\n      numCharClassesOpen--;\n      negated.pop();\n    }\n    result += m;\n  }\n  return result;\n}\n\n/**\nRuns a callback for each unescaped instance of a regex pattern in the given context.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {(match: RegExpExecArray, details: {\n  context: 'DEFAULT' | 'CHAR_CLASS';\n  negated: boolean;\n}) => void} callback\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n*/\nexport function forEachUnescaped(expression, needle, callback, context) {\n  // Do this the easy way\n  replaceUnescaped(expression, needle, callback, context);\n}\n\n/**\nReturns a match object for the first unescaped instance of a regex pattern in the given context, or\n`null`.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {number} [pos] Offset to start the search\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {RegExpExecArray | null}\n*/\nexport function execUnescaped(expression, needle, pos = 0, context) {\n  // Quick partial test; avoid the loop if not needed\n  if (!(new RegExp(needle, 'su').test(expression))) {\n    return null;\n  }\n  const re = new RegExp(`${needle}|(?<$skip>\\\\\\\\?.)`, 'gsu');\n  re.lastIndex = pos;\n  let numCharClassesOpen = 0;\n  let match;\n  while (match = re.exec(expression)) {\n    const {0: m, groups: {$skip}} = match;\n    if (!$skip && (!context || (context === Context.DEFAULT) === !numCharClassesOpen)) {\n      return match;\n    }\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (m === ']' && numCharClassesOpen) {\n      numCharClassesOpen--;\n    }\n    // Avoid an infinite loop on zero-length matches\n    if (re.lastIndex == match.index) {\n      re.lastIndex++;\n    }\n  }\n  return null;\n}\n\n/**\nChecks whether an unescaped instance of a regex pattern appears in the given context.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {boolean} Whether the pattern was found\n*/\nexport function hasUnescaped(expression, needle, context) {\n  // Do this the easy way\n  return !!execUnescaped(expression, needle, 0, context);\n}\n\n/**\nExtracts the full contents of a group (subpattern) from the given expression, accounting for\nescaped characters, nested groups, and character classes. The group is identified by the position\nwhere its contents start (the string index just after the group's opening delimiter). Returns the\nrest of the string if the group is unclosed.\n\nAssumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {number} contentsStartPos\n@returns {string}\n*/\nexport function getGroupContents(expression, contentsStartPos) {\n  const token = /\\\\?./gsu;\n  token.lastIndex = contentsStartPos;\n  let contentsEndPos = expression.length;\n  let numCharClassesOpen = 0;\n  // Starting search within an open group, after the group's opening\n  let numGroupsOpen = 1;\n  let match;\n  while (match = token.exec(expression)) {\n    const [m] = match;\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n      if (m === '(') {\n        numGroupsOpen++;\n      } else if (m === ')') {\n        numGroupsOpen--;\n        if (!numGroupsOpen) {\n          contentsEndPos = match.index;\n          break;\n        }\n      }\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n  return expression.slice(contentsStartPos, contentsEndPos);\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACjD,MAAM,UAAU,OAAO,MAAM,CAAC;IACnC,SAAS;IACT,YAAY;AACd;AAyBO,SAAS,iBAAiB,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO;IACvE,MAAM,KAAK,IAAI,OAAO,OAAO,GAAG,oBAAG,SAA+B;IAClE,MAAM,UAAU;QAAC;KAAM;IACvB,IAAI,qBAAqB;IACzB,IAAI,SAAS;IACb,KAAK,MAAM,SAAS,WAAW,QAAQ,CAAC,IAAK;QAC3C,MAAM,EAAC,GAAG,CAAC,EAAE,QAAQ,EAAC,KAAK,EAAC,EAAC,GAAG;QAChC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,AAAC,YAAY,QAAQ,OAAO,KAAM,CAAC,kBAAkB,GAAG;YACjF,IAAI,uBAAuB,UAAU;gBACnC,UAAU,YAAY,OAAO;oBAC3B,SAAS,qBAAqB,QAAQ,UAAU,GAAG,QAAQ,OAAO;oBAClE,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;gBACtC;YACF,OAAO;gBACL,UAAU;YACZ;YACA;QACF;QACA,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;YAChB;YACA,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;QACxB,OAAO,IAAI,MAAM,OAAO,oBAAoB;YAC1C;YACA,QAAQ,GAAG;QACb;QACA,UAAU;IACZ;IACA,OAAO;AACT;AAeO,SAAS,iBAAiB,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;IACpE,uBAAuB;IACvB,iBAAiB,YAAY,QAAQ,UAAU;AACjD;AAcO,SAAS,cAAc,UAAU,EAAE,MAAM;QAAE,MAAA,iEAAM,GAAG;IACzD,mDAAmD;IACnD,IAAI,CAAE,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,aAAc;QAChD,OAAO;IACT;IACA,MAAM,KAAK,IAAI,OAAO,AAAC,GAAS,OAAP,QAAO,sBAAoB;IACpD,GAAG,SAAS,GAAG;IACf,IAAI,qBAAqB;IACzB,IAAI;IACJ,MAAO,QAAQ,GAAG,IAAI,CAAC,YAAa;QAClC,MAAM,EAAC,GAAG,CAAC,EAAE,QAAQ,EAAC,KAAK,EAAC,EAAC,GAAG;QAChC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,AAAC,YAAY,QAAQ,OAAO,KAAM,CAAC,kBAAkB,GAAG;YACjF,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb;QACF,OAAO,IAAI,MAAM,OAAO,oBAAoB;YAC1C;QACF;QACA,gDAAgD;QAChD,IAAI,GAAG,SAAS,IAAI,MAAM,KAAK,EAAE;YAC/B,GAAG,SAAS;QACd;IACF;IACA,OAAO;AACT;AAYO,SAAS,aAAa,UAAU,EAAE,MAAM,EAAE,OAAO;IACtD,uBAAuB;IACvB,OAAO,CAAC,CAAC,cAAc,YAAY,QAAQ,GAAG;AAChD;AAaO,SAAS,iBAAiB,UAAU,EAAE,gBAAgB;IAC3D,MAAM,QAAQ;IACd,MAAM,SAAS,GAAG;IAClB,IAAI,iBAAiB,WAAW,MAAM;IACtC,IAAI,qBAAqB;IACzB,kEAAkE;IAClE,IAAI,gBAAgB;IACpB,IAAI;IACJ,MAAO,QAAQ,MAAM,IAAI,CAAC,YAAa;QACrC,MAAM,CAAC,EAAE,GAAG;QACZ,IAAI,MAAM,KAAK;YACb;QACF,OAAO,IAAI,CAAC,oBAAoB;YAC9B,IAAI,MAAM,KAAK;gBACb;YACF,OAAO,IAAI,MAAM,KAAK;gBACpB;gBACA,IAAI,CAAC,eAAe;oBAClB,iBAAiB,MAAM,KAAK;oBAC5B;gBACF;YACF;QACF,OAAO,IAAI,MAAM,KAAK;YACpB;QACF;IACF;IACA,OAAO,WAAW,KAAK,CAAC,kBAAkB;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex/src/atomic.js"], "sourcesContent": ["import {incrementIfAtLeast, noncapturingDelim, spliceStr} from './utils-internals.js';\nimport {Context, replaceUnescaped} from 'regex-utilities';\n\nconst atomicPluginToken = new RegExp(String.raw`(?<noncapturingStart>${noncapturingDelim})|(?<capturingStart>\\((?:\\?<[^>]+>)?)|\\\\?.`, 'gsu');\n\n/**\nApply transformations for atomic groups: `(?>…)`.\n@param {string} expression\n@param {import('./regex.js').PluginData} [data]\n@returns {Required<import('./regex.js').PluginResult>}\n*/\nfunction atomic(expression, data) {\n  const hiddenCaptures = data?.hiddenCaptures ?? [];\n  // Capture transfer is used by <github.com/slevithan/oniguruma-to-es>\n  let captureTransfers = data?.captureTransfers ?? new Map();\n  if (!/\\(\\?>/.test(expression)) {\n    return {\n      pattern: expression,\n      captureTransfers,\n      hiddenCaptures,\n    };\n  }\n\n  const aGDelim = '(?>';\n  const emulatedAGDelim = '(?:(?=(';\n  const captureNumMap = [0];\n  const addedHiddenCaptures = [];\n  let numCapturesBeforeAG = 0;\n  let numAGs = 0;\n  let aGPos = NaN;\n  let hasProcessedAG;\n  do {\n    hasProcessedAG = false;\n    let numCharClassesOpen = 0;\n    let numGroupsOpenInAG = 0;\n    let inAG = false;\n    let match;\n    atomicPluginToken.lastIndex = Number.isNaN(aGPos) ? 0 : aGPos + emulatedAGDelim.length;\n    while (match = atomicPluginToken.exec(expression)) {\n      const {0: m, index, groups: {capturingStart, noncapturingStart}} = match;\n      if (m === '[') {\n        numCharClassesOpen++;\n      } else if (!numCharClassesOpen) {\n\n        if (m === aGDelim && !inAG) {\n          aGPos = index;\n          inAG = true;\n        } else if (inAG && noncapturingStart) {\n          numGroupsOpenInAG++;\n        } else if (capturingStart) {\n          if (inAG) {\n            numGroupsOpenInAG++;\n          } else {\n            numCapturesBeforeAG++;\n            captureNumMap.push(numCapturesBeforeAG + numAGs);\n          }\n        } else if (m === ')' && inAG) {\n          if (!numGroupsOpenInAG) {\n            numAGs++;\n            const addedCaptureNum = numCapturesBeforeAG + numAGs;\n            // Replace `expression` and use `<$$N>` as a temporary wrapper for the backref so it\n            // can avoid backref renumbering afterward. Wrap the whole substitution (including the\n            // lookahead and following backref) in a noncapturing group to handle following\n            // quantifiers and literal digits\n            expression = `${expression.slice(0, aGPos)}${emulatedAGDelim}${\n                expression.slice(aGPos + aGDelim.length, index)\n              }))<$$${addedCaptureNum}>)${expression.slice(index + 1)}`;\n            hasProcessedAG = true;\n            addedHiddenCaptures.push(addedCaptureNum);\n            incrementIfAtLeast(hiddenCaptures, addedCaptureNum);\n            if (captureTransfers.size) {\n              const newCaptureTransfers = new Map();\n              captureTransfers.forEach((from, to) => {\n                newCaptureTransfers.set(\n                  to >= addedCaptureNum ? to + 1 : to,\n                  from.map(f => f >= addedCaptureNum ? f + 1 : f)\n                );\n              });\n              captureTransfers = newCaptureTransfers;\n            }\n            break;\n          }\n          numGroupsOpenInAG--;\n        }\n\n      } else if (m === ']') {\n        numCharClassesOpen--;\n      }\n    }\n  // Start over from the beginning of the atomic group's contents, in case the processed group\n  // contains additional atomic groups\n  } while (hasProcessedAG);\n\n  hiddenCaptures.push(...addedHiddenCaptures);\n\n  // Second pass to adjust numbered backrefs\n  expression = replaceUnescaped(\n    expression,\n    String.raw`\\\\(?<backrefNum>[1-9]\\d*)|<\\$\\$(?<wrappedBackrefNum>\\d+)>`,\n    ({0: m, groups: {backrefNum, wrappedBackrefNum}}) => {\n      if (backrefNum) {\n        const bNum = +backrefNum;\n        if (bNum > captureNumMap.length - 1) {\n          throw new Error(`Backref \"${m}\" greater than number of captures`);\n        }\n        return `\\\\${captureNumMap[bNum]}`;\n      }\n      return `\\\\${wrappedBackrefNum}`;\n    },\n    Context.DEFAULT\n  );\n\n  return {\n    pattern: expression,\n    captureTransfers,\n    hiddenCaptures,\n  };\n}\n\nconst baseQuantifier = String.raw`(?:[?*+]|\\{\\d+(?:,\\d*)?\\})`;\n// Complete tokenizer for base syntax; doesn't (need to) know about character-class-only syntax\nconst possessivePluginToken = new RegExp(String.raw`\n\\\\(?: \\d+\n  | c[A-Za-z]\n  | [gk]<[^>]+>\n  | [pPu]\\{[^\\}]+\\}\n  | u[A-Fa-f\\d]{4}\n  | x[A-Fa-f\\d]{2}\n  )\n| \\((?: \\? (?: [:=!>]\n  | <(?:[=!]|[^>]+>)\n  | [A-Za-z\\-]+:\n  | \\(DEFINE\\)\n  ))?\n| (?<qBase>${baseQuantifier})(?<qMod>[?+]?)(?<invalidQ>[?*+\\{]?)\n| \\\\?.\n`.replace(/\\s+/g, ''), 'gsu');\n\n/**\nTransform posessive quantifiers into atomic groups. The posessessive quantifiers are:\n`?+`, `*+`, `++`, `{N}+`, `{N,}+`, `{N,N}+`.\nThis follows Java, PCRE, Perl, and Python.\nPossessive quantifiers in Oniguruma and Onigmo are only: `?+`, `*+`, `++`.\n@param {string} expression\n@returns {import('./regex.js').PluginResult}\n*/\nfunction possessive(expression) {\n  if (!(new RegExp(`${baseQuantifier}\\\\+`).test(expression))) {\n    return {\n      pattern: expression,\n    };\n  }\n\n  const openGroupIndices = [];\n  let lastGroupIndex = null;\n  let lastCharClassIndex = null;\n  let lastToken = '';\n  let numCharClassesOpen = 0;\n  let match;\n  possessivePluginToken.lastIndex = 0;\n  while (match = possessivePluginToken.exec(expression)) {\n    const {0: m, index, groups: {qBase, qMod, invalidQ}} = match;\n    if (m === '[') {\n      if (!numCharClassesOpen) {\n        lastCharClassIndex = index;\n      }\n      numCharClassesOpen++;\n    } else if (m === ']') {\n      if (numCharClassesOpen) {\n        numCharClassesOpen--;\n      // Unmatched `]`\n      } else {\n        lastCharClassIndex = null;\n      }\n    } else if (!numCharClassesOpen) {\n\n      if (qMod === '+' && lastToken && !lastToken.startsWith('(')) {\n        // Invalid following quantifier would become valid via the wrapping group\n        if (invalidQ) {\n          throw new Error(`Invalid quantifier \"${m}\"`);\n        }\n        let charsAdded = -1; // -1 for removed trailing `+`\n        // Possessivizing fixed repetition quantifiers like `{2}` does't change their behavior, so\n        // avoid doing so (convert them to greedy)\n        if (/^\\{\\d+\\}$/.test(qBase)) {\n          expression = spliceStr(expression, index + qBase.length, qMod, '');\n        } else {\n          if (lastToken === ')' || lastToken === ']') {\n            const nodeIndex = lastToken === ')' ? lastGroupIndex : lastCharClassIndex;\n            // Unmatched `)` would break out of the wrapping group and mess with handling.\n            // Unmatched `]` wouldn't be a problem, but it's unnecessary to have dedicated support\n            // for unescaped `]++` since this won't work with flag u or v anyway\n            if (nodeIndex === null) {\n              throw new Error(`Invalid unmatched \"${lastToken}\"`);\n            }\n            expression = `${expression.slice(0, nodeIndex)}(?>${expression.slice(nodeIndex, index)}${qBase})${expression.slice(index + m.length)}`;\n          } else {\n            expression = `${expression.slice(0, index - lastToken.length)}(?>${lastToken}${qBase})${expression.slice(index + m.length)}`;\n          }\n          charsAdded += 4; // `(?>)`\n        }\n        possessivePluginToken.lastIndex += charsAdded;\n      } else if (m[0] === '(') {\n        openGroupIndices.push(index);\n      } else if (m === ')') {\n        lastGroupIndex = openGroupIndices.length ? openGroupIndices.pop() : null;\n      }\n\n    }\n    lastToken = m;\n  }\n\n  return {\n    pattern: expression,\n  };\n}\n\nexport {\n  atomic,\n  possessive,\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,IAAI,OAAO,OAAO,GAAG,oBAAwB,0KAAiB,GAA8C;AAEtI;;;;;AAKA,GACA,SAAS,OAAO,UAAU,EAAE,IAAI;QACP;IAAvB,MAAM,iBAAiB,CAAA,uBAAA,iBAAA,2BAAA,KAAM,cAAc,cAApB,kCAAA,uBAAwB,EAAE;QAE1B;IADvB,qEAAqE;IACrE,IAAI,mBAAmB,CAAA,yBAAA,iBAAA,2BAAA,KAAM,gBAAgB,cAAtB,oCAAA,yBAA0B,IAAI;IACrD,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa;QAC7B,OAAO;YACL,SAAS;YACT;YACA;QACF;IACF;IAEA,MAAM,UAAU;IAChB,MAAM,kBAAkB;IACxB,MAAM,gBAAgB;QAAC;KAAE;IACzB,MAAM,sBAAsB,EAAE;IAC9B,IAAI,sBAAsB;IAC1B,IAAI,SAAS;IACb,IAAI,QAAQ;IACZ,IAAI;IACJ,GAAG;QACD,iBAAiB;QACjB,IAAI,qBAAqB;QACzB,IAAI,oBAAoB;QACxB,IAAI,OAAO;QACX,IAAI;QACJ,kBAAkB,SAAS,GAAG,OAAO,KAAK,CAAC,SAAS,IAAI,QAAQ,gBAAgB,MAAM;QACtF,MAAO,QAAQ,kBAAkB,IAAI,CAAC,YAAa;YACjD,MAAM,EAAC,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAC,cAAc,EAAE,iBAAiB,EAAC,EAAC,GAAG;YACnE,IAAI,MAAM,KAAK;gBACb;YACF,OAAO,IAAI,CAAC,oBAAoB;gBAE9B,IAAI,MAAM,WAAW,CAAC,MAAM;oBAC1B,QAAQ;oBACR,OAAO;gBACT,OAAO,IAAI,QAAQ,mBAAmB;oBACpC;gBACF,OAAO,IAAI,gBAAgB;oBACzB,IAAI,MAAM;wBACR;oBACF,OAAO;wBACL;wBACA,cAAc,IAAI,CAAC,sBAAsB;oBAC3C;gBACF,OAAO,IAAI,MAAM,OAAO,MAAM;oBAC5B,IAAI,CAAC,mBAAmB;wBACtB;wBACA,MAAM,kBAAkB,sBAAsB;wBAC9C,oFAAoF;wBACpF,sFAAsF;wBACtF,+EAA+E;wBAC/E,iCAAiC;wBACjC,aAAa,AAAC,GAA+B,OAA7B,WAAW,KAAK,CAAC,GAAG,QAChC,OADyC,iBAEnC,OADN,WAAW,KAAK,CAAC,QAAQ,QAAQ,MAAM,EAAE,QAC1C,SAA2B,OAApB,iBAAgB,MAAgC,OAA5B,WAAW,KAAK,CAAC,QAAQ;wBACvD,iBAAiB;wBACjB,oBAAoB,IAAI,CAAC;wBACzB,IAAA,2KAAkB,EAAC,gBAAgB;wBACnC,IAAI,iBAAiB,IAAI,EAAE;4BACzB,MAAM,sBAAsB,IAAI;4BAChC,iBAAiB,OAAO,CAAC,CAAC,MAAM;gCAC9B,oBAAoB,GAAG,CACrB,MAAM,kBAAkB,KAAK,IAAI,IACjC,KAAK,GAAG,CAAC,CAAA,IAAK,KAAK,kBAAkB,IAAI,IAAI;4BAEjD;4BACA,mBAAmB;wBACrB;wBACA;oBACF;oBACA;gBACF;YAEF,OAAO,IAAI,MAAM,KAAK;gBACpB;YACF;QACF;IACF,4FAA4F;IAC5F,oCAAoC;IACpC,QAAS,eAAgB;IAEzB,eAAe,IAAI,IAAI;IAEvB,0CAA0C;IAC1C,aAAa,IAAA,yKAAgB,EAC3B,YACA,OAAO,GAAG,sBACV;YAAC,EAAC,GAAG,CAAC,EAAE,QAAQ,EAAC,UAAU,EAAE,iBAAiB,EAAC,EAAC;QAC9C,IAAI,YAAY;YACd,MAAM,OAAO,CAAC;YACd,IAAI,OAAO,cAAc,MAAM,GAAG,GAAG;gBACnC,MAAM,IAAI,MAAM,AAAC,YAAa,OAAF,GAAE;YAChC;YACA,OAAO,AAAC,KAAwB,OAApB,aAAa,CAAC,KAAK;QACjC;QACA,OAAO,AAAC,KAAsB,OAAlB;IACd,GACA,gKAAO,CAAC,OAAO;IAGjB,OAAO;QACL,SAAS;QACT;QACA;IACF;AACF;AAEA,MAAM,iBAAiB,OAAO,GAAG;AACjC,+FAA+F;AAC/F,MAAM,wBAAwB,IAAI,OAAO,OAAO,GAAG,qBAatC,gBAEX,OAAO,CAAC,QAAQ,KAAK;AAEvB;;;;;;;AAOA,GACA,SAAS,WAAW,UAAU;IAC5B,IAAI,CAAE,IAAI,OAAO,AAAC,GAAiB,OAAf,gBAAe,QAAM,IAAI,CAAC,aAAc;QAC1D,OAAO;YACL,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,EAAE;IAC3B,IAAI,iBAAiB;IACrB,IAAI,qBAAqB;IACzB,IAAI,YAAY;IAChB,IAAI,qBAAqB;IACzB,IAAI;IACJ,sBAAsB,SAAS,GAAG;IAClC,MAAO,QAAQ,sBAAsB,IAAI,CAAC,YAAa;QACrD,MAAM,EAAC,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAC,EAAC,GAAG;QACvD,IAAI,MAAM,KAAK;YACb,IAAI,CAAC,oBAAoB;gBACvB,qBAAqB;YACvB;YACA;QACF,OAAO,IAAI,MAAM,KAAK;YACpB,IAAI,oBAAoB;gBACtB;YACF,gBAAgB;YAChB,OAAO;gBACL,qBAAqB;YACvB;QACF,OAAO,IAAI,CAAC,oBAAoB;YAE9B,IAAI,SAAS,OAAO,aAAa,CAAC,UAAU,UAAU,CAAC,MAAM;gBAC3D,yEAAyE;gBACzE,IAAI,UAAU;oBACZ,MAAM,IAAI,MAAM,AAAC,uBAAwB,OAAF,GAAE;gBAC3C;gBACA,IAAI,aAAa,CAAC,GAAG,8BAA8B;gBACnD,0FAA0F;gBAC1F,0CAA0C;gBAC1C,IAAI,YAAY,IAAI,CAAC,QAAQ;oBAC3B,aAAa,IAAA,kKAAS,EAAC,YAAY,QAAQ,MAAM,MAAM,EAAE,MAAM;gBACjE,OAAO;oBACL,IAAI,cAAc,OAAO,cAAc,KAAK;wBAC1C,MAAM,YAAY,cAAc,MAAM,iBAAiB;wBACvD,8EAA8E;wBAC9E,sFAAsF;wBACtF,oEAAoE;wBACpE,IAAI,cAAc,MAAM;4BACtB,MAAM,IAAI,MAAM,AAAC,sBAA+B,OAAV,WAAU;wBAClD;wBACA,aAAa,AAAC,GAAsC,OAApC,WAAW,KAAK,CAAC,GAAG,YAAW,OAA0C,OAArC,WAAW,KAAK,CAAC,WAAW,QAAkB,OAAT,OAAM,KAAsC,OAAnC,WAAW,KAAK,CAAC,QAAQ,EAAE,MAAM;oBACrI,OAAO;wBACL,aAAa,AAAC,GAAqD,OAAnD,WAAW,KAAK,CAAC,GAAG,QAAQ,UAAU,MAAM,GAAE,OAAiB,OAAZ,WAAqB,OAAT,OAAM,KAAsC,OAAnC,WAAW,KAAK,CAAC,QAAQ,EAAE,MAAM;oBAC3H;oBACA,cAAc,GAAG,SAAS;gBAC5B;gBACA,sBAAsB,SAAS,IAAI;YACrC,OAAO,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBACvB,iBAAiB,IAAI,CAAC;YACxB,OAAO,IAAI,MAAM,KAAK;gBACpB,iBAAiB,iBAAiB,MAAM,GAAG,iBAAiB,GAAG,KAAK;YACtE;QAEF;QACA,YAAY;IACd;IAEA,OAAO;QACL,SAAS;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex/src/subclass.js"], "sourcesContent": ["/**\nWorks the same as JavaScript's native `RegExp` constructor in all contexts, but automatically\nadjusts subpattern matches and indices (with flag `d`) to account for captures added as part of\nemulating extended syntax.\n*/\nclass RegExpSubclass extends RegExp {\n  // Avoid `#private` to allow for subclassing\n  /**\n  @private\n  @type {Map<number, {\n    hidden: true;\n  }>}\n  */\n  _captureMap;\n  /**\n  @overload\n  @param {string} expression\n  @param {string} [flags]\n  @param {{\n    hiddenCaptures?: Array<number>;\n  }} [options]\n  */\n  /**\n  @overload\n  @param {RegExpSubclass} expression\n  @param {string} [flags]\n  */\n  constructor(expression, flags, options) {\n    // Argument `options` isn't provided when regexes are copied via `new RegExpSubclass(regexp)`,\n    // including as part of the internal handling of string methods `matchAll` and `split`\n    if (expression instanceof RegExp) {\n      if (options) {\n        throw new Error('Cannot provide options when copying a regexp');\n      }\n      super(expression, flags);\n      if (expression instanceof RegExpSubclass) {\n        this._captureMap = expression._captureMap;\n      } else {\n        this._captureMap = new Map();\n      }\n    } else {\n      super(expression, flags);\n      const hiddenCaptures = options?.hiddenCaptures ?? [];\n      this._captureMap = createCaptureMap(hiddenCaptures);\n    }\n  }\n  /**\n  Called internally by all String/RegExp methods that use regexes.\n  @override\n  @param {string} str\n  @returns {RegExpExecArray | null}\n  */\n  exec(str) {\n    const match = super.exec(str);\n    if (!match || !this._captureMap.size) {\n      return match;\n    }\n    const matchCopy = [...match];\n    // Empty all but the first value of the array while preserving its other properties\n    match.length = 1;\n    let indicesCopy;\n    if (this.hasIndices) {\n      indicesCopy = [...match.indices];\n      match.indices.length = 1;\n    }\n    for (let i = 1; i < matchCopy.length; i++) {\n      if (!this._captureMap.get(i)?.hidden) {\n        match.push(matchCopy[i]);\n        if (this.hasIndices) {\n          match.indices.push(indicesCopy[i]);\n        }\n      }\n    }\n    return match;\n  }\n}\n\n/**\nBuild the capturing group map, with hidden captures marked to indicate their submatches shouldn't\nappear in match results.\n@param {Array<number>} hiddenCaptures\n@returns {Map<number, {\n  hidden: true;\n}>}\n*/\nfunction createCaptureMap(hiddenCaptures) {\n  const captureMap = new Map();\n  for (const num of hiddenCaptures) {\n    captureMap.set(num, {\n      hidden: true,\n    });\n  }\n  return captureMap;\n}\n\nexport {\n  RegExpSubclass,\n};\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;AACA,MAAM,uBAAuB;IAyC3B;;;;;EAKA,GACA,KAAK,GAAG,EAAE;QACR,MAAM,QAAQ,KAAK,CAAC,KAAK;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACpC,OAAO;QACT;QACA,MAAM,YAAY;eAAI;SAAM;QAC5B,mFAAmF;QACnF,MAAM,MAAM,GAAG;QACf,IAAI;QACJ,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,cAAc;mBAAI,MAAM,OAAO;aAAC;YAChC,MAAM,OAAO,CAAC,MAAM,GAAG;QACzB;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACpC;YAAL,IAAI,GAAC,wBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAArB,4CAAA,sBAAyB,MAAM,GAAE;gBACpC,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE;gBACvB,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBACnC;YACF;QACF;QACA,OAAO;IACT;IA5DA;;;;;;;EAOA,GACA;;;;EAIA,GACA,YAAY,UAAU,EAAE,KAAK,EAAE,OAAO,CAAE;QACtC,8FAA8F;QAC9F,sFAAsF;QACtF,IAAI,sBAAsB,QAAQ;YAChC,IAAI,SAAS;gBACX,MAAM,IAAI,MAAM;YAClB;YACA,KAAK,CAAC,YAAY,QA5BtB,4CAA4C;YAC5C;;;;;EAKA,GACA,+KAAA,eAAA,KAAA;YAsBI,IAAI,sBAAsB,gBAAgB;gBACxC,IAAI,CAAC,WAAW,GAAG,WAAW,WAAW;YAC3C,OAAO;gBACL,IAAI,CAAC,WAAW,GAAG,IAAI;YACzB;QACF,OAAO;YACL,KAAK,CAAC,YAAY,QA5BtB,+KAAA,eAAA,KAAA;gBA6B2B;YAAvB,MAAM,iBAAiB,CAAA,0BAAA,oBAAA,8BAAA,QAAS,cAAc,cAAvB,qCAAA,0BAA2B,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,iBAAiB;QACtC;IACF;AA8BF;AAEA;;;;;;;AAOA,GACA,SAAS,iBAAiB,cAAc;IACtC,MAAM,aAAa,IAAI;IACvB,KAAK,MAAM,OAAO,eAAgB;QAChC,WAAW,GAAG,CAAC,KAAK;YAClB,QAAQ;QACV;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex/src/internals.js"], "sourcesContent": ["export {atomic, possessive} from './atomic.js';\nexport {RegExpSubclass} from './subclass.js';\n"], "names": [], "mappings": ";AAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/regex-recursion/src/index.js"], "sourcesContent": ["import {Context, forEachUnescaped, getGroupContents, hasUnescaped, replaceUnescaped} from 'regex-utilities';\n\nconst r = String.raw;\nconst gRToken = r`\\\\g<(?<gRNameOrNum>[^>&]+)&R=(?<gRDepth>[^>]+)>`;\nconst recursiveToken = r`\\(\\?R=(?<rDepth>[^\\)]+)\\)|${gRToken}`;\nconst namedCaptureDelim = r`\\(\\?<(?![=!])(?<captureName>[^>]+)>`;\nconst captureDelim = r`${namedCaptureDelim}|(?<unnamed>\\()(?!\\?)`;\nconst token = new RegExp(r`${namedCaptureDelim}|${recursiveToken}|\\(\\?|\\\\?.`, 'gsu');\nconst overlappingRecursionMsg = 'Cannot use multiple overlapping recursions';\n\n/**\n@param {string} pattern\n@param {{\n  flags?: string;\n  captureTransfers?: Map<number, Array<number>>;\n  hiddenCaptures?: Array<number>;\n  mode?: 'plugin' | 'external';\n}} [data]\n@returns {{\n  pattern: string;\n  captureTransfers: Map<number, Array<number>>;\n  hiddenCaptures: Array<number>;\n}}\n*/\nfunction recursion(pattern, data) {\n  const {hiddenCaptures, mode} = {\n    hiddenCaptures: [],\n    mode: 'plugin',\n    ...data,\n  };\n  // Capture transfer is used by <github.com/slevithan/oniguruma-to-es>\n  let captureTransfers = data?.captureTransfers ?? new Map();\n  // Keep the initial fail-check (which avoids unneeded processing) as fast as possible by testing\n  // without the accuracy improvement of using `hasUnescaped` with `Context.DEFAULT`\n  if (!(new RegExp(recursiveToken, 'su').test(pattern))) {\n    return {\n      pattern,\n      captureTransfers,\n      hiddenCaptures,\n    };\n  }\n  if (mode === 'plugin' && hasUnescaped(pattern, r`\\(\\?\\(DEFINE\\)`, Context.DEFAULT)) {\n    throw new Error('DEFINE groups cannot be used with recursion');\n  }\n\n  const addedHiddenCaptures = [];\n  const hasNumberedBackref = hasUnescaped(pattern, r`\\\\[1-9]`, Context.DEFAULT);\n  const groupContentsStartPos = new Map();\n  const openGroups = [];\n  let hasRecursed = false;\n  let numCharClassesOpen = 0;\n  let numCapturesPassed = 0;\n  let match;\n  token.lastIndex = 0;\n  while ((match = token.exec(pattern))) {\n    const {0: m, groups: {captureName, rDepth, gRNameOrNum, gRDepth}} = match;\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n\n      // `(?R=N)`\n      if (rDepth) {\n        assertMaxInBounds(rDepth);\n        if (hasRecursed) {\n          throw new Error(overlappingRecursionMsg);\n        }\n        if (hasNumberedBackref) {\n          // Could add support for numbered backrefs with extra effort, but it's probably not worth\n          // it. To trigger this error, the regex must include recursion and one of the following:\n          // - An interpolated regex that contains a numbered backref (since other numbered\n          //   backrefs are prevented by implicit flag n).\n          // - A numbered backref, when flag n is explicitly disabled.\n          // Note that Regex+'s extended syntax (atomic groups and sometimes subroutines) can also\n          // add numbered backrefs, but those work fine because external plugins like this one run\n          // *before* the transformation of built-in syntax extensions\n          throw new Error(\n            // When used in `external` mode by transpilers other than Regex+, backrefs might have\n            // gone through conversion from named to numbered, so avoid a misleading error\n            `${mode === 'external' ? 'Backrefs' : 'Numbered backrefs'} cannot be used with global recursion`\n          );\n        }\n        const left = pattern.slice(0, match.index);\n        const right = pattern.slice(token.lastIndex);\n        if (hasUnescaped(right, recursiveToken, Context.DEFAULT)) {\n          throw new Error(overlappingRecursionMsg);\n        }\n        const reps = +rDepth - 1;\n        pattern = makeRecursive(\n          left,\n          right,\n          reps,\n          false,\n          hiddenCaptures,\n          addedHiddenCaptures,\n          numCapturesPassed\n        );\n        captureTransfers = mapCaptureTransfers(\n          captureTransfers,\n          left,\n          reps,\n          addedHiddenCaptures.length,\n          0,\n          numCapturesPassed\n        );\n        // No need to parse further\n        break;\n      // `\\g<name&R=N>`, `\\g<number&R=N>`\n      } else if (gRNameOrNum) {\n        assertMaxInBounds(gRDepth);\n        let isWithinReffedGroup = false;\n        for (const g of openGroups) {\n          if (g.name === gRNameOrNum || g.num === +gRNameOrNum) {\n            isWithinReffedGroup = true;\n            if (g.hasRecursedWithin) {\n              throw new Error(overlappingRecursionMsg);\n            }\n            break;\n          }\n        }\n        if (!isWithinReffedGroup) {\n          throw new Error(r`Recursive \\g cannot be used outside the referenced group \"${\n            mode === 'external' ? gRNameOrNum : r`\\g<${gRNameOrNum}&R=${gRDepth}>`\n          }\"`);\n        }\n        const startPos = groupContentsStartPos.get(gRNameOrNum);\n        const groupContents = getGroupContents(pattern, startPos);\n        if (\n          hasNumberedBackref &&\n          hasUnescaped(groupContents, r`${namedCaptureDelim}|\\((?!\\?)`, Context.DEFAULT)\n        ) {\n          throw new Error(\n            // When used in `external` mode by transpilers other than Regex+, backrefs might have\n            // gone through conversion from named to numbered, so avoid a misleading error\n            `${mode === 'external' ? 'Backrefs' : 'Numbered backrefs'} cannot be used with recursion of capturing groups`\n          );\n        }\n        const groupContentsLeft = pattern.slice(startPos, match.index);\n        const groupContentsRight = groupContents.slice(groupContentsLeft.length + m.length);\n        const numAddedHiddenCapturesPreExpansion = addedHiddenCaptures.length;\n        const reps = +gRDepth - 1;\n        const expansion = makeRecursive(\n          groupContentsLeft,\n          groupContentsRight,\n          reps,\n          true,\n          hiddenCaptures,\n          addedHiddenCaptures,\n          numCapturesPassed\n        );\n        captureTransfers = mapCaptureTransfers(\n          captureTransfers,\n          groupContentsLeft,\n          reps,\n          addedHiddenCaptures.length - numAddedHiddenCapturesPreExpansion,\n          numAddedHiddenCapturesPreExpansion,\n          numCapturesPassed\n        );\n        const pre = pattern.slice(0, startPos);\n        const post = pattern.slice(startPos + groupContents.length);\n        // Modify the string we're looping over\n        pattern = `${pre}${expansion}${post}`;\n        // Step forward for the next loop iteration\n        token.lastIndex += expansion.length - m.length - groupContentsLeft.length - groupContentsRight.length;\n        openGroups.forEach(g => g.hasRecursedWithin = true);\n        hasRecursed = true;\n      } else if (captureName) {\n        numCapturesPassed++;\n        groupContentsStartPos.set(String(numCapturesPassed), token.lastIndex);\n        groupContentsStartPos.set(captureName, token.lastIndex);\n        openGroups.push({\n          num: numCapturesPassed,\n          name: captureName,\n        });\n      } else if (m[0] === '(') {\n        const isUnnamedCapture = m === '(';\n        if (isUnnamedCapture) {\n          numCapturesPassed++;\n          groupContentsStartPos.set(String(numCapturesPassed), token.lastIndex);\n        }\n        openGroups.push(isUnnamedCapture ? {num: numCapturesPassed} : {});\n      } else if (m === ')') {\n        openGroups.pop();\n      }\n\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n\n  hiddenCaptures.push(...addedHiddenCaptures);\n\n  return {\n    pattern,\n    captureTransfers,\n    hiddenCaptures,\n  };\n}\n\n/**\n@param {string} max\n*/\nfunction assertMaxInBounds(max) {\n  const errMsg = `Max depth must be integer between 2 and 100; used ${max}`;\n  if (!/^[1-9]\\d*$/.test(max)) {\n    throw new Error(errMsg);\n  }\n  max = +max;\n  if (max < 2 || max > 100) {\n    throw new Error(errMsg);\n  }\n}\n\n/**\n@param {string} left\n@param {string} right\n@param {number} reps\n@param {boolean} isSubpattern\n@param {Array<number>} hiddenCaptures\n@param {Array<number>} addedHiddenCaptures\n@param {number} numCapturesPassed\n@returns {string}\n*/\nfunction makeRecursive(\n  left,\n  right,\n  reps,\n  isSubpattern,\n  hiddenCaptures,\n  addedHiddenCaptures,\n  numCapturesPassed\n) {\n  const namesInRecursed = new Set();\n  // Can skip this work if not needed\n  if (isSubpattern) {\n    forEachUnescaped(left + right, namedCaptureDelim, ({groups: {captureName}}) => {\n      namesInRecursed.add(captureName);\n    }, Context.DEFAULT);\n  }\n  const rest = [\n    reps,\n    isSubpattern ? namesInRecursed : null,\n    hiddenCaptures,\n    addedHiddenCaptures,\n    numCapturesPassed,\n  ];\n  // Depth 2: 'left(?:left(?:)right)right'\n  // Depth 3: 'left(?:left(?:left(?:)right)right)right'\n  // Empty group in the middle separates tokens and absorbs a following quantifier if present\n  return `${left}${\n    repeatWithDepth(`(?:${left}`, 'forward', ...rest)\n  }(?:)${\n    repeatWithDepth(`${right})`, 'backward', ...rest)\n  }${right}`;\n}\n\n/**\n@param {string} pattern\n@param {'forward' | 'backward'} direction\n@param {number} reps\n@param {Set<string> | null} namesInRecursed\n@param {Array<number>} hiddenCaptures\n@param {Array<number>} addedHiddenCaptures\n@param {number} numCapturesPassed\n@returns {string}\n*/\nfunction repeatWithDepth(\n  pattern,\n  direction,\n  reps,\n  namesInRecursed,\n  hiddenCaptures,\n  addedHiddenCaptures,\n  numCapturesPassed\n) {\n  const startNum = 2;\n  const getDepthNum = i => direction === 'forward' ? (i + startNum) : (reps - i + startNum - 1);\n  let result = '';\n  for (let i = 0; i < reps; i++) {\n    const depthNum = getDepthNum(i);\n    result += replaceUnescaped(\n      pattern,\n      r`${captureDelim}|\\\\k<(?<backref>[^>]+)>`,\n      ({0: m, groups: {captureName, unnamed, backref}}) => {\n        if (backref && namesInRecursed && !namesInRecursed.has(backref)) {\n          // Don't alter backrefs to groups outside the recursed subpattern\n          return m;\n        }\n        const suffix = `_$${depthNum}`;\n        if (unnamed || captureName) {\n          const addedCaptureNum = numCapturesPassed + addedHiddenCaptures.length + 1;\n          addedHiddenCaptures.push(addedCaptureNum);\n          incrementIfAtLeast(hiddenCaptures, addedCaptureNum);\n          return unnamed ? m : `(?<${captureName}${suffix}>`;\n        }\n        return r`\\k<${backref}${suffix}>`;\n      },\n      Context.DEFAULT\n    );\n  }\n  return result;\n}\n\n/**\nUpdates the array in place by incrementing each value greater than or equal to the threshold.\n@param {Array<number>} arr\n@param {number} threshold\n*/\nfunction incrementIfAtLeast(arr, threshold) {\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] >= threshold) {\n      arr[i]++;\n    }\n  }\n}\n\n/**\n@param {Map<number, Array<number>>} captureTransfers\n@param {string} left\n@param {number} reps\n@param {number} numCapturesAddedInExpansion\n@param {number} numAddedHiddenCapturesPreExpansion\n@param {number} numCapturesPassed\n@returns {Map<number, Array<number>>}\n*/\nfunction mapCaptureTransfers(captureTransfers, left, reps, numCapturesAddedInExpansion, numAddedHiddenCapturesPreExpansion, numCapturesPassed) {\n  if (captureTransfers.size && numCapturesAddedInExpansion) {\n    let numCapturesInLeft = 0;\n    forEachUnescaped(left, captureDelim, () => numCapturesInLeft++, Context.DEFAULT);\n    // Is 0 for global recursion\n    const recursionDelimCaptureNum = numCapturesPassed - numCapturesInLeft + numAddedHiddenCapturesPreExpansion;\n    const newCaptureTransfers = new Map();\n    captureTransfers.forEach((from, to) => {\n      const numCapturesInRight = (numCapturesAddedInExpansion - (numCapturesInLeft * reps)) / reps;\n      const numCapturesAddedInLeft = numCapturesInLeft * reps;\n      const newTo = to > (recursionDelimCaptureNum + numCapturesInLeft) ? to + numCapturesAddedInExpansion : to;\n      const newFrom = [];\n      for (const f of from) {\n        // Before the recursed subpattern\n        if (f <= recursionDelimCaptureNum) {\n          newFrom.push(f);\n        // After the recursed subpattern\n        } else if (f > (recursionDelimCaptureNum + numCapturesInLeft + numCapturesInRight)) {\n          newFrom.push(f + numCapturesAddedInExpansion);\n        // Within the recursed subpattern, on the left of the recursion token\n        } else if (f <= (recursionDelimCaptureNum + numCapturesInLeft)) {\n          for (let i = 0; i <= reps; i++) {\n            newFrom.push(f + (numCapturesInLeft * i));\n          }\n        // Within the recursed subpattern, on the right of the recursion token\n        } else {\n          for (let i = 0; i <= reps; i++) {\n            newFrom.push(f + numCapturesAddedInLeft + (numCapturesInRight * i));\n          }\n        }\n      }\n      newCaptureTransfers.set(newTo, newFrom);\n    });\n    return newCaptureTransfers;\n  }\n  return captureTransfers;\n}\n\nexport {\n  recursion,\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,OAAO,GAAG;AACpB,MAAM,UAAU;AAChB,MAAM,iBAAiB,sBAA8B;AACrD,MAAM,oBAAoB;AAC1B,MAAM,eAAe,sBAAI;AACzB,MAAM,QAAQ,IAAI,OAAO,sBAAI,mBAAqB,iBAA4B;AAC9E,MAAM,0BAA0B;AAEhC;;;;;;;;;;;;;AAaA,GACA,SAAS,UAAU,OAAO,EAAE,IAAI;IAC9B,MAAM,EAAC,cAAc,EAAE,IAAI,EAAC,GAAG;QAC7B,gBAAgB,EAAE;QAClB,MAAM;QACN,GAAG,IAAI;IACT;QAEuB;IADvB,qEAAqE;IACrE,IAAI,mBAAmB,CAAA,yBAAA,iBAAA,2BAAA,KAAM,gBAAgB,cAAtB,oCAAA,yBAA0B,IAAI;IACrD,gGAAgG;IAChG,kFAAkF;IAClF,IAAI,CAAE,IAAI,OAAO,gBAAgB,MAAM,IAAI,CAAC,UAAW;QACrD,OAAO;YACL;YACA;YACA;QACF;IACF;IACA,IAAI,SAAS,YAAY,IAAA,qKAAY,EAAC,SAAS,uBAAmB,gKAAO,CAAC,OAAO,GAAG;QAClF,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,sBAAsB,EAAE;IAC9B,MAAM,qBAAqB,IAAA,qKAAY,EAAC,SAAS,uBAAY,gKAAO,CAAC,OAAO;IAC5E,MAAM,wBAAwB,IAAI;IAClC,MAAM,aAAa,EAAE;IACrB,IAAI,cAAc;IAClB,IAAI,qBAAqB;IACzB,IAAI,oBAAoB;IACxB,IAAI;IACJ,MAAM,SAAS,GAAG;IAClB,MAAQ,QAAQ,MAAM,IAAI,CAAC,SAAW;QACpC,MAAM,EAAC,GAAG,CAAC,EAAE,QAAQ,EAAC,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAC,EAAC,GAAG;QACpE,IAAI,MAAM,KAAK;YACb;QACF,OAAO,IAAI,CAAC,oBAAoB;YAE9B,WAAW;YACX,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,IAAI,aAAa;oBACf,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,oBAAoB;oBACtB,yFAAyF;oBACzF,wFAAwF;oBACxF,iFAAiF;oBACjF,gDAAgD;oBAChD,4DAA4D;oBAC5D,wFAAwF;oBACxF,wFAAwF;oBACxF,4DAA4D;oBAC5D,MAAM,IAAI,MACR,qFAAqF;oBACrF,8EAA8E;oBAC7E,GAAyD,OAAvD,SAAS,aAAa,aAAa,qBAAoB;gBAE9D;gBACA,MAAM,OAAO,QAAQ,KAAK,CAAC,GAAG,MAAM,KAAK;gBACzC,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,SAAS;gBAC3C,IAAI,IAAA,qKAAY,EAAC,OAAO,gBAAgB,gKAAO,CAAC,OAAO,GAAG;oBACxD,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAO,CAAC,SAAS;gBACvB,UAAU,cACR,MACA,OACA,MACA,OACA,gBACA,qBACA;gBAEF,mBAAmB,oBACjB,kBACA,MACA,MACA,oBAAoB,MAAM,EAC1B,GACA;gBAGF;YACF,mCAAmC;YACnC,OAAO,IAAI,aAAa;gBACtB,kBAAkB;gBAClB,IAAI,sBAAsB;gBAC1B,KAAK,MAAM,KAAK,WAAY;oBAC1B,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,GAAG,KAAK,CAAC,aAAa;wBACpD,sBAAsB;wBACtB,IAAI,EAAE,iBAAiB,EAAE;4BACvB,MAAM,IAAI,MAAM;wBAClB;wBACA;oBACF;gBACF;gBACA,IAAI,CAAC,qBAAqB;oBACxB,MAAM,IAAI,MAAM,sBACd,SAAS,aAAa,cAAc,sBAAO,aAAiB;gBAEhE;gBACA,MAAM,WAAW,sBAAsB,GAAG,CAAC;gBAC3C,MAAM,gBAAgB,IAAA,yKAAgB,EAAC,SAAS;gBAChD,IACE,sBACA,IAAA,qKAAY,EAAC,eAAe,sBAAI,oBAA8B,gKAAO,CAAC,OAAO,GAC7E;oBACA,MAAM,IAAI,MACR,qFAAqF;oBACrF,8EAA8E;oBAC7E,GAAyD,OAAvD,SAAS,aAAa,aAAa,qBAAoB;gBAE9D;gBACA,MAAM,oBAAoB,QAAQ,KAAK,CAAC,UAAU,MAAM,KAAK;gBAC7D,MAAM,qBAAqB,cAAc,KAAK,CAAC,kBAAkB,MAAM,GAAG,EAAE,MAAM;gBAClF,MAAM,qCAAqC,oBAAoB,MAAM;gBACrE,MAAM,OAAO,CAAC,UAAU;gBACxB,MAAM,YAAY,cAChB,mBACA,oBACA,MACA,MACA,gBACA,qBACA;gBAEF,mBAAmB,oBACjB,kBACA,mBACA,MACA,oBAAoB,MAAM,GAAG,oCAC7B,oCACA;gBAEF,MAAM,MAAM,QAAQ,KAAK,CAAC,GAAG;gBAC7B,MAAM,OAAO,QAAQ,KAAK,CAAC,WAAW,cAAc,MAAM;gBAC1D,uCAAuC;gBACvC,UAAU,AAAC,GAAQ,OAAN,KAAkB,OAAZ,WAAiB,OAAL;gBAC/B,2CAA2C;gBAC3C,MAAM,SAAS,IAAI,UAAU,MAAM,GAAG,EAAE,MAAM,GAAG,kBAAkB,MAAM,GAAG,mBAAmB,MAAM;gBACrG,WAAW,OAAO,CAAC,CAAA,IAAK,EAAE,iBAAiB,GAAG;gBAC9C,cAAc;YAChB,OAAO,IAAI,aAAa;gBACtB;gBACA,sBAAsB,GAAG,CAAC,OAAO,oBAAoB,MAAM,SAAS;gBACpE,sBAAsB,GAAG,CAAC,aAAa,MAAM,SAAS;gBACtD,WAAW,IAAI,CAAC;oBACd,KAAK;oBACL,MAAM;gBACR;YACF,OAAO,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBACvB,MAAM,mBAAmB,MAAM;gBAC/B,IAAI,kBAAkB;oBACpB;oBACA,sBAAsB,GAAG,CAAC,OAAO,oBAAoB,MAAM,SAAS;gBACtE;gBACA,WAAW,IAAI,CAAC,mBAAmB;oBAAC,KAAK;gBAAiB,IAAI,CAAC;YACjE,OAAO,IAAI,MAAM,KAAK;gBACpB,WAAW,GAAG;YAChB;QAEF,OAAO,IAAI,MAAM,KAAK;YACpB;QACF;IACF;IAEA,eAAe,IAAI,IAAI;IAEvB,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA;;AAEA,GACA,SAAS,kBAAkB,GAAG;IAC5B,MAAM,SAAS,AAAC,qDAAwD,OAAJ;IACpE,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM;QAC3B,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,CAAC;IACP,IAAI,MAAM,KAAK,MAAM,KAAK;QACxB,MAAM,IAAI,MAAM;IAClB;AACF;AAEA;;;;;;;;;AASA,GACA,SAAS,cACP,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,YAAY,EACZ,cAAc,EACd,mBAAmB,EACnB,iBAAiB;IAEjB,MAAM,kBAAkB,IAAI;IAC5B,mCAAmC;IACnC,IAAI,cAAc;QAChB,IAAA,yKAAgB,EAAC,OAAO,OAAO,mBAAmB;gBAAC,EAAC,QAAQ,EAAC,WAAW,EAAC,EAAC;YACxE,gBAAgB,GAAG,CAAC;QACtB,GAAG,gKAAO,CAAC,OAAO;IACpB;IACA,MAAM,OAAO;QACX;QACA,eAAe,kBAAkB;QACjC;QACA;QACA;KACD;IACD,wCAAwC;IACxC,qDAAqD;IACrD,2FAA2F;IAC3F,OAAO,AAAC,GACN,OADQ,MAGR,OAFA,gBAAgB,AAAC,MAAU,OAAL,OAAQ,cAAc,OAC7C,QAEE,OADD,gBAAgB,AAAC,GAAQ,OAAN,OAAM,MAAI,eAAe,OACrC,OAAN;AACL;AAEA;;;;;;;;;AASA,GACA,SAAS,gBACP,OAAO,EACP,SAAS,EACT,IAAI,EACJ,eAAe,EACf,cAAc,EACd,mBAAmB,EACnB,iBAAiB;IAEjB,MAAM,WAAW;IACjB,MAAM,cAAc,CAAA,IAAK,cAAc,YAAa,IAAI,WAAa,OAAO,IAAI,WAAW;IAC3F,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC7B,MAAM,WAAW,YAAY;QAC7B,UAAU,IAAA,yKAAgB,EACxB,SACA,uBAAI,eACJ;gBAAC,EAAC,GAAG,CAAC,EAAE,QAAQ,EAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAC,EAAC;YAC9C,IAAI,WAAW,mBAAmB,CAAC,gBAAgB,GAAG,CAAC,UAAU;gBAC/D,iEAAiE;gBACjE,OAAO;YACT;YACA,MAAM,SAAS,AAAC,KAAa,OAAT;YACpB,IAAI,WAAW,aAAa;gBAC1B,MAAM,kBAAkB,oBAAoB,oBAAoB,MAAM,GAAG;gBACzE,oBAAoB,IAAI,CAAC;gBACzB,mBAAmB,gBAAgB;gBACnC,OAAO,UAAU,IAAI,AAAC,MAAmB,OAAd,aAAqB,OAAP,QAAO;YAClD;YACA,OAAO,uBAAO,SAAU;QAC1B,GACA,gKAAO,CAAC,OAAO;IAEnB;IACA,OAAO;AACT;AAEA;;;;AAIA,GACA,SAAS,mBAAmB,GAAG,EAAE,SAAS;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,GAAG,CAAC,EAAE,IAAI,WAAW;YACvB,GAAG,CAAC,EAAE;QACR;IACF;AACF;AAEA;;;;;;;;AAQA,GACA,SAAS,oBAAoB,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,2BAA2B,EAAE,kCAAkC,EAAE,iBAAiB;IAC3I,IAAI,iBAAiB,IAAI,IAAI,6BAA6B;QACxD,IAAI,oBAAoB;QACxB,IAAA,yKAAgB,EAAC,MAAM,cAAc,IAAM,qBAAqB,gKAAO,CAAC,OAAO;QAC/E,4BAA4B;QAC5B,MAAM,2BAA2B,oBAAoB,oBAAoB;QACzE,MAAM,sBAAsB,IAAI;QAChC,iBAAiB,OAAO,CAAC,CAAC,MAAM;YAC9B,MAAM,qBAAqB,CAAC,8BAA+B,oBAAoB,IAAK,IAAI;YACxF,MAAM,yBAAyB,oBAAoB;YACnD,MAAM,QAAQ,KAAM,2BAA2B,oBAAqB,KAAK,8BAA8B;YACvG,MAAM,UAAU,EAAE;YAClB,KAAK,MAAM,KAAK,KAAM;gBACpB,iCAAiC;gBACjC,IAAI,KAAK,0BAA0B;oBACjC,QAAQ,IAAI,CAAC;gBACf,gCAAgC;gBAChC,OAAO,IAAI,IAAK,2BAA2B,oBAAoB,oBAAqB;oBAClF,QAAQ,IAAI,CAAC,IAAI;gBACnB,qEAAqE;gBACrE,OAAO,IAAI,KAAM,2BAA2B,mBAAoB;oBAC9D,IAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAK;wBAC9B,QAAQ,IAAI,CAAC,IAAK,oBAAoB;oBACxC;gBACF,sEAAsE;gBACtE,OAAO;oBACL,IAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAK;wBAC9B,QAAQ,IAAI,CAAC,IAAI,yBAA0B,qBAAqB;oBAClE;gBACF;YACF;YACA,oBAAoB,GAAG,CAAC,OAAO;QACjC;QACA,OAAO;IACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/utils.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/options.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/unicode.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/transform.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/generate.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/subclass.js", "file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/oniguruma-to-es/src/index.js"], "sourcesContent": ["import {EsVersion, Target} from './options.js';\n\nconst cp = String.fromCodePoint;\nconst r = String.raw;\n\nconst envFlags = {\n  flagGroups: (() => {\n    try {\n      new RegExp('(?i:)');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n  unicodeSets: (() => {\n    try {\n      new RegExp('', 'v');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n};\n// Detect WebKit bug: <github.com/slevithan/oniguruma-to-es/issues/30>\nenvFlags.bugFlagVLiteralHyphenIsRange = envFlags.unicodeSets ? (() => {\n  try {\n    new RegExp(r`[\\d\\-a]`, 'v');\n  } catch {\n    return true;\n  }\n  return false;\n})() : false;\n// Detect WebKit bug: <github.com/slevithan/oniguruma-to-es/issues/38>\nenvFlags.bugNestedClassIgnoresNegation = envFlags.unicodeSets && new RegExp('[[^a]]', 'v').test('a');\n\nfunction getNewCurrentFlags(current, {enable, disable}) {\n  return {\n    dotAll: !disable?.dotAll && !!(enable?.dotAll || current.dotAll),\n    ignoreCase: !disable?.ignoreCase && !!(enable?.ignoreCase || current.ignoreCase),\n  };\n}\n\nfunction getOrInsert(map, key, defaultValue) {\n  if (!map.has(key)) {\n    map.set(key, defaultValue);\n  }\n  return map.get(key);\n}\n\n/**\n@param {keyof Target} target\n@param {keyof Target} min\n@returns {boolean}\n*/\nfunction isMinTarget(target, min) {\n  return EsVersion[target] >= EsVersion[min];\n}\n\nfunction throwIfNullish(value, msg) {\n  if (value == null) {\n    throw new Error(msg ?? 'Value expected');\n  }\n  return value;\n}\n\nexport {\n  cp,\n  envFlags,\n  getNewCurrentFlags,\n  getOrInsert,\n  isMinTarget,\n  r,\n  throwIfNullish,\n};\n", "import {envFlags} from './utils.js';\n/**\n@import {ToRegExpOptions} from './index.js';\n*/\n\nconst Accuracy = /** @type {const} */ ({\n  default: 'default',\n  strict: 'strict',\n});\n\nconst EsVersion = {\n  ES2025: 2025,\n  ES2024: 2024,\n  ES2018: 2018,\n};\n\nconst Target = /** @type {const} */ ({\n  auto: 'auto',\n  ES2025: 'ES2025',\n  ES2024: 'ES2024',\n  ES2018: 'ES2018',\n});\n\n/**\nReturns a complete set of options, with default values set for options that weren't provided.\n@param {ToRegExpOptions} [options]\n@returns {Required<ToRegExpOptions>}\n*/\nfunction getOptions(options = {}) {\n  if ({}.toString.call(options) !== '[object Object]') {\n    throw new Error('Unexpected options');\n  }\n  if (options.target !== undefined && !Target[options.target]) {\n    throw new Error(`Unexpected target \"${options.target}\"`)\n  }\n  // Set default values\n  const opts = {\n    // Sets the level of emulation rigor/strictness.\n    accuracy: 'default',\n    // Disables advanced emulation that relies on returning a `RegExp` subclass, resulting in\n    // certain patterns not being emulatable.\n    avoidSubclass: false,\n    // Oniguruma flags; a string with `i`, `m`, `x`, `D`, `S`, `W`, `y{g}` in any order (all\n    // optional). Oniguruma's `m` is equivalent to JavaScript's `s` (`dotAll`).\n    flags: '',\n    // Include JavaScript flag `g` (`global`) in the result.\n    global: false,\n    // Include JavaScript flag `d` (`hasIndices`) in the result.\n    hasIndices: false,\n    // Delay regex construction until first use if the transpiled pattern is at least this length.\n    lazyCompileLength: Infinity,\n    // JavaScript version used for generated regexes. Using `auto` detects the best value based on\n    // your environment. Later targets allow faster processing, simpler generated source, and\n    // support for additional features.\n    target: 'auto',\n    // Disables minifications that simplify the pattern without changing the meaning.\n    verbose: false,\n    ...options,\n    // Advanced options that override standard behavior, error checking, and flags when enabled.\n    rules: {\n      // Useful with TextMate grammars that merge backreferences across patterns.\n      allowOrphanBackrefs: false,\n      // Use ASCII `\\b` and `\\B`, which increases search performance of generated regexes.\n      asciiWordBoundaries: false,\n      // Allow unnamed captures and numbered calls (backreferences and subroutines) when using\n      // named capture. This is Oniguruma option `ONIG_OPTION_CAPTURE_GROUP`; on by default in\n      // `vscode-oniguruma`.\n      captureGroup: false,\n      // Change the recursion depth limit from Oniguruma's `20` to an integer `2`–`20`.\n      recursionLimit: 20,\n      // `^` as `\\A`; `$` as`\\Z`. Improves search performance of generated regexes without changing\n      // the meaning if searching line by line. This is Oniguruma option `ONIG_OPTION_SINGLELINE`.\n      singleline: false,\n      ...options.rules,\n    },\n  };\n  if (opts.target === 'auto') {\n    opts.target = envFlags.flagGroups ? 'ES2025' : (envFlags.unicodeSets ? 'ES2024' : 'ES2018');\n  }\n  return opts;\n}\n\nexport {\n  Accuracy,\n  EsVersion,\n  getOptions,\n  Target,\n};\n", "import {cp, r} from './utils.js';\nimport {slug} from 'oniguruma-parser/parser';\n\n// `\\t\\n\\v\\f\\r\\x20`\nconst asciiSpaceChar = '[\\t-\\r ]';\n\nconst CharsWithoutIgnoreCaseExpansion = new Set([\n  cp(0x130), // İ\n  cp(0x131), // ı\n]);\n\n// Different than `PosixClassMap`'s `word`\nconst defaultWordChar = r`[\\p{L}\\p{M}\\p{N}\\p{Pc}]`;\n\nfunction getIgnoreCaseMatchChars(char) {\n  // Some chars should not match the chars they case swap to\n  if (CharsWithoutIgnoreCaseExpansion.has(char)) {\n    return [char];\n  }\n  const set = new Set();\n  const lower = char.toLowerCase();\n  // Everything else is based on `lower`\n  const upper = lower.toUpperCase();\n  const title = LowerToTitleCaseMap.get(lower);\n  const altLower = LowerToAlternativeLowerCaseMap.get(lower);\n  const altUpper = LowerToAlternativeUpperCaseMap.get(lower);\n  // Exclude ucase if multiple chars; count code point length. Excludes ucase versions of German\n  // es-zed 'ß', ligatures like 'ﬀ', and chars with no precomposed ucase like 'ŉ'. See\n  // <unicode.org/Public/UNIDATA/SpecialCasing.txt>\n  if ([...upper].length === 1) {\n    set.add(upper);\n  }\n  altUpper && set.add(altUpper);\n  title && set.add(title);\n  // Lcase of 'İ' is multiple chars, but it's excluded by `CharsWithoutIgnoreCaseExpansion`\n  set.add(lower);\n  altLower && set.add(altLower);\n  return [...set];\n}\n\n// The following set includes:\n// - All ES2024 general categories and their aliases (all are supported by Oniguruma). See\n//   <github.com/mathiasbynens/unicode-match-property-value-ecmascript/blob/main/data/mappings.js>\n// - All ES2024 binary properties and their aliases (all are supported by Oniguruma). See\n//   <tc39.es/ecma262/multipage/text-processing.html#table-binary-unicode-properties>\n// Unicode properties must be mapped to property names supported by JS, and must also apply JS's\n// stricter rules for casing, whitespace, hyphens, and underscores in Unicode property names. In\n// order to remain lightweight, this library assumes properties not in this list are Unicode script\n// names (which require a `Script=` or `sc=` prefix in JS). Unlike JS, Oniguruma doesn't support\n// script extensions, and it supports some properties that aren't supported in JS (including blocks\n// with an `In_` prefix). See also:\n// - Properties supported in Oniguruma: <github.com/kkos/oniguruma/blob/master/doc/UNICODE_PROPERTIES>\n// - Properties supported in JS by spec version: <github.com/eslint-community/regexpp/blob/main/src/unicode/properties.ts>\nconst JsUnicodePropertyMap = /* @__PURE__ */ new Map(\n`C Other\nCc Control cntrl\nCf Format\nCn Unassigned\nCo Private_Use\nCs Surrogate\nL Letter\nLC Cased_Letter\nLl Lowercase_Letter\nLm Modifier_Letter\nLo Other_Letter\nLt Titlecase_Letter\nLu Uppercase_Letter\nM Mark Combining_Mark\nMc Spacing_Mark\nMe Enclosing_Mark\nMn Nonspacing_Mark\nN Number\nNd Decimal_Number digit\nNl Letter_Number\nNo Other_Number\nP Punctuation punct\nPc Connector_Punctuation\nPd Dash_Punctuation\nPe Close_Punctuation\nPf Final_Punctuation\nPi Initial_Punctuation\nPo Other_Punctuation\nPs Open_Punctuation\nS Symbol\nSc Currency_Symbol\nSk Modifier_Symbol\nSm Math_Symbol\nSo Other_Symbol\nZ Separator\nZl Line_Separator\nZp Paragraph_Separator\nZs Space_Separator\nASCII\nASCII_Hex_Digit AHex\nAlphabetic Alpha\nAny\nAssigned\nBidi_Control Bidi_C\nBidi_Mirrored Bidi_M\nCase_Ignorable CI\nCased\nChanges_When_Casefolded CWCF\nChanges_When_Casemapped CWCM\nChanges_When_Lowercased CWL\nChanges_When_NFKC_Casefolded CWKCF\nChanges_When_Titlecased CWT\nChanges_When_Uppercased CWU\nDash\nDefault_Ignorable_Code_Point DI\nDeprecated Dep\nDiacritic Dia\nEmoji\nEmoji_Component EComp\nEmoji_Modifier EMod\nEmoji_Modifier_Base EBase\nEmoji_Presentation EPres\nExtended_Pictographic ExtPict\nExtender Ext\nGrapheme_Base Gr_Base\nGrapheme_Extend Gr_Ext\nHex_Digit Hex\nIDS_Binary_Operator IDSB\nIDS_Trinary_Operator IDST\nID_Continue IDC\nID_Start IDS\nIdeographic Ideo\nJoin_Control Join_C\nLogical_Order_Exception LOE\nLowercase Lower\nMath\nNoncharacter_Code_Point NChar\nPattern_Syntax Pat_Syn\nPattern_White_Space Pat_WS\nQuotation_Mark QMark\nRadical\nRegional_Indicator RI\nSentence_Terminal STerm\nSoft_Dotted SD\nTerminal_Punctuation Term\nUnified_Ideograph UIdeo\nUppercase Upper\nVariation_Selector VS\nWhite_Space space\nXID_Continue XIDC\nXID_Start XIDS`.\n  split(/\\s/).\n  map(p => [slug(p), p])\n);\n\nconst LowerToAlternativeLowerCaseMap = new Map([\n  ['s', cp(0x17F)], // s, ſ\n  [cp(0x17F), 's'], // ſ, s\n]);\n\nconst LowerToAlternativeUpperCaseMap = new Map([\n  [cp(0xDF), cp(0x1E9E)], // ß, ẞ\n  [cp(0x6B), cp(0x212A)], // k, K (Kelvin)\n  [cp(0xE5), cp(0x212B)], // å, Å (Angstrom)\n  [cp(0x3C9), cp(0x2126)], // ω, Ω (Ohm)\n]);\n\n// See <github.com/node-unicode/unicode-16.0.0/tree/main/General_Category/Titlecase_Letter>\nconst LowerToTitleCaseMap = new Map([\n  titleEntry(0x1C5),\n  titleEntry(0x1C8),\n  titleEntry(0x1CB),\n  titleEntry(0x1F2),\n  ...titleRange(0x1F88, 0x1F8F),\n  ...titleRange(0x1F98, 0x1F9F),\n  ...titleRange(0x1FA8, 0x1FAF),\n  titleEntry(0x1FBC),\n  titleEntry(0x1FCC),\n  titleEntry(0x1FFC),\n]);\n\n// Unlike Onig's Unicode properties via `\\p` and `\\P`, these names are case sensitive and don't\n// allow inserting whitespace and underscores. Definitions at\n// <github.com/kkos/oniguruma/blob/master/doc/RE> (see: POSIX bracket: Unicode Case)\n// Note: Handling in the transformer assumes all values here are a single, negateable node that's\n// not pre-negated at the top level. It also uses ASCII versions of `graph` and `print` for target\n// `ES2018` (which doesn't allow intersection) if `accuracy` isn't `strict`\nconst PosixClassMap = new Map([\n  ['alnum', r`[\\p{Alpha}\\p{Nd}]`],\n  ['alpha', r`\\p{Alpha}`],\n  ['ascii', r`\\p{ASCII}`],\n  ['blank', r`[\\p{Zs}\\t]`],\n  ['cntrl', r`\\p{Cc}`],\n  ['digit', r`\\p{Nd}`],\n  ['graph', r`[\\P{space}&&\\P{Cc}&&\\P{Cn}&&\\P{Cs}]`],\n  ['lower', r`\\p{Lower}`],\n  ['print', r`[[\\P{space}&&\\P{Cc}&&\\P{Cn}&&\\P{Cs}]\\p{Zs}]`],\n  ['punct', r`[\\p{P}\\p{S}]`], // Updated value from Onig 6.9.9; changed from Unicode `\\p{punct}`\n  ['space', r`\\p{space}`],\n  ['upper', r`\\p{Upper}`],\n  ['word', r`[\\p{Alpha}\\p{M}\\p{Nd}\\p{Pc}]`],\n  ['xdigit', r`\\p{AHex}`],\n]);\n\nfunction range(start, end) {\n  // const range = Array.from(Array(end + 1 - start), (_, i) => i + start);\n  // const range = Array(end + 1 - start).fill(start).map((x, i) => x + i);\n  const range = [];\n  for (let i = start; i <= end; i++) {\n    range.push(i);\n  }\n  return range;\n}\n\nfunction titleEntry(codePoint) {\n  const char = cp(codePoint);\n  return [char.toLowerCase(), char];\n}\n\nfunction titleRange(start, end) {\n  return range(start, end).map(codePoint => titleEntry(codePoint));\n}\n\nconst UnicodePropertiesWithSpecificCase = new Set([\n  'Lower', 'Lowercase',\n  'Upper', 'Uppercase',\n  'Ll', 'Lowercase_Letter',\n  'Lt', 'Titlecase_Letter',\n  'Lu', 'Uppercase_Letter',\n  // The `Changes_When_*` properties (and their aliases) could be included, but they're very rare.\n  // Some other properties include a handful of chars with specific cases only, but these chars are\n  // generally extreme edge cases and using such properties case insensitively generally produces\n  // undesired behavior anyway\n]);\n\nexport {\n  asciiSpaceChar,\n  defaultWordChar,\n  getIgnoreCaseMatchChars,\n  JsUnicodePropertyMap,\n  PosixClassMap,\n  UnicodePropertiesWithSpecificCase,\n};\n", "import {Accuracy, Target} from './options.js';\nimport {asciiSpaceChar, defaultWordChar, JsUnicodePropertyMap, PosixClassMap} from './unicode.js';\nimport {cp, getNewCurrentFlags, getOrInsert, isMinTarget, r} from './utils.js';\nimport {createAlternative, createAssertion, createBackreference, createCapturingGroup, createCharacter, createCharacterClass, createCharacterSet, createGroup, createLookaroundAssertion, createQuantifier, createSubroutine, createUnicodeProperty, hasOnlyChild, parse, slug} from 'oniguruma-parser/parser';\nimport {traverse} from 'oniguruma-parser/traverser';\n/**\n@import {CapturingGroupNode, OnigurumaAst, Node} from 'oniguruma-parser/parser';\n@import {Visitor} from 'oniguruma-parser/traverser';\n*/\n\n/**\n@typedef {\n  OnigurumaAst & {\n    options: {\n      disable: {[key: string]: boolean};\n      force: {[key: string]: boolean};\n    };\n    _originMap: Map<CapturingGroupNode, CapturingGroupNode>;\n    _strategy: string | null;\n  }\n} RegexPlusAst\n*/\n/**\nTransforms an Oniguruma AST in-place to a [Regex+](https://github.com/slevithan/regex) AST.\nAssumes target ES2025, expecting the generator to down-convert to the desired JS target version.\n\nRegex+'s syntax and behavior is a strict superset of native JavaScript, so the AST is very close\nto representing native ES2025 `RegExp` but with some added features (atomic groups, possessive\nquantifiers, recursion). The AST doesn't use some of Regex+'s extended features like flag x or\nsubroutines because they follow PCRE behavior and work somewhat differently than in Oniguruma. The\nAST represents what's needed to precisely reproduce Oniguruma behavior using Regex+.\n@param {OnigurumaAst} ast\n@param {{\n  accuracy?: keyof Accuracy;\n  asciiWordBoundaries?: boolean;\n  avoidSubclass?: boolean;\n  bestEffortTarget?: keyof Target;\n}} [options]\n@returns {RegexPlusAst}\n*/\nfunction transform(ast, options) {\n  const opts = {\n    // A couple edge cases exist where options `accuracy` and `bestEffortTarget` are used:\n    // - `CharacterSet` kind `text_segment` (`\\X`): An exact representation would require heavy\n    //   Unicode data; a best-effort approximation requires knowing the target.\n    // - `CharacterSet` kind `posix` with values `graph` and `print`: Their complex Unicode\n    //   representations would be hard to change to ASCII versions after the fact in the generator\n    //   based on `target`/`accuracy`, so produce the appropriate structure here.\n    accuracy: 'default',\n    asciiWordBoundaries: false,\n    avoidSubclass: false,\n    bestEffortTarget: 'ES2025',\n    ...options,\n  };\n  // Add `parent` properties to all nodes to help during traversal; also expected by the generator\n  addParentProperties(ast);\n  const firstPassState = {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    flagDirectivesByAlt: new Map(),\n    jsGroupNameMap: new Map(),\n    minTargetEs2024: isMinTarget(opts.bestEffortTarget, 'ES2024'),\n    passedLookbehind: false,\n    strategy: null,\n    // Subroutines can appear before the groups they ref, so collect reffed nodes for a second pass \n    subroutineRefMap: new Map(),\n    supportedGNodes: new Set(),\n    digitIsAscii: ast.flags.digitIsAscii,\n    spaceIsAscii: ast.flags.spaceIsAscii,\n    wordIsAscii: ast.flags.wordIsAscii,\n  };\n  traverse(ast, FirstPassVisitor, firstPassState);\n  // Global flags modified by the first pass\n  const globalFlags = {\n    dotAll: ast.flags.dotAll,\n    ignoreCase: ast.flags.ignoreCase,\n  };\n  // The interplay of subroutines (with Onig's unique rules/behavior for them; see comments in the\n  // parser for details) with backref multiplexing (a unique Onig feature), flag modifiers, and\n  // duplicate group names (which might be indirectly referenced by subroutines even though\n  // subroutines can't directly reference duplicate names) is extremely complicated to emulate in\n  // JS in a way that handles all edge cases, so we need multiple passes to do it\n  const secondPassState = {\n    currentFlags: globalFlags,\n    prevFlags: null,\n    globalFlags,\n    groupOriginByCopy: new Map(),\n    groupsByName: new Map(),\n    multiplexCapturesToLeftByRef: new Map(),\n    openRefs: new Map(),\n    reffedNodesByReferencer: new Map(),\n    subroutineRefMap: firstPassState.subroutineRefMap,\n  };\n  traverse(ast, SecondPassVisitor, secondPassState);\n  const thirdPassState = {\n    groupsByName: secondPassState.groupsByName,\n    highestOrphanBackref: 0,\n    numCapturesToLeft: 0,\n    reffedNodesByReferencer: secondPassState.reffedNodesByReferencer,\n  };\n  traverse(ast, ThirdPassVisitor, thirdPassState);\n  ast._originMap = secondPassState.groupOriginByCopy;\n  ast._strategy = firstPassState.strategy;\n  return ast;\n}\n\nconst /** @type {Visitor} */ FirstPassVisitor = {\n  AbsenceFunction({node, parent, replaceWith}) {\n    const {body, kind} = node;\n    if (kind === 'repeater') {\n      // Convert `(?~…)` to `(?:(?:(?!…)\\p{Any})*)`\n      const innerGroup = createGroup();\n      innerGroup.body[0].body.push(\n        // Insert own alts as `body`\n        createLookaroundAssertion({negate: true, body}),\n        createUnicodeProperty('Any')\n      );\n      const outerGroup = createGroup();\n      outerGroup.body[0].body.push(\n        createQuantifier('greedy', 0, Infinity, innerGroup)\n      );\n      replaceWith(setParentDeep(outerGroup, parent), {traverse: true});\n    } else {\n      throw new Error(`Unsupported absence function \"(?~|\"`);\n    }\n  },\n\n  Alternative: {\n    enter({node, parent, key}, {flagDirectivesByAlt}) {\n      // Look for own-level flag directives when entering an alternative because after traversing\n      // the directive itself, any subsequent flag directives will no longer be at the same level\n      const flagDirectives = node.body.filter(el => el.kind === 'flags');\n      for (let i = key + 1; i < parent.body.length; i++) {\n        const forwardSiblingAlt = parent.body[i];\n        getOrInsert(flagDirectivesByAlt, forwardSiblingAlt, []).push(...flagDirectives);\n      }\n    },\n    exit({node}, {flagDirectivesByAlt}) {\n      // Wait until exiting to wrap an alternative's nodes with flag groups that extend flag\n      // directives from prior sibling alternatives, because doing this at the end allows inner\n      // nodes to accurately check their level in the tree\n      if (flagDirectivesByAlt.get(node)?.length) {\n        const flags = getCombinedFlagModsFromFlagNodes(flagDirectivesByAlt.get(node));\n        if (flags) {\n          const flagGroup = createGroup({flags});\n          flagGroup.body[0].body = node.body;\n          node.body = [setParentDeep(flagGroup, node)];\n        }\n      }\n    },\n  },\n\n  Assertion({node, parent, key, container, root, remove, replaceWith}, state) {\n    const {kind, negate} = node;\n    const {asciiWordBoundaries, avoidSubclass, supportedGNodes, wordIsAscii} = state;\n    if (kind === 'text_segment_boundary') {\n      // Supported by the parser but not yet for transpilation\n      throw new Error(`Unsupported text segment boundary \"\\\\${negate ? 'Y' : 'y'}\"`);\n    } else if (kind === 'line_end') {\n      replaceWith(setParentDeep(createLookaroundAssertion({body: [\n        createAlternative({body: [createAssertion('string_end')]}),\n        createAlternative({body: [createCharacter(10)]}), // `\\n`\n      ]}), parent));\n    } else if (kind === 'line_start') {\n      // Onig's `^` doesn't match after a string-terminating line feed\n      replaceWith(setParentDeep(parseFragment(r`(?<=\\A|\\n(?!\\z))`, {skipLookbehindValidation: true}), parent));\n    } else if (kind === 'search_start') {\n      if (supportedGNodes.has(node)) {\n        root.flags.sticky = true;\n        remove();\n      } else {\n        const prev = container[key - 1]; // parent.body[key - 1]\n        // Not all ways of blocking the `\\G` from matching are covered here (ex: a node prior to\n        // the `prev` node could block), but blocked `\\G` is an edge case and it's okay if some\n        // blocked cases result in the standard error for being unsupported without a subclass\n        if (prev && isAlwaysNonZeroLength(prev)) {\n          replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n        } else if (avoidSubclass) {\n          throw new Error(r`Uses \"\\G\" in a way that requires a subclass`);\n        } else {\n          replaceWith(setParent(createAssertion('string_start'), parent));\n          state.strategy = 'clip_search';\n        }\n      }\n    } else if (kind === 'string_end' || kind === 'string_start') {\n      // Don't need transformation since JS flag m isn't used\n    } else if (kind === 'string_end_newline') {\n      replaceWith(setParentDeep(parseFragment(r`(?=\\n?\\z)`), parent));\n    } else if (kind === 'word_boundary') {\n      if (!wordIsAscii && !asciiWordBoundaries) {\n        const b = `(?:(?<=${defaultWordChar})(?!${defaultWordChar})|(?<!${defaultWordChar})(?=${defaultWordChar}))`;\n        const B = `(?:(?<=${defaultWordChar})(?=${defaultWordChar})|(?<!${defaultWordChar})(?!${defaultWordChar}))`;\n        replaceWith(setParentDeep(parseFragment(negate ? B : b), parent));\n      }\n    } else {\n      throw new Error(`Unexpected assertion kind \"${kind}\"`);\n    }\n  },\n\n  Backreference({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidJsGroupName(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n\n  CapturingGroup({node}, {jsGroupNameMap, subroutineRefMap}) {\n    let {name} = node;\n    if (name && !isValidJsGroupName(name)) {\n      name = getAndStoreJsGroupName(name, jsGroupNameMap);\n      node.name = name;\n    }\n    subroutineRefMap.set(node.number, node);\n    if (name) {\n      subroutineRefMap.set(name, node);\n    }\n  },\n\n  CharacterClassRange({node, parent, replaceWith}) {\n    if (parent.kind === 'intersection') {\n      // JS doesn't allow intersection with ranges without a wrapper class\n      const cc = createCharacterClass({body: [node]});\n      replaceWith(setParentDeep(cc, parent), {traverse: true});\n    }\n  },\n\n  CharacterSet({node, parent, replaceWith}, {accuracy, minTargetEs2024, digitIsAscii, spaceIsAscii, wordIsAscii}) {\n    const {kind, negate, value} = node;\n    // Flag D with `\\d`, `\\p{Digit}`, `[[:digit:]]`\n    if (digitIsAscii && (kind === 'digit' || value === 'digit')) {\n      replaceWith(setParent(createCharacterSet('digit', {negate}), parent));\n      return;\n    }\n    // Flag S with `\\s`, `\\p{Space}`, `[[:space:]]`\n    if (spaceIsAscii && (kind === 'space' || value === 'space')) {\n      replaceWith(setParentDeep(setNegate(parseFragment(asciiSpaceChar), negate), parent));\n      return;\n    }\n    // Flag W with `\\w`, `\\p{Word}`, `[[:word:]]`\n    if (wordIsAscii && (kind === 'word' || value === 'word')) {\n      replaceWith(setParent(createCharacterSet('word', {negate}), parent));\n      return;\n    }\n    if (kind === 'any') {\n      replaceWith(setParent(createUnicodeProperty('Any'), parent));\n    } else if (kind === 'digit') {\n      replaceWith(setParent(createUnicodeProperty('Nd', {negate}), parent));\n    } else if (kind === 'dot') {\n      // No-op; doesn't need transformation\n    } else if (kind === 'text_segment') {\n      if (accuracy === 'strict') {\n        throw new Error(r`Use of \"\\X\" requires non-strict accuracy`);\n      }\n      // Emoji pattern based on <github.com/slevithan/emoji-regex-xs> but adapted for our use case\n      // Note: Not using raw strings to work around Bun ≤ 1.1.34 issue <github.com/oven-sh/bun/issues/7540>\n      const eBase = '\\\\p{Emoji}(?:\\\\p{EMod}|\\\\uFE0F\\\\u20E3?|[\\\\x{E0020}-\\\\x{E007E}]+\\\\x{E007F})?';\n      const emoji = r`\\p{RI}{2}|${eBase}(?:\\u200D${eBase})*`;\n      replaceWith(setParentDeep(parseFragment(\n        // Close approximation of an extended grapheme cluster; see: <unicode.org/reports/tr29/>\n        r`(?>\\r\\n|${minTargetEs2024 ? r`\\p{RGI_Emoji}` : emoji}|\\P{M}\\p{M}*)`,\n        // Allow JS property `RGI_Emoji` through\n        {skipPropertyNameValidation: true}\n      ), parent));\n    } else if (kind === 'hex') {\n      replaceWith(setParent(createUnicodeProperty('AHex', {negate}), parent));\n    } else if (kind === 'newline') {\n      replaceWith(setParentDeep(parseFragment(negate ? '[^\\n]' : '(?>\\r\\n?|[\\n\\v\\f\\x85\\u2028\\u2029])'), parent));\n    } else if (kind === 'posix') {\n      if (!minTargetEs2024 && (value === 'graph' || value === 'print')) {\n        if (accuracy === 'strict') {\n          throw new Error(`POSIX class \"${value}\" requires min target ES2024 or non-strict accuracy`);\n        }\n        let ascii = {\n          graph: '!-~',\n          print: ' -~',\n        }[value];\n        if (negate) {\n          // POSIX classes are always nested in a char class; manually invert the range rather than\n          // using `[^…]` so it can be unwrapped since ES2018 doesn't support nested classes\n          ascii = `\\0-${cp(ascii.codePointAt(0) - 1)}${cp(ascii.codePointAt(2) + 1)}-\\u{10FFFF}`;\n        }\n        replaceWith(setParentDeep(parseFragment(`[${ascii}]`), parent));\n      } else {\n        replaceWith(setParentDeep(setNegate(parseFragment(PosixClassMap.get(value)), negate), parent));\n      }\n    } else if (kind === 'property') {\n      if (!JsUnicodePropertyMap.has(slug(value))) {\n        // Assume it's a script; no error checking is the price for avoiding heavyweight Unicode\n        // data for all script names\n        node.key = 'sc';\n      }\n    } else if (kind === 'space') {\n      // Can't use JS's Unicode `\\s` since unlike Onig it includes `\\uFEFF` and excludes `\\x85`\n      replaceWith(setParent(createUnicodeProperty('space', {negate}), parent));\n    } else if (kind === 'word') {\n      replaceWith(setParentDeep(setNegate(parseFragment(defaultWordChar), negate), parent));\n    } else {\n      throw new Error(`Unexpected character set kind \"${kind}\"`);\n    }\n  },\n\n  Directive({node, parent, root, remove, replaceWith, removeAllPrevSiblings, removeAllNextSiblings}) {\n    const {kind, flags} = node;\n    if (kind === 'flags') {\n      if (!flags.enable && !flags.disable) {\n        // Flag directive without flags; ex: `(?-)`, `(?--)`\n        remove();\n      } else {\n        const flagGroup = createGroup({flags});\n        flagGroup.body[0].body = removeAllNextSiblings();\n        replaceWith(setParentDeep(flagGroup, parent), {traverse: true});\n      }\n    } else if (kind === 'keep') {\n      const firstAlt = root.body[0];\n      // Supporting a full-pattern wrapper around `\\K` enables use with flag modifiers\n      const hasWrapperGroup =\n        root.body.length === 1 &&\n        // Not emulatable if within a `CapturingGroup`\n        hasOnlyChild(firstAlt, {type: 'Group'}) &&\n        firstAlt.body[0].body.length === 1;\n      const topLevel = hasWrapperGroup ? firstAlt.body[0] : root;\n      if (parent.parent !== topLevel || topLevel.body.length > 1) {\n        throw new Error(r`Uses \"\\K\" in a way that's unsupported`);\n      }\n      const lookbehind = createLookaroundAssertion({behind: true});\n      lookbehind.body[0].body = removeAllPrevSiblings();\n      replaceWith(setParentDeep(lookbehind, parent));\n    } else {\n      throw new Error(`Unexpected directive kind \"${kind}\"`);\n    }\n  },\n\n  Flags({node, parent}) {\n    // Throw for flags supported by the parser but not yet for transpilation\n    if (node.posixIsAscii) {\n      throw new Error('Unsupported flag \"P\"');\n    }\n    if (node.textSegmentMode === 'word') {\n      throw new Error('Unsupported flag \"y{w}\"');\n    }\n    // Remove Onig flags that aren't available in JS\n    [ 'digitIsAscii', // Flag D\n      'extended', // Flag x\n      'posixIsAscii', // Flag P\n      'spaceIsAscii', // Flag S\n      'wordIsAscii', // Flag W\n      'textSegmentMode', // Flag y{g} or y{w}\n    ].forEach(f => delete node[f]);\n    Object.assign(node, {\n      // JS flag g; no Onig equiv\n      global: false,\n      // JS flag d; no Onig equiv\n      hasIndices: false,\n      // JS flag m; no Onig equiv but its behavior is always on in Onig. Onig's only line break\n      // char is line feed, unlike JS, so this flag isn't used since it would produce inaccurate\n      // results (also allows `^` and `$` to be used in the generator for string start and end)\n      multiline: false,\n      // JS flag y; no Onig equiv, but used for `\\G` emulation\n      sticky: node.sticky ?? false,\n      // Note: Regex+ doesn't allow explicitly adding flags it handles implicitly, so leave out\n      // properties `unicode` (JS flag u) and `unicodeSets` (JS flag v). Keep the existing values\n      // for `ignoreCase` (flag i) and `dotAll` (JS flag s, but Onig flag m)\n    });\n    // Options accepted by Regex+; see <github.com/slevithan/regex#-options>\n    parent.options = {\n      disable: {\n        // Onig uses different rules for flag x than Regex+, so disable the implicit flag\n        x: true,\n        // Onig has no flag to control \"named capture only\" mode but contextually applies its\n        // behavior when named capturing is used, so disable Regex+'s implicit flag for it\n        n: true,\n      },\n      force: {\n        // Always add flag v because we're generating an AST that relies on it (it enables JS\n        // support for Onig features nested classes, intersection, Unicode properties, etc.).\n        // However, the generator might disable flag v based on its `target` option\n        v: true,\n      },\n    };\n  },\n\n  Group({node}) {\n    if (!node.flags) {\n      return;\n    }\n    const {enable, disable} = node.flags;\n    // Onig's flag x (`extended`) isn't available in JS\n    enable?.extended && delete enable.extended;\n    disable?.extended && delete disable.extended;\n    // JS doesn't support flag groups that enable and disable the same flag; ex: `(?i-i:)`\n    enable?.dotAll && disable?.dotAll && delete enable.dotAll;\n    enable?.ignoreCase && disable?.ignoreCase && delete enable.ignoreCase;\n    // Cleanup\n    enable && !Object.keys(enable).length && delete node.flags.enable;\n    disable && !Object.keys(disable).length && delete node.flags.disable;\n    !node.flags.enable && !node.flags.disable && delete node.flags;\n  },\n\n  LookaroundAssertion({node}, state) {\n    const {kind} = node;\n    if (kind === 'lookbehind') {\n      state.passedLookbehind = true;\n    }\n  },\n\n  NamedCallout({node, parent, replaceWith}) {\n    const {kind} = node;\n    if (kind === 'fail') {\n      replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n    } else {\n      throw new Error(`Unsupported named callout \"(*${kind.toUpperCase()}\"`);\n    }\n  },\n\n  Quantifier({node}) {\n    if (node.body.type === 'Quantifier') {\n      // Change e.g. `a**` to `(?:a*)*`\n      const group = createGroup();\n      group.body[0].body.push(node.body);\n      node.body = setParentDeep(group, node);\n    }\n  },\n\n  Regex: {\n    enter({node}, {supportedGNodes}) {\n      // For `\\G` to be accurately emulatable using JS flag y, it must be at (and only at) the start\n      // of every top-level alternative (with complex rules for what determines being at the start).\n      // Additional `\\G` error checking in `Assertion` visitor\n      const leadingGs = [];\n      let hasAltWithLeadG = false;\n      let hasAltWithoutLeadG = false;\n      for (const alt of node.body) {\n        if (alt.body.length === 1 && alt.body[0].kind === 'search_start') {\n          // Remove the `\\G` (leaving behind an empty alternative, and without adding JS flag y)\n          // since a top-level alternative that includes only `\\G` always matches at the start of the\n          // match attempt. Note that this is based on Oniguruma's rules, and is different than other\n          // regex flavors where `\\G` matches at the end of the previous match (a subtle distinction\n          // that's relevant after zero-length matches)\n          alt.body.pop();\n        } else {\n          const leadingG = getLeadingG(alt.body);\n          if (leadingG) {\n            hasAltWithLeadG = true;\n            Array.isArray(leadingG) ?\n              leadingGs.push(...leadingG) :\n              leadingGs.push(leadingG);\n          } else {\n            hasAltWithoutLeadG = true;\n          }\n        }\n      }\n      if (hasAltWithLeadG && !hasAltWithoutLeadG) {\n        // Supported `\\G` nodes will be removed (and add flag y) when traversed\n        leadingGs.forEach(g => supportedGNodes.add(g));\n      }\n    },\n    exit(_, {accuracy, passedLookbehind, strategy}) {\n      if (accuracy === 'strict' && passedLookbehind && strategy) {\n        throw new Error(r`Uses \"\\G\" in a way that requires non-strict accuracy`);\n      }\n    },\n  },\n\n  Subroutine({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidJsGroupName(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n};\n\nconst /** @type {Visitor} */ SecondPassVisitor = {\n  Backreference({node}, {multiplexCapturesToLeftByRef, reffedNodesByReferencer}) {\n    const {orphan, ref} = node;\n    if (!orphan) {\n      // Copy the current state for later multiplexing expansion. That's done in a subsequent pass\n      // because backref numbers need to be recalculated after subroutine expansion\n      reffedNodesByReferencer.set(node, [...multiplexCapturesToLeftByRef.get(ref).map(({node}) => node)]);\n    }\n  },\n\n  CapturingGroup: {\n    enter(\n      { node,\n        parent,\n        replaceWith,\n        skip,\n      },\n      { groupOriginByCopy,\n        groupsByName,\n        multiplexCapturesToLeftByRef,\n        openRefs,\n        reffedNodesByReferencer,\n      }\n    ) {\n      // Has value if we're within a subroutine expansion\n      const origin = groupOriginByCopy.get(node);\n\n      // ## Handle recursion; runs after subroutine expansion\n      if (origin && openRefs.has(node.number)) {\n        // Recursive subroutines don't affect any following backrefs to their `ref` (unlike other\n        // subroutines), so don't wrap with a capture. The reffed group might have its name removed\n        // due to later subroutine expansion\n        const recursion = setParent(createRecursion(node.number), parent);\n        reffedNodesByReferencer.set(recursion, openRefs.get(node.number));\n        replaceWith(recursion);\n        return;\n      }\n      openRefs.set(node.number, node);\n\n      // ## Track data for backref multiplexing\n      multiplexCapturesToLeftByRef.set(node.number, []);\n      if (node.name) {\n        getOrInsert(multiplexCapturesToLeftByRef, node.name, []);\n      }\n      const multiplexNodes = multiplexCapturesToLeftByRef.get(node.name ?? node.number);\n      for (let i = 0; i < multiplexNodes.length; i++) {\n        // Captures added via subroutine expansion (maybe indirectly because they were descendant\n        // captures of the reffed group or in a nested subroutine expansion) form a set with their\n        // origin group and any other copies of it added via subroutines. Only the most recently\n        // matched within this set is added to backref multiplexing. So search the list of already-\n        // tracked multiplexed nodes for this group name or number to see if there's a node being\n        // replaced by this capture\n        const multiplex = multiplexNodes[i];\n        if (\n          // This group is from subroutine expansion, and there's a multiplex value from either the\n          // origin node or a prior subroutine expansion group with the same origin\n          (origin === multiplex.node || (origin && origin === multiplex.origin)) ||\n          // This group is not from subroutine expansion, and it comes after a subroutine expansion\n          // group that refers to this group\n          node === multiplex.origin\n        ) {\n          multiplexNodes.splice(i, 1);\n          break;\n        }\n      }\n      multiplexCapturesToLeftByRef.get(node.number).push({node, origin});\n      if (node.name) {\n        multiplexCapturesToLeftByRef.get(node.name).push({node, origin});\n      }\n\n      // ## Track data for duplicate names\n      // Pre-ES2025 doesn't allow duplicate names, but ES2025 allows duplicate names that are\n      // unique per mutually exclusive alternation path. However, Oniguruma's handling for named\n      // subpatterns on match results means we can't use this ES2025 feature even when in an ES2025\n      // env. So, if using a duplicate name, remove the name from all but the first instance that\n      // wasn't created by subroutine expansion\n      if (node.name) {\n        const groupsWithSameName = getOrInsert(groupsByName, node.name, new Map());\n        let hasDuplicateNameToRemove = false;\n        if (origin) {\n          // Subroutines and their child captures shouldn't hold duplicate names in the final state\n          hasDuplicateNameToRemove = true;\n        } else {\n          for (const groupInfo of groupsWithSameName.values()) {\n            if (!groupInfo.hasDuplicateNameToRemove) {\n              // Will change to an unnamed capture in a later pass\n              hasDuplicateNameToRemove = true;\n              break;\n            }\n          }\n        }\n        groupsByName.get(node.name).set(node, {node, hasDuplicateNameToRemove});\n      }\n    },\n    exit({node}, {openRefs}) {\n      openRefs.delete(node.number);\n    },\n  },\n\n  Group: {\n    enter({node}, state) {\n      // Flag directives have already been converted to flag groups by the previous pass\n      state.prevFlags = state.currentFlags;\n      if (node.flags) {\n        state.currentFlags = getNewCurrentFlags(state.currentFlags, node.flags);\n      }\n    },\n    exit(_, state) {\n      state.currentFlags = state.prevFlags;\n    },\n  },\n\n  Subroutine({node, parent, replaceWith}, state) {\n    const {isRecursive, ref} = node;\n\n    // Subroutine nodes with `isRecursive` are created during the current traversal; they're only\n    // traversed here if a recursive subroutine created during traversal is then copied by a\n    // subroutine expansion, e.g. with `(?<a>\\g<a>)\\g<a>`\n    if (isRecursive) {\n      // Immediate parent is an alternative or quantifier; can skip\n      let reffed = parent;\n      while ((reffed = reffed.parent)) {\n        if (reffed.type === 'CapturingGroup' && (reffed.name === ref || reffed.number === ref)) {\n          break;\n        }\n      }\n      // Track the referenced node because `ref`s are rewritten in a subsequent pass; capturing\n      // group names and numbers might change due to subroutine expansion and duplicate group names\n      state.reffedNodesByReferencer.set(node, reffed);\n      return;\n    }\n\n    const reffedGroupNode = state.subroutineRefMap.get(ref);\n    // Other forms of recursion are handled by the `CapturingGroup` visitor\n    const isGlobalRecursion = ref === 0;\n    const expandedSubroutine = isGlobalRecursion ?\n      createRecursion(0) :\n      // The reffed group might itself contain subroutines, which are expanded during sub-traversal\n      cloneCapturingGroup(reffedGroupNode, state.groupOriginByCopy, null);\n    let replacement = expandedSubroutine;\n    if (!isGlobalRecursion) {\n      // Subroutines take their flags from the reffed group, not the flags surrounding themselves\n      const reffedGroupFlagMods = getCombinedFlagModsFromFlagNodes(getAllParents(\n        reffedGroupNode,\n        p => p.type === 'Group' && !!p.flags\n      ));\n      const reffedGroupFlags = reffedGroupFlagMods ?\n        getNewCurrentFlags(state.globalFlags, reffedGroupFlagMods) :\n        state.globalFlags;\n      if (!areFlagsEqual(reffedGroupFlags, state.currentFlags)) {\n        replacement = createGroup({\n          flags: getFlagModsFromFlags(reffedGroupFlags),\n        });\n        replacement.body[0].body.push(expandedSubroutine);\n      }\n    }\n    replaceWith(setParentDeep(replacement, parent), {traverse: !isGlobalRecursion});\n  },\n};\n\nconst /** @type {Visitor} */ ThirdPassVisitor = {\n  Backreference({node, parent, replaceWith}, state) {\n    if (node.orphan) {\n      state.highestOrphanBackref = Math.max(state.highestOrphanBackref, node.ref);\n      // Don't renumber; used with `allowOrphanBackrefs`\n      return;\n    }\n    const reffedNodes = state.reffedNodesByReferencer.get(node);\n    const participants = reffedNodes.filter(reffed => canParticipateWithNode(reffed, node));\n    // For the backref's `ref`, use `number` rather than `name` because group names might have been\n    // removed if they're duplicates within their alternation path, or they might be removed later\n    // by the generator (depending on target) if they're duplicates within the overall pattern.\n    // Backrefs must come after groups they ref, so reffed node `number`s are already recalculated\n    if (!participants.length) {\n      // If no participating capture, convert backref to to `(?!)`; backrefs to nonparticipating\n      // groups can't match in Onig but match the empty string in JS\n      replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n    } else if (participants.length > 1) {\n      // Multiplex for backrefs to duplicate capture names; try them in reverse order\n      const group = createGroup({\n        atomic: true,\n        body: participants.reverse().map(reffed => createAlternative({\n          body: [createBackreference(reffed.number)],\n        })),\n      });\n      replaceWith(setParentDeep(group, parent));\n    } else {\n      node.ref = participants[0].number;\n    }\n  },\n\n  CapturingGroup({node}, state) {\n    // Recalculate the number since the current value might be wrong due to subroutine expansion\n    node.number = ++state.numCapturesToLeft;\n    if (node.name) {\n      // Removing duplicate names here rather than in an earlier pass avoids extra complexity when\n      // handling subroutine expansion and backref multiplexing\n      if (state.groupsByName.get(node.name).get(node).hasDuplicateNameToRemove) {\n        delete node.name;\n      }\n    }\n  },\n\n  Regex: {\n    exit({node}, state) {\n      // HACK: Add unnamed captures to the end of the regex if needed to allow orphaned backrefs\n      // to be valid in JS with flag u/v. This is needed to support TextMate grammars, which\n      // replace numbered backrefs in their `end` pattern with values matched by captures in their\n      // `begin` pattern! See <github.com/microsoft/vscode-textmate/blob/7e0ea282f4f25fef12a6c84fa4fa7266f67b58dc/src/rule.ts#L661-L663>\n      // An `end` pattern, prior to this substitution, might have backrefs to a group that doesn't\n      // exist within `end`. This presents a dilemma since both Oniguruma and JS (with flag u/v)\n      // error for backrefs to undefined captures. So adding captures to the end is a solution that\n      // doesn't change what the regex matches, and lets invalid numbered backrefs through. Note:\n      // Orphan backrefs are only allowed if `allowOrphanBackrefs` is enabled\n      const numCapsNeeded = Math.max(state.highestOrphanBackref - state.numCapturesToLeft, 0);\n      for (let i = 0; i < numCapsNeeded; i++) {\n        const emptyCapture = createCapturingGroup();\n        node.body.at(-1).body.push(emptyCapture);\n      }\n    },\n  },\n\n  Subroutine({node}, state) {\n    if (!node.isRecursive || node.ref === 0) {\n      return;\n    }\n    // For the recursion's `ref`, use `number` rather than `name` because group names might have\n    // been removed if they're duplicates within their alternation path, or they might be removed\n    // later by the generator (depending on target) if they're duplicates within the overall\n    // pattern. Since recursion appears within the group it refs, the reffed node's `number` has\n    // already been recalculated\n    node.ref = state.reffedNodesByReferencer.get(node).number;\n  },\n};\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nfunction addParentProperties(root) {\n  traverse(root, {\n    '*'({node, parent}) {\n      node.parent = parent;\n    },\n  });\n}\n\nfunction areFlagsEqual(a, b) {\n  return a.dotAll === b.dotAll && a.ignoreCase === b.ignoreCase;\n}\n\nfunction canParticipateWithNode(capture, node) {\n  // Walks to the left (prev siblings), down (sibling descendants), up (parent), then back down\n  // (parent's prev sibling descendants) the tree in a loop\n  let rightmostPoint = node;\n  do {\n    if (rightmostPoint.type === 'Regex') {\n      // End of the line; capture is not in node's alternation path\n      return false;\n    }\n    if (rightmostPoint.type === 'Alternative') {\n      // Skip past alts to their parent because we don't want to look at the kids of preceding alts\n      continue;\n    }\n    if (rightmostPoint === capture) {\n      // Capture is ancestor of node\n      return false;\n    }\n    const kidsOfParent = getKids(rightmostPoint.parent);\n    for (const kid of kidsOfParent) {\n      if (kid === rightmostPoint) {\n        // Reached rightmost node in sibling list that we want to consider; break to parent loop\n        break;\n      }\n      if (kid === capture || isAncestorOf(kid, capture)) {\n        return true;\n      }\n    }\n  } while ((rightmostPoint = rightmostPoint.parent));\n  throw new Error('Unexpected path');\n}\n\n// Creates a deep copy of the provided node, with special handling:\n// - Make `parent` props point to their parent in the copy\n// - Update the provided `originMap` for each cloned capturing group (outer and nested)\nfunction cloneCapturingGroup(obj, originMap, up, up2) {\n  const store = Array.isArray(obj) ? [] : {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (key === 'parent') {\n      // If the last cloned item was a container array (for holding kids), use the object above it\n      store.parent = Array.isArray(up) ? up2 : up;\n    } else if (value && typeof value === 'object') {\n      store[key] = cloneCapturingGroup(value, originMap, store, up);\n    } else {\n      if (key === 'type' && value === 'CapturingGroup') {\n        // Key is the copied node, value is the origin node\n        originMap.set(store, originMap.get(obj) ?? obj);\n      }\n      store[key] = value;\n    }\n  }\n  return store;\n}\n\nfunction createRecursion(ref) {\n  const node = createSubroutine(ref);\n  // In the future, the parser will set a `recursive` property on subroutines:\n  // <github.com/slevithan/oniguruma-parser/issues/3>. When that's available, this function won't\n  // be needed and the related logic in this transformer should change (simplify) to use it\n  node.isRecursive = true;\n  return node;\n}\n\nfunction getAllParents(node, filterFn) {\n  const results = [];\n  while ((node = node.parent)) {\n    if (!filterFn || filterFn(node)) {\n      results.push(node);\n    }\n  }\n  return results;\n}\n\n// See also `isValidJsGroupName`\nfunction getAndStoreJsGroupName(name, map) {\n  if (map.has(name)) {\n    return map.get(name);\n  }\n  // Onig group names can't start with `$`, but JS names can\n  const jsName = `$${map.size}_${name.replace(/^[^$_\\p{IDS}]|[^$\\u200C\\u200D\\p{IDC}]/ug, '_')}`;\n  map.set(name, jsName);\n  return jsName;\n}\n\nfunction getCombinedFlagModsFromFlagNodes(flagNodes) {\n  const flagProps = ['dotAll', 'ignoreCase'];\n  const combinedFlags = {enable: {}, disable: {}};\n  flagNodes.forEach(({flags}) => {\n    flagProps.forEach(prop => {\n      if (flags.enable?.[prop]) {\n        // Need to remove `disable` since disabled flags take precedence\n        delete combinedFlags.disable[prop];\n        combinedFlags.enable[prop] = true;\n      }\n      if (flags.disable?.[prop]) {\n        combinedFlags.disable[prop] = true;\n      }\n    });\n  });\n  if (!Object.keys(combinedFlags.enable).length) {\n    delete combinedFlags.enable;\n  }\n  if (!Object.keys(combinedFlags.disable).length) {\n    delete combinedFlags.disable;\n  }\n  if (combinedFlags.enable || combinedFlags.disable) {\n    return combinedFlags;\n  }\n  return null;\n}\n\nfunction getFlagModsFromFlags({dotAll, ignoreCase}) {\n  const mods = {};\n  if (dotAll || ignoreCase) {\n    mods.enable = {};\n    dotAll && (mods.enable.dotAll = true);\n    ignoreCase && (mods.enable.ignoreCase = true);\n  }\n  if (!dotAll || !ignoreCase) {\n    mods.disable = {};\n    !dotAll && (mods.disable.dotAll = true);\n    !ignoreCase && (mods.disable.ignoreCase = true);\n  }\n  return mods;\n}\n\nfunction getKids(node) {\n  if (!node) {\n    throw new Error('Node expected');\n  }\n  // NOTE: Not handling `CharacterClassRange`'s `min`/`max` and `Regex`'s `flags`, only because\n  // they haven't been needed by current callers\n  const {body} = node;\n  return Array.isArray(body) ? body : (body ? [body] : null);\n}\n\nfunction getLeadingG(els) {\n  const firstToConsider = els.find(el => (\n    el.kind === 'search_start' ||\n    isLoneGLookaround(el, {negate: false}) ||\n    !isAlwaysZeroLength(el)\n  ));\n  if (!firstToConsider) {\n    return null;\n  }\n  if (firstToConsider.kind === 'search_start') {\n    return firstToConsider;\n  }\n  if (firstToConsider.type === 'LookaroundAssertion') {\n    return firstToConsider.body[0].body[0];\n  }\n  if (firstToConsider.type === 'CapturingGroup' || firstToConsider.type === 'Group') {\n    const gNodesForGroup = [];\n    // Recursively find `\\G` nodes for all alternatives in the group\n    for (const alt of firstToConsider.body) {\n      const leadingG = getLeadingG(alt.body);\n      if (!leadingG) {\n        // Don't return `gNodesForGroup` collected so far since this alt didn't qualify\n        return null;\n      }\n      Array.isArray(leadingG) ?\n        gNodesForGroup.push(...leadingG) :\n        gNodesForGroup.push(leadingG);\n    }\n    return gNodesForGroup;\n  }\n  return null;\n}\n\nfunction isAncestorOf(node, descendant) {\n  const kids = getKids(node) ?? [];\n  for (const kid of kids) {\n    if (kid === descendant || isAncestorOf(kid, descendant)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAlwaysZeroLength({type}) {\n  return (\n    type === 'Assertion' ||\n    type === 'Directive' ||\n    type === 'LookaroundAssertion'\n  );\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAlwaysNonZeroLength(node) {\n  const types = [\n    'Character',\n    'CharacterClass',\n    'CharacterSet',\n  ];\n  return types.includes(node.type) || (\n    node.type === 'Quantifier' &&\n    node.min &&\n    types.includes(node.body.type)\n  );\n}\n\nfunction isLoneGLookaround(node, options) {\n  const opts = {\n    negate: null,\n    ...options,\n  };\n  return (\n    node.type === 'LookaroundAssertion' &&\n    (opts.negate === null || node.negate === opts.negate) &&\n    node.body.length === 1 &&\n    hasOnlyChild(node.body[0], {\n      type: 'Assertion',\n      kind: 'search_start',\n    })\n  );\n}\n\n// See also `getAndStoreJsGroupName`\nfunction isValidJsGroupName(name) {\n  // JS group names are more restrictive than Onig; see\n  // <developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#identifiers>\n  return /^[$_\\p{IDS}][$\\u200C\\u200D\\p{IDC}]*$/u.test(name);\n}\n\n// Returns a single node, either the given node or all nodes wrapped in a noncapturing group\nfunction parseFragment(pattern, options) {\n  const ast = parse(pattern, {\n    ...options,\n    // Providing a custom set of Unicode property names avoids converting some JS Unicode\n    // properties (ex: `\\p{Alpha}`) to Onig POSIX classes\n    unicodePropertyMap: JsUnicodePropertyMap,\n  });\n  const alts = ast.body;\n  if (alts.length > 1 || alts[0].body.length > 1) {\n    return createGroup({body: alts});\n  }\n  return alts[0].body[0];\n}\n\nfunction setNegate(node, negate) {\n  node.negate = negate;\n  return node;\n}\n\nfunction setParent(node, parent) {\n  node.parent = parent;\n  return node;\n}\n\nfunction setParentDeep(node, parent) {\n  addParentProperties(node);\n  node.parent = parent;\n  return node;\n}\n\nexport {\n  transform,\n};\n", "import {getOptions} from './options.js';\nimport {getIgnoreCaseMatchChars, UnicodePropertiesWithSpecificCase} from './unicode.js';\nimport {cp, envFlags, getNewCurrentFlags, getOrInsert, isMinTarget, r, throwIfNullish} from './utils.js';\nimport {createAlternative, createCharacter, createGroup} from 'oniguruma-parser/parser';\nimport {traverse} from 'oniguruma-parser/traverser';\n/**\n@import {ToRegExpOptions} from './index.js';\n@import {RegexPlusAst} from './transform.js';\n@import {AlternativeNode, AssertionNode, BackreferenceNode, CapturingGroupNode, CharacterClassNode, CharacterClassRangeNode, CharacterNode, CharacterSetNode, FlagsNode, GroupNode, LookaroundAssertionNode, Node, QuantifierNode, SubroutineNode} from 'oniguruma-parser/parser';\n@import {Visitor} from 'oniguruma-parser/traverser';\n*/\n\n/**\nGenerates a Regex+ compatible `pattern`, `flags`, and `options` from a Regex+ AST.\n@param {RegexPlusAst} ast\n@param {ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options: Object;\n  _captureTransfers: Map<number, Array<number>>;\n  _hiddenCaptures: Array<number>;\n}}\n*/\nfunction generate(ast, options) {\n  const opts = getOptions(options);\n  const minTargetEs2024 = isMinTarget(opts.target, 'ES2024');\n  const minTargetEs2025 = isMinTarget(opts.target, 'ES2025');\n  const recursionLimit = opts.rules.recursionLimit;\n  if (!Number.isInteger(recursionLimit) || recursionLimit < 2 || recursionLimit > 20) {\n    throw new Error('Invalid recursionLimit; use 2-20');\n  }\n\n  // If the output can't use flag groups, we need a pre-pass to check for the use of chars with\n  // case in case sensitive/insensitive states. This minimizes the need for case expansions (though\n  // expansions are lossless, even given Unicode case complexities) and allows supporting case\n  // insensitive backrefs in more cases\n  // TODO: Consider gathering this data in the transformer's final traversal to avoid work here\n  let hasCaseInsensitiveNode = null;\n  let hasCaseSensitiveNode = null;\n  if (!minTargetEs2025) {\n    const iStack = [ast.flags.ignoreCase];\n    traverse(ast, FlagModifierVisitor, {\n      getCurrentModI: () => iStack.at(-1),\n      popModI() {iStack.pop()},\n      pushModI(isIOn) {iStack.push(isIOn)},\n      setHasCasedChar() {\n        if (iStack.at(-1)) {\n          hasCaseInsensitiveNode = true;\n        } else {\n          hasCaseSensitiveNode = true;\n        }\n      },\n    });\n  }\n\n  const appliedGlobalFlags = {\n    dotAll: ast.flags.dotAll,\n    // - Turn global flag i on if a case insensitive node was used and no case sensitive nodes were\n    //   used (to avoid unnecessary node expansion).\n    // - Turn global flag i off if a case sensitive node was used (since case sensitivity can't be\n    //   forced without the use of ES2025 flag groups)\n    ignoreCase: !!((ast.flags.ignoreCase || hasCaseInsensitiveNode) && !hasCaseSensitiveNode),\n  };\n  let /** @type {Node} */ lastNode = ast;\n  const state = {\n    accuracy: opts.accuracy,\n    appliedGlobalFlags,\n    captureMap: new Map(),\n    currentFlags: {\n      dotAll: ast.flags.dotAll,\n      ignoreCase: ast.flags.ignoreCase,\n    },\n    inCharClass: false,\n    lastNode,\n    originMap: ast._originMap,\n    recursionLimit,\n    useAppliedIgnoreCase: !!(!minTargetEs2025 && hasCaseInsensitiveNode && hasCaseSensitiveNode),\n    useFlagMods: minTargetEs2025,\n    useFlagV: minTargetEs2024,\n    verbose: opts.verbose,\n  };\n  function gen(/** @type {Node} */ node) {\n    state.lastNode = lastNode;\n    lastNode = node; // For the next iteration\n    const fn = throwIfNullish(generator[node.type], `Unexpected node type \"${node.type}\"`);\n    return fn(node, state, gen);\n  }\n\n  const result = {\n    pattern: ast.body.map(gen).join('|'),\n    // Could reset `lastNode` at this point via `lastNode = ast`, but it isn't needed by flags\n    flags: gen(ast.flags),\n    options: {...ast.options},\n  };\n  if (!minTargetEs2024) {\n    // Switch from flag v to u; Regex+ implicitly chooses by default\n    delete result.options.force.v;\n    result.options.disable.v = true;\n    result.options.unicodeSetsPlugin = null;\n  }\n  result._captureTransfers = new Map();\n  result._hiddenCaptures = [];\n  state.captureMap.forEach((value, key) => {\n    if (value.hidden) {\n      result._hiddenCaptures.push(key);\n    }\n    if (value.transferTo) {\n      getOrInsert(result._captureTransfers, value.transferTo, []).push(key);\n    }\n  });\n\n  return result;\n}\n\nconst /** @type {Visitor} */ FlagModifierVisitor = {\n  '*': {\n    enter({node}, state) {\n      if (isAnyGroup(node)) {\n        const currentModI = state.getCurrentModI();\n        state.pushModI(\n          node.flags ?\n            getNewCurrentFlags({ignoreCase: currentModI}, node.flags).ignoreCase :\n            currentModI\n        );\n      }\n    },\n    exit({node}, state) {\n      if (isAnyGroup(node)) {\n        state.popModI();\n      }\n    },\n  },\n  Backreference(_, state) {\n    // Can't know for sure, so assume the backref will include chars with case (best that could be\n    // done is not calling `setHasCasedChar` if the reffed group doesn't contain a char with case\n    // or most kinds of char sets)\n    state.setHasCasedChar();\n  },\n  Character({node}, state) {\n    if (charHasCase(cp(node.value))) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterClassRange({node, skip}, state) {\n    skip();\n    if (getCasesOutsideCharClassRange(node, {firstOnly: true}).length) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterSet({node}, state) {\n    if (\n      node.kind === 'property' &&\n      UnicodePropertiesWithSpecificCase.has(node.value)\n    ) {\n      state.setHasCasedChar();\n    }\n  },\n};\n\n// `AbsenceFunction`, `Directive`, and `NamedCallout` nodes aren't included in transformer output\nconst generator = {\n  /**\n  @param {AlternativeNode} node\n  */\n  Alternative({body}, _, gen) {\n    return body.map(gen).join('');\n  },\n\n  /**\n  @param {AssertionNode} node\n  */\n  Assertion({kind, negate}) {\n    // Can always use `^` and `$` for string boundaries since JS flag m is never used (Onig uses\n    // different line break chars)\n    if (kind === 'string_end') {\n      return '$';\n    }\n    if (kind === 'string_start') {\n      return '^';\n    }\n    // If a word boundary came through the transformer unaltered, that means `wordIsAscii` or\n    // `asciiWordBoundaries` is enabled\n    if (kind === 'word_boundary') {\n      return negate ? r`\\B` : r`\\b`;\n    }\n    // Kinds `line_end`, `line_start`, `search_start`, `string_end_newline`, and\n    // `text_segment_boundary` are never included in transformer output\n    throw new Error(`Unexpected assertion kind \"${kind}\"`);\n  },\n\n  /**\n  @param {BackreferenceNode} node\n  */\n  Backreference({ref}, state) {\n    if (typeof ref !== 'number') {\n      throw new Error('Unexpected named backref in transformed AST');\n    }\n    if (\n      !state.useFlagMods &&\n      state.accuracy === 'strict' &&\n      state.currentFlags.ignoreCase &&\n      !state.captureMap.get(ref).ignoreCase\n    ) {\n      throw new Error('Use of case-insensitive backref to case-sensitive group requires target ES2025 or non-strict accuracy');\n    }\n    return '\\\\' + ref;\n  },\n\n  /**\n  @param {CapturingGroupNode} node\n  */\n  CapturingGroup(node, state, gen) {\n    const {body, name, number} = node;\n    const data = {ignoreCase: state.currentFlags.ignoreCase};\n    // Has origin if the capture is from an expanded subroutine\n    const origin = state.originMap.get(node);\n    if (origin) {\n      // All captures from/within expanded subroutines are marked as hidden\n      data.hidden = true;\n      // If a subroutine (or descendant capture) occurs after its origin group, it's marked to have\n      // its captured value transferred to the origin's capture slot. `number` and `origin.number`\n      // are the capture numbers *after* subroutine expansion\n      if (number > origin.number) {\n        data.transferTo = origin.number;\n      }\n    }\n    state.captureMap.set(number, data);\n    return `(${name ? `?<${name}>` : ''}${body.map(gen).join('|')})`;\n  },\n\n  /**\n  @param {CharacterNode} node\n  */\n  Character({value}, state) {\n    const char = cp(value);\n    const escaped = getCharEscape(value, {\n      escDigit: state.lastNode.type === 'Backreference',\n      inCharClass: state.inCharClass,\n      useFlagV: state.useFlagV,\n    });\n    if (escaped !== char) {\n      return escaped;\n    }\n    if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase && charHasCase(char)) {\n      const cases = getIgnoreCaseMatchChars(char);\n      return state.inCharClass ?\n        cases.join('') :\n        (cases.length > 1 ? `[${cases.join('')}]` : cases[0]);\n    }\n    return char;\n  },\n\n  /**\n  @param {CharacterClassNode} node\n  */\n  CharacterClass(node, state, gen) {\n    const {kind, negate, parent} = node;\n    let {body} = node;\n    if (kind === 'intersection' && !state.useFlagV) {\n      throw new Error('Use of class intersection requires min target ES2024');\n    }\n    // Work around a WebKit parser bug by moving literal hyphens to the beginning of the class; see\n    // <github.com/slevithan/oniguruma-to-es/issues/30>\n    if (envFlags.bugFlagVLiteralHyphenIsRange && state.useFlagV && body.some(isLiteralHyphen)) {\n      body = [createCharacter(45), ...body.filter(kid => !isLiteralHyphen(kid))];\n    }\n    const genClass = () => `[${negate ? '^' : ''}${\n      body.map(gen).join(kind === 'intersection' ? '&&' : '')\n    }]`;\n    if (!state.inCharClass) {\n      // HACK: Transform the AST to support top-level-nested, negated classes in non-negated\n      // classes (ex: `[…[^…]]`) with pre-ES2024 `target`, via `(?:[…]|[^…])` or `(?:[^…])`,\n      // possibly with multiple alts that contain negated classes. Would be better to do this in\n      // the transformer, but it doesn't have true `target` since that's supposed to be a concern\n      // of the generator\n      if (\n        // Already established `kind !== 'intersection'` if `!state.useFlagV`; don't check again\n        (!state.useFlagV || envFlags.bugNestedClassIgnoresNegation) &&\n        !negate\n      ) {\n        const negatedChildClasses = body.filter(\n          kid => kid.type === 'CharacterClass' && kid.kind === 'union' && kid.negate\n        );\n        if (negatedChildClasses.length) {\n          const group = createGroup();\n          const groupFirstAlt = group.body[0];\n          group.parent = parent;\n          groupFirstAlt.parent = group;\n          body = body.filter(kid => !negatedChildClasses.includes(kid));\n          node.body = body;\n          if (body.length) {\n            node.parent = groupFirstAlt;\n            groupFirstAlt.body.push(node);\n          } else {\n            // Remove the group's only alt thus far, but since the class's `body` is empty, that\n            // implies there's at least one negated class we removed from it, so we'll add at least\n            // one alt back to the group, next\n            group.body.pop();\n          }\n          negatedChildClasses.forEach(cc => {\n            const newAlt = createAlternative({body: [cc]});\n            cc.parent = newAlt;\n            newAlt.parent = group;\n            group.body.push(newAlt);\n          });\n          return gen(group);\n        }\n      }\n      // For the outermost char class, set state\n      state.inCharClass = true;\n      const result = genClass();\n      state.inCharClass = false;\n      return result;\n    }\n    // No first element for implicit class in empty intersection like `[&&]`\n    const firstEl = body[0];\n    if (\n      // Already established that the parent is a char class via `inCharClass`; don't check again\n      kind === 'union' &&\n      !negate &&\n      firstEl &&\n      (\n        ( // Allows many nested classes to work with `target` ES2018 which doesn't support nesting\n          (!state.useFlagV || !state.verbose) &&\n          parent.kind === 'union' &&\n          !(envFlags.bugFlagVLiteralHyphenIsRange && state.useFlagV)\n        ) ||\n        ( !state.verbose &&\n          parent.kind === 'intersection' &&\n          // JS doesn't allow intersection with union or ranges\n          body.length === 1 &&\n          firstEl.type !== 'CharacterClassRange'\n        )\n      )\n    ) {\n      // Remove unnecessary nesting; unwrap kids into the parent char class\n      return body.map(gen).join('');\n    }\n    if (!state.useFlagV && parent.type === 'CharacterClass') {\n      throw new Error('Use of nested character class requires min target ES2024');\n    }\n    return genClass();\n  },\n\n  /**\n  @param {CharacterClassRangeNode} node\n  */\n  CharacterClassRange(node, state) {\n    const min = node.min.value;\n    const max = node.max.value;\n    const escOpts = {\n      escDigit: false,\n      inCharClass: true,\n      useFlagV: state.useFlagV,\n    };\n    const minStr = getCharEscape(min, escOpts);\n    const maxStr = getCharEscape(max, escOpts);\n    const extraChars = new Set();\n    if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase) {\n      // TODO: Avoid duplication by considering other chars in the parent char class when expanding\n      const charsOutsideRange = getCasesOutsideCharClassRange(node);\n      const ranges = getCodePointRangesFromChars(charsOutsideRange);\n      ranges.forEach(value => {\n        extraChars.add(\n          Array.isArray(value) ?\n            `${getCharEscape(value[0], escOpts)}-${getCharEscape(value[1], escOpts)}` :\n            getCharEscape(value, escOpts)\n        );\n      });\n    }\n    // Create the range without calling `gen` on the `min`/`max` kids\n    return `${minStr}-${maxStr}${[...extraChars].join('')}`;\n  },\n\n  /**\n  @param {CharacterSetNode} node\n  */\n  CharacterSet({kind, negate, value, key}, state) {\n    if (kind === 'dot') {\n      return state.currentFlags.dotAll ?\n        ((state.appliedGlobalFlags.dotAll || state.useFlagMods) ? '.' : '[^]') :\n        // Onig's only line break char is line feed, unlike JS\n        r`[^\\n]`;\n    }\n    if (kind === 'digit') {\n      return negate ? r`\\D` : r`\\d`;\n    }\n    if (kind === 'property') {\n      if (\n        state.useAppliedIgnoreCase &&\n        state.currentFlags.ignoreCase &&\n        UnicodePropertiesWithSpecificCase.has(value)\n      ) {\n        // Support for this would require heavy Unicode data. Could change e.g. `\\p{Lu}` to\n        // `\\p{LC}` if not using strict `accuracy` (since it's close but not 100%), but this\n        // wouldn't work for e.g. `\\p{Lt}`, and in any case, it's probably user error if using\n        // these case-specific props case-insensitively\n        throw new Error(`Unicode property \"${value}\" can't be case-insensitive when other chars have specific case`);\n      }\n      return `${negate ? r`\\P` : r`\\p`}{${key ? `${key}=` : ''}${value}}`;\n    }\n    if (kind === 'word') {\n      return negate ? r`\\W` : r`\\w`;\n    }\n    // Kinds `any`, `hex`, `newline`, `posix`, `space`, and `text_segment` are never included in\n    // transformer output\n    throw new Error(`Unexpected character set kind \"${kind}\"`);\n  },\n\n  /**\n  @param {FlagsNode} node\n  */\n  Flags(node, state) {\n    return (\n      // The transformer should never turn on the properties for flags d, g, m since Onig doesn't\n      // have equivs. Flag m is never used since Onig uses different line break chars than JS\n      // (node.hasIndices ? 'd' : '') +\n      // (node.global ? 'g' : '') +\n      // (node.multiline ? 'm' : '') +\n      (state.appliedGlobalFlags.ignoreCase ? 'i' : '') +\n      (node.dotAll ? 's' : '') +\n      (node.sticky ? 'y' : '')\n      // Regex+ doesn't allow explicitly adding flags it handles implicitly, so there are no\n      // `unicode` (flag u) or `unicodeSets` (flag v) props; those flags are added separately\n    );\n  },\n\n  /**\n  @param {GroupNode} node\n  */\n  Group({atomic, body, flags, parent}, state, gen) {\n    const currentFlags = state.currentFlags;\n    if (flags) {\n      state.currentFlags = getNewCurrentFlags(currentFlags, flags);\n    }\n    const contents = body.map(gen).join('|');\n    const result = (\n      !state.verbose &&\n      body.length === 1 && // Single alt\n      parent.type !== 'Quantifier' &&\n      !atomic &&\n      (!state.useFlagMods || !flags)\n     ) ? contents : `(?${getGroupPrefix(atomic, flags, state.useFlagMods)}${contents})`;\n    state.currentFlags = currentFlags;\n    return result;\n  },\n\n  /**\n  @param {LookaroundAssertionNode} node\n  */\n  LookaroundAssertion({body, kind, negate}, _, gen) {\n    const prefix = `${kind === 'lookahead' ? '' : '<'}${negate ? '!' : '='}`;\n    return `(?${prefix}${body.map(gen).join('|')})`;\n  },\n\n  /**\n  @param {QuantifierNode} node\n  */\n  Quantifier(node, _, gen) {\n    return gen(node.body) + getQuantifierStr(node);\n  },\n\n  /**\n  @param {SubroutineNode & {isRecursive: true}} node\n  */\n  Subroutine({isRecursive, ref}, state) {\n    if (!isRecursive) {\n      throw new Error('Unexpected non-recursive subroutine in transformed AST');\n    }\n    const limit = state.recursionLimit;\n    // Using the syntax supported by `regex-recursion`\n    return ref === 0 ? `(?R=${limit})` : r`\\g<${ref}&R=${limit}>`;\n  },\n};\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nconst BaseEscapeChars = new Set([\n  '$', '(', ')', '*', '+', '.', '?', '[', '\\\\', ']', '^', '{', '|', '}',\n]);\n\nconst CharClassEscapeChars = new Set([\n  '-', '\\\\', ']', '^',\n  // Literal `[` doesn't require escaping with flag u, but this can help work around regex source\n  // linters and regex syntax processors that expect unescaped `[` to create a nested class\n  '[',\n]);\n\nconst CharClassEscapeCharsFlagV = new Set([\n  '(', ')', '-', '/', '[', '\\\\', ']', '^', '{', '|', '}',\n  // Double punctuators; also includes already-listed `-` and `^`\n  '!', '#', '$', '%', '&', '*', '+', ',', '.', ':', ';', '<', '=', '>', '?', '@', '`', '~',\n]);\n\nconst CharCodeEscapeMap = new Map([\n  [ 9, r`\\t`], // horizontal tab\n  [10, r`\\n`], // line feed\n  [11, r`\\v`], // vertical tab\n  [12, r`\\f`], // form feed\n  [13, r`\\r`], // carriage return\n  [0x2028, r`\\u2028`], // line separator\n  [0x2029, r`\\u2029`], // paragraph separator\n  [0xFEFF, r`\\uFEFF`], // ZWNBSP/BOM\n]);\n\nconst casedRe = /^\\p{Cased}$/u;\nfunction charHasCase(char) {\n  return casedRe.test(char);\n}\n\n/**\nGiven a `CharacterClassRange` node, returns an array of chars that are a case variant of a char in\nthe range, and aren't already in the range.\n*/\nfunction getCasesOutsideCharClassRange(node, options) {\n  const firstOnly = !!options?.firstOnly;\n  const min = node.min.value;\n  const max = node.max.value;\n  const found = [];\n  // Avoid unneeded work. Assumptions (per Unicode 16):\n  // - No case variants cross the Basic Multilingual Plane boundary\n  // - No cased chars appear beyond the Supplementary Multilingual Plane\n  if ((min < 65 && (max === 0xFFFF || max >= 0x1FFFF)) || (min === 0x10000 && max >= 0x1FFFF)) {\n    return found;\n  }\n  for (let i = min; i <= max; i++) {\n    const char = cp(i);\n    if (!charHasCase(char)) {\n      continue;\n    }\n    const charsOutsideRange = getIgnoreCaseMatchChars(char).filter(caseOfChar => {\n      const num = caseOfChar.codePointAt(0);\n      return num < min || num > max;\n    });\n    if (charsOutsideRange.length) {\n      found.push(...charsOutsideRange);\n      if (firstOnly) {\n        break;\n      }\n    }\n  }\n  return found;\n}\n\n// This shouldn't modifiy any char that has case\nfunction getCharEscape(codePoint, {escDigit, inCharClass, useFlagV}) {\n  if (CharCodeEscapeMap.has(codePoint)) {\n    return CharCodeEscapeMap.get(codePoint);\n  }\n  if (\n    // Control chars, etc.; condition modeled on the Chrome developer console's display for strings\n    codePoint < 32 || (codePoint > 126 && codePoint < 160) ||\n    // Unicode planes 4-16; unassigned, special purpose, and private use area\n    codePoint > 0x3FFFF ||\n    // Avoid corrupting a preceding backref by immediately following it with a literal digit\n    (escDigit && isDigitCharCode(codePoint))\n  ) {\n    // Don't convert codePoint `0` to `\\0` since that's corruptible by following literal digits\n    // Note: Not using raw strings to work around Bun ≤ 1.1.34 issue <github.com/oven-sh/bun/issues/7540>\n    return codePoint > 0xFF ?\n      `\\\\u{${codePoint.toString(16).toUpperCase()}}` :\n      `\\\\x${codePoint.toString(16).toUpperCase().padStart(2, '0')}`;\n  }\n  const escapeChars = inCharClass ?\n    (useFlagV ? CharClassEscapeCharsFlagV : CharClassEscapeChars) :\n    BaseEscapeChars;\n  const char = cp(codePoint);\n  return (escapeChars.has(char) ? '\\\\' : '') + char;\n}\n\nfunction getCodePointRangesFromChars(chars) {\n  const codePoints = chars.map(char => char.codePointAt(0)).sort((a, b) => a - b);\n  const values = [];\n  let start = null;\n  for (let i = 0; i < codePoints.length; i++) {\n    if (codePoints[i + 1] === codePoints[i] + 1) {\n      start ??= codePoints[i];\n    } else if (start === null) {\n      values.push(codePoints[i]);\n    } else {\n      values.push([start, codePoints[i]]);\n      start = null;\n    }\n  }\n  return values;\n}\n\nfunction getGroupPrefix(atomic, flagMods, useFlagMods) {\n  if (atomic) {\n    return '>';\n  }\n  let mods = '';\n  if (flagMods && useFlagMods) {\n    const {enable, disable} = flagMods;\n    mods =\n      (enable?.ignoreCase ? 'i' : '') +\n      (enable?.dotAll ? 's' : '') +\n      (disable ? '-' : '') +\n      (disable?.ignoreCase ? 'i' : '') +\n      (disable?.dotAll ? 's' : '');\n  }\n  return `${mods}:`;\n}\n\n/**\n@param {QuantifierNode} node\n@returns {string}\n*/\nfunction getQuantifierStr({kind, max, min}) {\n  let base;\n  if (!min && max === 1) {\n    base = '?';\n  } else if (!min && max === Infinity) {\n    base = '*';\n  } else if (min === 1 && max === Infinity) {\n    base = '+';\n  } else if (min === max) {\n    base = `{${min}}`;\n  } else {\n    base = `{${min},${max === Infinity ? '' : max}}`;\n  }\n  return base + {\n    greedy: '',\n    lazy: '?',\n    possessive: '+',\n  }[kind];\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAnyGroup({type}) {\n  return type === 'CapturingGroup' ||\n    type === 'Group' ||\n    type === 'LookaroundAssertion';\n}\n\nfunction isDigitCharCode(value) {\n  return value > 47 && value < 58;\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isLiteralHyphen({type, value}) {\n  return type === 'Character' && value === 45;\n}\n\nexport {\n  generate,\n};\n", "import {getOrInsert} from './utils.js';\n\n/**\n@typedef {{\n  hiddenCaptures?: Array<number>;\n  lazyCompile?: boolean;\n  strategy?: string | null;\n  transfers?: Array<[number, Array<number>]>;\n}} EmulatedRegExpOptions\n*/\n\n/**\nWorks the same as JavaScript's native `RegExp` constructor in all contexts, but can be given\nresults from `toRegExpDetails` to produce the same result as `toRegExp`.\n*/\nclass EmulatedRegExp extends RegExp {\n  /**\n  @type {Map<number, {\n    hidden?: true;\n    transferTo?: number;\n  }>}\n  */\n  #captureMap = new Map();\n\n  /**\n  @type {RegExp | EmulatedRegExp | null}\n  */\n  #compiled = null;\n\n  /**\n  @type {string}\n  */\n  #pattern;\n\n  /**\n  @type {Map<number, string>?}\n  */\n  #nameMap = null;\n\n  /**\n  @type {string?}\n  */\n  #strategy = null;\n\n  /**\n  Can be used to serialize the instance.\n  @type {EmulatedRegExpOptions}\n  */\n  rawOptions = {};\n\n  // Override the getter with one that works with lazy-compiled regexes\n  get source() {\n    return this.#pattern || '(?:)';\n  }\n\n  /**\n  @overload\n  @param {string} pattern\n  @param {string} [flags]\n  @param {EmulatedRegExpOptions} [options]\n  */\n  /**\n  @overload\n  @param {EmulatedRegExp} pattern\n  @param {string} [flags]\n  */\n  constructor(pattern, flags, options) {\n    const lazyCompile = !!options?.lazyCompile;\n    if (pattern instanceof RegExp) {\n      // Argument `options` isn't provided when regexes are copied, including as part of the\n      // internal handling of string methods `matchAll` and `split`\n      if (options) {\n        throw new Error('Cannot provide options when copying a regexp');\n      }\n      const re = pattern; // Alias for readability\n      super(re, flags);\n      this.#pattern = re.source;\n      if (re instanceof EmulatedRegExp) {\n        this.#captureMap = re.#captureMap;\n        this.#nameMap = re.#nameMap;\n        this.#strategy = re.#strategy;\n        this.rawOptions = re.rawOptions;\n      }\n    } else {\n      const opts = {\n        hiddenCaptures: [],\n        strategy: null,\n        transfers: [],\n        ...options,\n      };\n      super(lazyCompile ? '' : pattern, flags);\n      this.#pattern = pattern;\n      this.#captureMap = createCaptureMap(opts.hiddenCaptures, opts.transfers);\n      this.#strategy = opts.strategy;\n      // Don't add default values from `opts` since this gets serialized\n      this.rawOptions = options ?? {};\n    }\n    if (!lazyCompile) {\n      this.#compiled = this;\n    }\n  }\n\n  /**\n  Called internally by all String/RegExp methods that use regexes.\n  @override\n  @param {string} str\n  @returns {RegExpExecArray?}\n  */\n  exec(str) {\n    // Lazy compilation\n    if (!this.#compiled) {\n      const {lazyCompile, ...rest} = this.rawOptions;\n      this.#compiled = new EmulatedRegExp(this.#pattern, this.flags, rest);\n    }\n\n    const useLastIndex = this.global || this.sticky;\n    const pos = this.lastIndex;\n\n    if (this.#strategy === 'clip_search' && useLastIndex && pos) {\n      // Reset since this tests on a sliced string that we want to match at the start of\n      this.lastIndex = 0;\n      // Slicing the string can lead to mismatches when three edge cases are stacked on each other:\n      // 1. An uncommon use of `\\G` that relies on `clip_search`, combined with...\n      // 2. Lookbehind that searches behind the search start (not match start) position...\n      // 3. During a search when the regex's `lastIndex` isn't `0`.\n      // The `clip_search` strategy is therefore only allowed when lookbehind isn't present, if\n      // using strict `accuracy`\n      const match = this.#execCore(str.slice(pos));\n      if (match) {\n        adjustMatchDetailsForOffset(match, pos, str, this.hasIndices);\n        this.lastIndex += pos;\n      }\n      return match;\n    }\n\n    return this.#execCore(str);\n  }\n\n  /**\n  Adds support for hidden and transfer captures.\n  @param {string} str\n  @returns\n  */\n  #execCore(str) {\n    // Support lazy compilation\n    this.#compiled.lastIndex = this.lastIndex;\n    const match = super.exec.call(this.#compiled, str);\n    this.lastIndex = this.#compiled.lastIndex;\n\n    if (!match || !this.#captureMap.size) {\n      return match;\n    }\n\n    const matchCopy = [...match];\n    // Empty all but the first value of the array while preserving its other properties\n    match.length = 1;\n    let indicesCopy;\n    if (this.hasIndices) {\n      indicesCopy = [...match.indices];\n      match.indices.length = 1;\n    }\n    const mappedNums = [0];\n    for (let i = 1; i < matchCopy.length; i++) {\n      const {hidden, transferTo} = this.#captureMap.get(i) ?? {};\n      if (hidden) {\n        mappedNums.push(null);\n      } else {\n        mappedNums.push(match.length);\n        match.push(matchCopy[i]);\n        if (this.hasIndices) {\n          match.indices.push(indicesCopy[i]);\n        }\n      }\n\n      // Only transfer if the capture participated in the match\n      if (transferTo && matchCopy[i] !== undefined) {\n        const to = mappedNums[transferTo];\n        if (!to) {\n          throw new Error(`Invalid capture transfer to \"${to}\"`);\n        }\n        match[to] = matchCopy[i];\n        if (this.hasIndices) {\n          match.indices[to] = indicesCopy[i];\n        }\n        if (match.groups) {\n          if (!this.#nameMap) {\n            // Generate and cache the first time it's needed\n            this.#nameMap = createNameMap(this.source);\n          }\n          const name = this.#nameMap.get(transferTo);\n          if (name) {\n            match.groups[name] = matchCopy[i];\n            if (this.hasIndices) {\n              match.indices.groups[name] = indicesCopy[i];\n            }\n          }\n        }\n      }\n    }\n\n    return match;\n  }\n}\n\nfunction adjustMatchDetailsForOffset(match, offset, input, hasIndices) {\n  match.index += offset;\n  match.input = input;\n  if (hasIndices) {\n    const indices = match.indices;\n    for (let i = 0; i < indices.length; i++) {\n      const arr = indices[i];\n      if (arr) {\n        // Replace the array rather than updating values since the keys of `match.indices` and\n        // `match.indices.groups` share their value arrays by reference. Need to be precise in case\n        // they were previously altered separately\n        indices[i] = [arr[0] + offset, arr[1] + offset];\n      }\n    }\n    const groupIndices = indices.groups;\n    if (groupIndices) {\n      Object.keys(groupIndices).forEach(key => {\n        const arr = groupIndices[key];\n        if (arr) {\n          groupIndices[key] = [arr[0] + offset, arr[1] + offset];\n        }\n      });\n    }\n  }\n}\n\n/**\nBuild the capturing group map, with hidden/transfer groups marked to indicate their submatches\nshould get special handling in match results.\n@param {Array<number>} hiddenCaptures\n@param {Array<[number, Array<number>]>} transfers\n@returns {Map<number, {\n  hidden?: true;\n  transferTo?: number;\n}>}\n*/\nfunction createCaptureMap(hiddenCaptures, transfers) {\n  const captureMap = new Map();\n  for (const num of hiddenCaptures) {\n    captureMap.set(num, {\n      hidden: true,\n    });\n  }\n  for (const [to, from] of transfers) {\n    for (const num of from) {\n      getOrInsert(captureMap, num, {}).transferTo = to;\n    }\n  }\n  return captureMap;\n}\n\n/**\n@param {string} pattern\n@returns {Map<number, string>}\n*/\nfunction createNameMap(pattern) {\n  const re = /(?<capture>\\((?:\\?<(?![=!])(?<name>[^>]+)>|(?!\\?)))|\\\\?./gsu;\n  const map = new Map();\n  let numCharClassesOpen = 0;\n  let numCaptures = 0;\n  let match;\n  while ((match = re.exec(pattern))) {\n    const {0: m, groups: {capture, name}} = match;\n    // Relies on no unescaped literal `[` in char classes (valid in JS if not using flag v), but\n    // this library's generator never produces unescaped literal `[` even with `target` ES2018 (see\n    // `CharClassEscapeChars`)\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n      if (capture) {\n        numCaptures++;\n        if (name) {\n          map.set(numCaptures, name);\n        }\n      }\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n  return map;\n}\n\nexport {\n  EmulatedRegExp,\n};\n", "import {transform} from './transform.js';\nimport {generate} from './generate.js';\nimport {Accuracy, getOptions, Target} from './options.js';\nimport {EmulatedRegExp} from './subclass.js';\nimport {JsUnicodePropertyMap} from './unicode.js';\nimport {parse} from 'oniguruma-parser/parser';\nimport {atomic, possessive} from 'regex/internals';\nimport {recursion} from 'regex-recursion';\n/**\n@import {EmulatedRegExpOptions} from './subclass.js';\n*/\n\n// The validation and transformation for Oniguruma's unique syntax and behavior differences\n// compared to native JS RegExp is layered into all steps of the compilation process:\n// 1. Parser: Uses `oniguruma-parser` to build an Oniguruma AST, which accounts for many\n//    differences between Oniguruma and JS.\n// 2. Transformer: Converts the Oniguruma AST to a Regex+ AST that preserves all Oniguruma\n//    behavior. This is true even in cases of non-native-JS features that are supported by both\n//    Regex+ and Oniguruma but with subtly different behavior in each (subroutines, flag x).\n// 3. Generator: Converts the Regex+ AST to a Regex+ pattern, flags, and options.\n// 4. Postprocessing: Regex+ internals and plugins are used to transpile several remaining features\n//    (atomic groups, possessive quantifiers, recursion). Regex+ uses a strict superset of JS\n//    RegExp syntax, so using it allows this library to benefit from not reinventing the wheel for\n//    complex features that Regex+ already knows how to transpile to JS.\n\n/**\n@typedef {{\n  accuracy?: keyof Accuracy;\n  avoidSubclass?: boolean;\n  flags?: string;\n  global?: boolean;\n  hasIndices?: boolean;\n  lazyCompileLength?: number;\n  rules?: {\n    allowOrphanBackrefs?: boolean;\n    asciiWordBoundaries?: boolean;\n    captureGroup?: boolean;\n    recursionLimit?: number;\n    singleline?: boolean;\n  };\n  target?: keyof Target;\n  verbose?: boolean;\n}} ToRegExpOptions\n*/\n\n/**\nAccepts an Oniguruma pattern and returns an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {RegExp | EmulatedRegExp}\n*/\nfunction toRegExp(pattern, options) {\n  const d = toRegExpDetails(pattern, options);\n  if (d.options) {\n    return new EmulatedRegExp(d.pattern, d.flags, d.options);\n  }\n  return new RegExp(d.pattern, d.flags);\n}\n\n/**\nAccepts an Oniguruma pattern and returns the details for an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options?: EmulatedRegExpOptions;\n}}\n*/\nfunction toRegExpDetails(pattern, options) {\n  const opts = getOptions(options);\n  const onigurumaAst = parse(pattern, {\n    flags: opts.flags,\n    normalizeUnknownPropertyNames: true,\n    rules: {\n      captureGroup: opts.rules.captureGroup,\n      singleline: opts.rules.singleline,\n    },\n    skipBackrefValidation: opts.rules.allowOrphanBackrefs,\n    unicodePropertyMap: JsUnicodePropertyMap,\n  });\n  const regexPlusAst = transform(onigurumaAst, {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.rules.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    bestEffortTarget: opts.target,\n  });\n  const generated = generate(regexPlusAst, opts);\n  const recursionResult = recursion(generated.pattern, {\n    captureTransfers: generated._captureTransfers,\n    hiddenCaptures: generated._hiddenCaptures,\n    mode: 'external',\n  });\n  const possessiveResult = possessive(recursionResult.pattern);\n  const atomicResult = atomic(possessiveResult.pattern, {\n    captureTransfers: recursionResult.captureTransfers,\n    hiddenCaptures: recursionResult.hiddenCaptures,\n  });\n  const details = {\n    pattern: atomicResult.pattern,\n    flags: `${opts.hasIndices ? 'd' : ''}${opts.global ? 'g' : ''}${generated.flags}${generated.options.disable.v ? 'u' : 'v'}`,\n  };\n  if (opts.avoidSubclass) {\n    if (opts.lazyCompileLength !== Infinity) {\n      throw new Error('Lazy compilation requires subclass');\n    }\n  } else {\n    // Sort isn't required; only for readability when serialized\n    const hiddenCaptures = atomicResult.hiddenCaptures.sort((a, b) => a - b);\n    // Change the map to the `EmulatedRegExp` format, serializable as JSON\n    const transfers = Array.from(atomicResult.captureTransfers);\n    const strategy = regexPlusAst._strategy;\n    const lazyCompile = details.pattern.length >= opts.lazyCompileLength;\n    if (hiddenCaptures.length || transfers.length || strategy || lazyCompile) {\n      details.options = {\n        ...(hiddenCaptures.length && {hiddenCaptures}),\n        ...(transfers.length && {transfers}),\n        ...(strategy && {strategy}),\n        ...(lazyCompile && {lazyCompile}),\n      };\n    }\n  }\n  return details;\n}\n\n// function toOnigurumaAst(pattern, options) {\n//   return parse(pattern, {\n//     flags: options?.flags ?? '',\n//     normalizeUnknownPropertyNames: true,\n//     rules: options?.rules ?? {},\n//     unicodePropertyMap: JsUnicodePropertyMap,\n//   });\n// }\n\n// function toRegexPlusAst(pattern, options) {\n//   return transform(toOnigurumaAst(pattern, options));\n// }\n\nexport {\n  EmulatedRegExp,\n  toRegExp,\n  toRegExpDetails,\n  // toOnigurumaAst,\n  // toRegexPlusAst,\n};\n"], "names": ["range", "slug", "node", "recursion", "createAlternative", "createCharacter", "createGroup", "traverse", "atomic", "parse"], "mappings": ";;;;;;;;;;;;;;;;;;;AECA,SAAQ,YAAW;;ACEnB,SAAQ,mBAAmB,iBAAiB,qBAAqB,sBAAsB,iBAAiB,sBAAsB,oBAAoB,aAAa,2BAA2B,kBAAkB,kBAAkB,uBAAuB,cAAc,OAAO,QAAAC,aAAW;AACrR,SAAQ,gBAAe;;AGEvB,SAAQ,QAAQ,kBAAiB;AACjC,SAAQ,iBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDQY;;;;;EAAA,gBAOZ;;EAAA,cAKV;;EAAA,aAKZ;;EAAA,aAKW;;EAAA,cAmGX;;;;EAAA;ALtIF,IAAM,KAAK,OAAO,aAAA;AAClB,IAAM,IAAI,OAAO,GAAA;AAEjB,IAAM,WAAW;IACf,YAAA,CAAa,MAAM;QACjB,IAAI;YACF,IAAI,OAAO,OAAO;QACpB,EAAA,UAAQ;YACN,OAAO;QACT;QACA,OAAO;IACT,CAAA,EAAG;IACH,aAAA,CAAc,MAAM;QAClB,IAAI;YACF,IAAI,OAAO,IAAI,GAAG;QACpB,EAAA,UAAQ;YACN,OAAO;QACT;QACA,OAAO;IACT,CAAA,EAAG;AACL;AAEA,SAAS,4BAAA,GAA+B,SAAS,WAAA,GAAA,CAAe,MAAM;IACpE,IAAI;QACF,IAAI,OAAO,sBAAY,GAAG;IAC5B,EAAA,UAAQ;QACN,OAAO;IACT;IACA,OAAO;AACT,CAAA,EAAG,IAAI;AAEP,SAAS,6BAAA,GAAgC,SAAS,WAAA,IAAe,IAAI,OAAO,UAAU,GAAG,EAAE,IAAA,CAAK,GAAG;AAEnG,SAAS,mBAAmB,OAAA,OAAyB;UAAf,MAAA,EAAQ,OAAA,CAAO,CAAA,EAAG,CAAnB;IACnC,OAAO;QACL,QAAQ,oDAAC,QAAS,MAAA,KAAU,CAAC,CAAA,kDAAE,OAAQ,MAAA,KAAU,QAAQ,MAAA;QACzD,YAAY,oDAAC,QAAS,UAAA,KAAc,CAAC,CAAA,qBAAE,oCAAQ,UAAA,KAAc,QAAQ,UAAA;IACvE;AACF;AAEA,SAAS,YAAY,GAAA,EAAK,GAAA,EAAK,YAAA,EAAc;IAC3C,IAAI,CAAC,IAAI,GAAA,CAAI,GAAG,GAAG;QACjB,IAAI,GAAA,CAAI,KAAK,YAAY;IAC3B;IACA,OAAO,IAAI,GAAA,CAAI,GAAG;AACpB;AAOA,SAAS,YAAY,MAAA,EAAQ,GAAA,EAAK;IAChC,OAAO,SAAA,CAAU,MAAM,CAAA,IAAK,SAAA,CAAU,GAAG,CAAA;AAC3C;AAEA,SAAS,eAAe,KAAA,EAAO,GAAA,EAAK;IAClC,IAAI,SAAS,MAAM;QACjB,MAAM,IAAI,uCAAM,MAAO,gBAAgB;IACzC;IACA,OAAO;AACT;;ACrDA,IAAM,YAAY;IAChB,QAAQ;IACR,QAAQ;IACR,QAAQ;AACV;AAEA,IAAM,SAAA,kBAAA,GAA+B;IACnC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;AACV;AAOA,SAAS;kBAAW,iEAAU,CAAC,GAAG;IAChC,KAAI,EAAC,EAAE,QAAA,CAAS,IAAA,CAAK,OAAO,MAAM,mBAAmB;QACnD,MAAM,IAAI,MAAM,oBAAoB;IACtC;IACA,IAAI,QAAQ,MAAA,KAAW,KAAA,KAAa,CAAC,MAAA,CAAO,QAAQ,MAAM,CAAA,EAAG;QAC3D,MAAM,IAAI,MAAM,sBAAoC,OAAd,QAAQ,MAAM,EAAA,EAAG;IACzD;IAEA,MAAM,OAAO;QAAA,gDAAA;QAEX,UAAU;QAAA,yFAAA;QAAA,yCAAA;QAGV,eAAe;QAAA,wFAAA;QAAA,2EAAA;QAGf,OAAO;QAAA,wDAAA;QAEP,QAAQ;QAAA,4DAAA;QAER,YAAY;QAAA,8FAAA;QAEZ,mBAAmB;QAAA,8FAAA;QAAA,yFAAA;QAAA,mCAAA;QAInB,QAAQ;QAAA,iFAAA;QAER,SAAS;QACT,GAAG,OAAA;QAAA,4FAAA;QAEH,OAAO;YAAA,2EAAA;YAEL,qBAAqB;YAAA,oFAAA;YAErB,qBAAqB;YAAA,wFAAA;YAAA,wFAAA;YAAA,sBAAA;YAIrB,cAAc;YAAA,iFAAA;YAEd,gBAAgB;YAAA,6FAAA;YAAA,4FAAA;YAGhB,YAAY;YACZ,GAAG,QAAQ,KAAA;QACb;IACF;IACA,IAAI,KAAK,MAAA,KAAW,QAAQ;QAC1B,KAAK,MAAA,GAAS,SAAS,UAAA,GAAa,WAAY,SAAS,WAAA,GAAc,WAAW;IACpF;IACA,OAAO;AACT;;AC5EA,IAAM,iBAAiB;AAEvB,IAAM,kCAAkC,aAAA,GAAA,IAAI,IAAI;IAC9C,GAAG,GAAK;IAAA,IAAA;IACR,GAAG,GAAK;CACT;AAGD,IAAM,kBAAkB;AAExB,SAAS,wBAAwB,IAAA,EAAM;IAErC,IAAI,gCAAgC,GAAA,CAAI,IAAI,GAAG;QAC7C,OAAO;YAAC,IAAI;SAAA;IACd;IACA,MAAM,MAAM,aAAA,GAAA,IAAI,IAAI;IACpB,MAAM,QAAQ,KAAK,WAAA,CAAY;IAE/B,MAAM,QAAQ,MAAM,WAAA,CAAY;IAChC,MAAM,QAAQ,oBAAoB,GAAA,CAAI,KAAK;IAC3C,MAAM,WAAW,+BAA+B,GAAA,CAAI,KAAK;IACzD,MAAM,WAAW,+BAA+B,GAAA,CAAI,KAAK;IAIzD,IAAI,CAAC;WAAG,KAAK;KAAA,CAAE,MAAA,KAAW,GAAG;QAC3B,IAAI,GAAA,CAAI,KAAK;IACf;IACA,YAAY,IAAI,GAAA,CAAI,QAAQ;IAC5B,SAAS,IAAI,GAAA,CAAI,KAAK;IAEtB,IAAI,GAAA,CAAI,KAAK;IACb,YAAY,IAAI,GAAA,CAAI,QAAQ;IAC5B,OAAO,CAAC;WAAG,GAAG;KAAA;AAChB;AAeA,IAAM,uBAAuC,aAAA,GAAA,IAAI,IACjD,gwDA2FE,KAAA,CAAM,IAAI,EACV,GAAA,CAAI,CAAA,IAAK;QAAC,6LAAA,EAAK,CAAC;QAAG,CAAC;KAAC;AAGvB,IAAM,iCAAiC,aAAA,GAAA,IAAI,IAAI;IAC7C;QAAC;QAAK,GAAG,GAAK,CAAC;KAAA;IAAA,OAAA;IACf;QAAC,GAAG,GAAK;QAAG,GAAG;KAAA;CAChB;AAED,IAAM,iCAAiC,aAAA,GAAA,IAAI,IAAI;IAC7C;QAAC,GAAG,GAAI;QAAG,GAAG,IAAM,CAAC;KAAA;IAAA,OAAA;IACrB;QAAC,GAAG,GAAI;QAAG,GAAG,IAAM,CAAC;KAAA;IAAA,gBAAA;IACrB;QAAC,GAAG,GAAI;QAAG,GAAG,IAAM,CAAC;KAAA;IAAA,kBAAA;IACrB;QAAC,GAAG,GAAK;QAAG,GAAG,IAAM,CAAC;KAAA;CACvB;AAGD,IAAM,sBAAsB,IAAI,IAAI;IAClC,WAAW,GAAK;IAChB,WAAW,GAAK;IAChB,WAAW,GAAK;IAChB,WAAW,GAAK;OACb,WAAW,MAAQ,IAAM;OACzB,WAAW,MAAQ,IAAM;OACzB,WAAW,MAAQ,IAAM;IAC5B,WAAW,IAAM;IACjB,WAAW,IAAM;IACjB,WAAW,IAAM;CAClB;AAQD,IAAM,gBAAgB,aAAA,GAAA,IAAI,IAAI;IAC5B;QAAC;QAAS,oBAAoB;KAAA;IAC9B;QAAC;QAAS,YAAY;KAAA;IACtB;QAAC;QAAS,YAAY;KAAA;IACtB;QAAC;QAAS,aAAa;KAAA;IACvB;QAAC;QAAS,SAAS;KAAA;IACnB;QAAC;QAAS,SAAS;KAAA;IACnB;QAAC;QAAS,sCAAsC;KAAA;IAChD;QAAC;QAAS,YAAY;KAAA;IACtB;QAAC;QAAS,8CAA8C;KAAA;IACxD;QAAC;QAAS,eAAe;KAAA;IAAA,kEAAA;IACzB;QAAC;QAAS,YAAY;KAAA;IACtB;QAAC;QAAS,YAAY;KAAA;IACtB;QAAC;QAAQ,+BAA+B;KAAA;IACxC;QAAC;QAAU,WAAW;KAAA;CACvB;AAED,SAAS,MAAM,KAAA,EAAO,GAAA,EAAK;IAGzB,MAAMD,SAAQ,CAAC,CAAA;IACf,IAAA,IAAS,IAAI,OAAO,KAAK,KAAK,IAAK;QACjCA,OAAM,IAAA,CAAK,CAAC;IACd;IACA,OAAOA;AACT;AAEA,SAAS,WAAW,SAAA,EAAW;IAC7B,MAAM,OAAO,GAAG,SAAS;IACzB,OAAO;QAAC,KAAK,WAAA,CAAY;QAAG,IAAI;KAAA;AAClC;AAEA,SAAS,WAAW,KAAA,EAAO,GAAA,EAAK;IAC9B,OAAO,MAAM,OAAO,GAAG,EAAE,GAAA,CAAI,CAAA,YAAa,WAAW,SAAS,CAAC;AACjE;AAEA,IAAM,oCAAoC,aAAA,GAAA,IAAI,IAAI;IAChD;IAAS;IACT;IAAS;IACT;IAAM;IACN;IAAM;IACN;IAAM;CAKP;;;AC3LD,SAAS,UAAU,GAAA,EAAK,OAAA,EAAS;IAC/B,MAAM,OAAO;QAAA,sFAAA;QAAA,2FAAA;QAAA,2EAAA;QAAA,uFAAA;QAAA,8FAAA;QAAA,6EAAA;QAOX,UAAU;QACV,qBAAqB;QACrB,eAAe;QACf,kBAAkB;QAClB,GAAG,OAAA;IACL;IAEA,oBAAoB,GAAG;IACvB,MAAM,iBAAiB;QACrB,UAAU,KAAK,QAAA;QACf,qBAAqB,KAAK,mBAAA;QAC1B,eAAe,KAAK,aAAA;QACpB,qBAAqB,aAAA,GAAA,IAAI,IAAI;QAC7B,gBAAgB,aAAA,GAAA,IAAI,IAAI;QACxB,iBAAiB,YAAY,KAAK,gBAAA,EAAkB,QAAQ;QAC5D,kBAAkB;QAClB,UAAU;QAAA,gGAAA;QAEV,kBAAkB,aAAA,GAAA,IAAI,IAAI;QAC1B,iBAAiB,aAAA,GAAA,IAAI,IAAI;QACzB,cAAc,IAAI,KAAA,CAAM,YAAA;QACxB,cAAc,IAAI,KAAA,CAAM,YAAA;QACxB,aAAa,IAAI,KAAA,CAAM,WAAA;IACzB;IACA,IAAA,mLAAA,EAAS,KAAK,kBAAkB,cAAc;IAE9C,MAAM,cAAc;QAClB,QAAQ,IAAI,KAAA,CAAM,MAAA;QAClB,YAAY,IAAI,KAAA,CAAM,UAAA;IACxB;IAMA,MAAM,kBAAkB;QACtB,cAAc;QACd,WAAW;QACX;QACA,mBAAmB,aAAA,GAAA,IAAI,IAAI;QAC3B,cAAc,aAAA,GAAA,IAAI,IAAI;QACtB,8BAA8B,aAAA,GAAA,IAAI,IAAI;QACtC,UAAU,aAAA,GAAA,IAAI,IAAI;QAClB,yBAAyB,aAAA,GAAA,IAAI,IAAI;QACjC,kBAAkB,eAAe,gBAAA;IACnC;IACA,IAAA,mLAAA,EAAS,KAAK,mBAAmB,eAAe;IAChD,MAAM,iBAAiB;QACrB,cAAc,gBAAgB,YAAA;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,yBAAyB,gBAAgB,uBAAA;IAC3C;IACA,IAAA,mLAAA,EAAS,KAAK,kBAAkB,cAAc;IAC9C,IAAI,UAAA,GAAa,gBAAgB,iBAAA;IACjC,IAAI,SAAA,GAAY,eAAe,QAAA;IAC/B,OAAO;AACT;AAEA,IAA6B,mBAAmB;IAC9C,iBAAgB,KAA0B,EAAG;cAA5B,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA;QACxC,MAAM,EAAC,IAAA,EAAM,IAAA,CAAI,CAAA,GAAI;QACrB,IAAI,SAAS,YAAY;YAEvB,MAAM,iBAAa,gMAAA,CAAY;YAC/B,WAAW,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,IAAA,CAAA,4BAAA;YAEtB,kNAAA,EAA0B;gBAAC,QAAQ;gBAAM;YAAI,CAAC,OAC9C,0MAAA,EAAsB,KAAK;YAE7B,MAAM,iBAAa,gMAAA,CAAY;YAC/B,WAAW,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,IAAA,CACtB,yMAAA,EAAiB,UAAU,GAAG,UAAU,UAAU;YAEpD,YAAY,cAAc,YAAY,MAAM,GAAG;gBAAC,UAAU;YAAI,CAAC;QACjE,OAAO;YACL,MAAM,IAAI,MAAM,oCAAqC;QACvD;IACF;IAEA,aAAa;QACX,OAAM,KAAkB,QAAuB,EAAG;kBAA3C,IAAA,EAAM,MAAA,EAAQ,GAAA,CAAG,CAAA,YAAI,mBAAA,CAAmB,CAAA,GAApB;YAGzB,MAAM,iBAAiB,KAAK,IAAA,CAAK,MAAA,CAAO,CAAA,KAAM,GAAG,IAAA,KAAS,OAAO;YACjE,IAAA,IAAS,IAAI,MAAM,GAAG,IAAI,OAAO,IAAA,CAAK,MAAA,EAAQ,IAAK;gBACjD,MAAM,oBAAoB,OAAO,IAAA,CAAK,CAAC,CAAA;gBACvC,YAAY,qBAAqB,mBAAmB,CAAC,CAAC,EAAE,IAAA,CAAK,GAAG,cAAc;YAChF;QACF;QACA,MAAK,KAAK,EAAG,MAAoB,EAAG;kBAA9B,IAAA,CAAI,CAAA,YAAI,mBAAA,CAAmB,CAAA;;YAI/B,oDAAwB,GAAA,CAAI,IAAI,8DAA5B,yBAA+B,MAAA,EAAQ;gBACzC,MAAM,QAAQ,iCAAiC,oBAAoB,GAAA,CAAI,IAAI,CAAC;gBAC5E,IAAI,OAAO;oBACT,MAAM,YAAY,oMAAA,EAAY;wBAAC;oBAAK,CAAC;oBACrC,UAAU,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,GAAO,KAAK,IAAA;oBAC9B,KAAK,IAAA,GAAO;wBAAC,cAAc,WAAW,IAAI,CAAC;qBAAA;gBAC7C;YACF;QACF;IACF;IAEA,gBAAkE,EAAG,KAAA,EAAO;cAAjE,IAAA,EAAM,MAAA,EAAQ,GAAA,EAAK,SAAA,EAAW,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA,GAAxD;QACR,MAAM,EAAC,IAAA,EAAM,MAAA,CAAM,CAAA,GAAI;QACvB,MAAM,EAAC,mBAAA,EAAqB,aAAA,EAAe,eAAA,EAAiB,WAAA,CAAW,CAAA,GAAI;QAC3E,IAAI,SAAS,yBAAyB;YAEpC,MAAM,IAAI,MAAM,wCAA0D,OAAlB,SAAS,MAAM,GAAG,EAAA,EAAG;QAC/E,OAAA,IAAW,SAAS,YAAY;YAC9B,YAAY,kBAAc,8MAAA,EAA0B;gBAAC,MAAM;wBACzD,sMAAA,EAAkB;wBAAC,MAAM;gCAAC,oMAAA,EAAgB,YAAY,CAAC;yBAAA;oBAAC,CAAC;wBACzD,sMAAA,EAAkB;wBAAC,MAAM;gCAAC,oMAAA,EAAgB,EAAE,CAAC;yBAAA;oBAAC,CAAC;iBACjD;YAAC,CAAC,GAAG,MAAM,CAAC;QACd,OAAA,IAAW,SAAS,cAAc;YAEhC,YAAY,cAAc,cAAc,wBAAqB;gBAAC,0BAA0B;YAAI,CAAC,GAAG,MAAM,CAAC;QACzG,OAAA,IAAW,SAAS,gBAAgB;YAClC,IAAI,gBAAgB,GAAA,CAAI,IAAI,GAAG;gBAC7B,KAAK,KAAA,CAAM,MAAA,GAAS;gBACpB,OAAO;YACT,OAAO;gBACL,MAAM,OAAO,SAAA,CAAU,MAAM,CAAC,CAAA;gBAI9B,IAAI,QAAQ,sBAAsB,IAAI,GAAG;oBACvC,YAAY,kBAAc,8MAAA,EAA0B;wBAAC,QAAQ;oBAAI,CAAC,GAAG,MAAM,CAAC;gBAC9E,OAAA,IAAW,eAAe;oBACxB,MAAM,IAAI,MAAM,8CAA8C;gBAChE,OAAO;oBACL,YAAY,cAAU,oMAAA,EAAgB,cAAc,GAAG,MAAM,CAAC;oBAC9D,MAAM,QAAA,GAAW;gBACnB;YACF;QACF,OAAA,IAAW,SAAS,gBAAgB,SAAS,gBAAgB,CAE7D,OAAA,IAAW,SAAS,sBAAsB;YACxC,YAAY,cAAc,cAAc,YAAY,aAAG,MAAM,CAAC;QAChE,OAAA,IAAW,SAAS,iBAAiB;YACnC,IAAI,CAAC,eAAe,CAAC,qBAAqB;gBACxC,MAAM,IAAI,iBAAU,eAAe,EAAA,QAA+B,OAAxB,QAAuC,OAAxB,EAAA,kCAAwB,QAAsB,OAAf,eAAe,EAAA;gBACvG,MAAM,IAAI,iBAAU,eAAe,EAAA,eAAO,eAAe,EAAA,UAA+B,OAAtB,QAAqC,OAAtB,EAAA,QAAsB,wBAAA;gBACvG,YAAY,cAAc,cAAc,SAAS,IAAI,CAAC,GAAG,MAAM,CAAC;YAClE;QACF,OAAO;YACL,MAAM,IAAI,MAAM,8BAAkC,OAAJ,IAAI,EAAA,EAAG;QACvD;IACF;IAEA,oBAAmB,QAAkB;cAAtB,IAAA,CAAI,CAAA,GAAL,SAAS,cAAA,CAAc,CAAA,EAAG,CAAlB;QACpB,IAAI,EAAC,GAAA,CAAG,CAAA,GAAI;QACZ,IAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;YACvD,MAAM,uBAAuB,KAAK,cAAc;YAChD,KAAK,GAAA,GAAM;QACb;IACF;IAEA,qBAAoB,EAAG,MAAiC,EAAG;YAA5C,EAAC,IAAA,CAAI,CAAA,YAAI,cAAA,EAAgB,gBAAA,CAAgB,CAAA;QACtD,IAAI,EAAC,IAAA,CAAI,CAAA,GAAI;QACb,IAAI,QAAQ,CAAC,mBAAmB,IAAI,GAAG;YACrC,OAAO,uBAAuB,MAAM,cAAc;YAClD,KAAK,IAAA,GAAO;QACd;QACA,iBAAiB,GAAA,CAAI,KAAK,MAAA,EAAQ,IAAI;QACtC,IAAI,MAAM;YACR,iBAAiB,GAAA,CAAI,MAAM,IAAI;QACjC;IACF;IAEA,0BAA8C;cAAzB,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA,EAAG,CAA7B;QAClB,IAAI,OAAO,IAAA,KAAS,gBAAgB;YAElC,MAAM,SAAK,yMAAA,EAAqB;gBAAC,MAAM;oBAAC,IAAI;iBAAA;YAAC,CAAC;YAC9C,YAAY,cAAc,IAAI,MAAM,GAAG;gBAAC,UAAU;YAAI,CAAC;QACzD;IACF;IAEA,mBAAuC,EAAG,MAAmE,EAAG;YAAnG,EAAC,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA,YAAI,QAAA,EAAU,eAAA,EAAiB,YAAA,EAAc,YAAA,EAAc,WAAA,CAAW,CAAA;QAC3G,MAAM,EAAC,IAAA,EAAM,MAAA,EAAQ,KAAA,CAAK,CAAA,GAAI;QAE9B,IAAI,gBAAA,CAAiB,SAAS,WAAW,UAAU,OAAA,GAAU;YAC3D,YAAY,cAAU,uMAAA,EAAmB,SAAS;gBAAC;YAAM,CAAC,GAAG,MAAM,CAAC;YACpE;QACF;QAEA,IAAI,gBAAA,CAAiB,SAAS,WAAW,UAAU,OAAA,GAAU;YAC3D,YAAY,cAAc,UAAU,cAAc,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;YACnF;QACF;QAEA,IAAI,eAAA,CAAgB,SAAS,UAAU,UAAU,MAAA,GAAS;YACxD,YAAY,cAAU,uMAAA,EAAmB,QAAQ;gBAAC;YAAM,CAAC,GAAG,MAAM,CAAC;YACnE;QACF;QACA,IAAI,SAAS,OAAO;YAClB,YAAY,cAAU,0MAAA,EAAsB,KAAK,GAAG,MAAM,CAAC;QAC7D,OAAA,IAAW,SAAS,SAAS;YAC3B,YAAY,cAAU,0MAAA,EAAsB,MAAM;gBAAC;YAAM,CAAC,GAAG,MAAM,CAAC;QACtE,OAAA,IAAW,SAAS,OAAO,CAE3B,OAAA,IAAW,SAAS,gBAAgB;YAClC,IAAI,aAAa,UAAU;gBACzB,MAAM,IAAI,MAAM,2CAA2C;YAC7D;YAGA,MAAM,QAAQ;YACd,MAAM,QAAQ,uBAAc,KAAK,EAAY,KAAK;YAClD,YAAY,cAAc,cAAA,wFAAA;YAExB,uBAAY,kBAAkB,yBAAmB,KAAK,GAAA,wCAAA;YAEtD;gBAAC,4BAA4B;YAAI,IAChC,MAAM,CAAC;QACZ,OAAA,IAAW,SAAS,OAAO;YACzB,YAAY,cAAU,0MAAA,EAAsB,QAAQ;gBAAC;YAAM,CAAC,GAAG,MAAM,CAAC;QACxE,OAAA,IAAW,SAAS,WAAW;YAC7B,YAAY,cAAc,cAAc,SAAS,UAAU,oCAAoC,GAAG,MAAM,CAAC;QAC3G,OAAA,IAAW,SAAS,SAAS;YAC3B,IAAI,CAAC,mBAAA,CAAoB,UAAU,WAAW,UAAU,OAAA,GAAU;gBAChE,IAAI,aAAa,UAAU;oBACzB,MAAM,IAAI,MAAM,gBAAqB,OAAL,KAAK,EAAA,oDAAqD;gBAC5F;gBACA,IAAI,QAAQ;oBACV,OAAO;oBACP,OAAO;gBACT,CAAA,CAAE,KAAK,CAAA;gBACP,IAAI,QAAQ;oBAGV,QAAQ,MAAqC,OAA/B,GAAG,MAAM,WAAA,CAAY,CAAC,IAAI,CAAC,CAAC,EAA+B,UAAzB,MAAM,WAAA,CAAY,CAAC,IAAI,CAAC,CAAC,EAAA;gBAC3E;gBACA,YAAY,cAAc,cAAc,IAAS,OAAL,KAAK,EAAA,EAAG,KAAG,MAAM,CAAC;YAChE,OAAO;gBACL,YAAY,cAAc,UAAU,cAAc,cAAc,GAAA,CAAI,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC;YAC/F;QACF,OAAA,IAAW,SAAS,YAAY;YAC9B,IAAI,CAAC,qBAAqB,GAAA,KAAIC,yLAAAA,EAAK,KAAK,CAAC,GAAG;gBAG1C,KAAK,GAAA,GAAM;YACb;QACF,OAAA,IAAW,SAAS,SAAS;YAE3B,YAAY,cAAU,0MAAA,EAAsB,SAAS;gBAAC;YAAM,CAAC,GAAG,MAAM,CAAC;QACzE,OAAA,IAAW,SAAS,QAAQ;YAC1B,YAAY,cAAc,UAAU,cAAc,eAAe,GAAG,MAAM,GAAG,MAAM,CAAC;QACtF,OAAO;YACL,MAAM,IAAI,MAAM,kCAAsC,EAAG,KAAP,IAAI,EAAA;QACxD;IACF;IAEA,WAAU,KAAsF;cAArF,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,MAAA,EAAQ,WAAA,EAAa,qBAAA,EAAuB,qBAAA,CAAqB,CAAA,EAAG;QACjG,MAAM,EAAC,IAAA,EAAM,KAAA,CAAK,CAAA,GAAI;QACtB,IAAI,SAAS,SAAS;YACpB,IAAI,CAAC,MAAM,MAAA,IAAU,CAAC,MAAM,OAAA,EAAS;gBAEnC,OAAO;YACT,OAAO;gBACL,MAAM,gBAAY,gMAAA,EAAY;oBAAC;gBAAK,CAAC;gBACrC,UAAU,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,GAAO,sBAAsB;gBAC/C,YAAY,cAAc,WAAW,MAAM,GAAG;oBAAC,UAAU;gBAAI,CAAC;YAChE;QACF,OAAA,IAAW,SAAS,QAAQ;YAC1B,MAAM,WAAW,KAAK,IAAA,CAAK,CAAC,CAAA;YAE5B,MAAM,kBACJ,KAAK,IAAA,CAAK,MAAA,KAAW,KAAA,8CAAA;gBAErB,yLAAA,EAAa,UAAU;gBAAC,MAAM;YAAO,CAAC,KACtC,SAAS,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,MAAA,KAAW;YACnC,MAAM,WAAW,kBAAkB,SAAS,IAAA,CAAK,CAAC,CAAA,GAAI;YACtD,IAAI,OAAO,MAAA,KAAW,YAAY,SAAS,IAAA,CAAK,MAAA,GAAS,GAAG;gBAC1D,MAAM,IAAI,MAAM,wCAAwC;YAC1D;YACA,MAAM,iBAAa,8MAAA,EAA0B;gBAAC,QAAQ;YAAI,CAAC;YAC3D,WAAW,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,GAAO,sBAAsB;YAChD,YAAY,cAAc,YAAY,MAAM,CAAC;QAC/C,OAAO;YACL,MAAM,IAAI,MAAM,8BAAkC,OAAJ,IAAI,EAAA,EAAG;QACvD;IACF;IAEA,YAAmB;cAAZ,IAAA,EAAM,MAAA,CAAM,CAAA,EAAG,CAAhB;QAEJ,IAAI,KAAK,YAAA,EAAc;YACrB,MAAM,IAAI,MAAM,sBAAsB;QACxC;QACA,IAAI,KAAK,eAAA,KAAoB,QAAQ;YACnC,MAAM,IAAI,MAAM,yBAAyB;QAC3C;QAEA;YAAE;YAAA,SAAA;YACA;YAAA,SAAA;YACA;YAAA,SAAA;YACA;YAAA,SAAA;YACA;YAAA,SAAA;YACA;SACF,CAAE,OAAA,CAAQ,CAAA,IAAK,OAAO,IAAA,CAAK,CAAC,CAAC;YAWnB;QAVV,OAAO,MAAA,CAAO,MAAM;YAAA,2BAAA;YAElB,QAAQ;YAAA,2BAAA;YAER,YAAY;YAAA,yFAAA;YAAA,0FAAA;YAAA,yFAAA;YAIZ,WAAW;YAAA,wDAAA;YAEX,6BAAa,MAAA,uDAAU;QAIzB,CAAC;QAED,OAAO,OAAA,GAAU;YACf,SAAS;gBAAA,iFAAA;gBAEP,GAAG;gBAAA,qFAAA;gBAAA,kFAAA;gBAGH,GAAG;YACL;YACA,OAAO;gBAAA,qFAAA;gBAAA,qFAAA;gBAAA,2EAAA;gBAIL,GAAG;YACL;QACF;IACF;IAEA,YAAW;cAAJ,IAAA,CAAI,CAAA,EAAG,CAAR;QACJ,IAAI,CAAC,KAAK,KAAA,EAAO;YACf;QACF;QACA,MAAM,EAAC,MAAA,EAAQ,OAAA,CAAO,CAAA,GAAI,KAAK,KAAA;QAE/B,CAAA,mBAAA,6BAAA,OAAQ,QAAA,KAAY,OAAO,OAAO,QAAA;QAClC,CAAA,oBAAA,8BAAA,QAAS,QAAA,KAAY,OAAO,QAAQ,QAAA;QAEpC,CAAA,mBAAA,6BAAA,OAAQ,MAAA,MAAU,0DAAS,MAAA,KAAU,OAAO,OAAO,MAAA;QACnD,CAAA,mBAAA,6BAAA,OAAQ,UAAA,wDAAc,QAAS,UAAA,KAAc,OAAO,OAAO,UAAA;QAE3D,UAAU,CAAC,OAAO,IAAA,CAAK,MAAM,EAAE,MAAA,IAAU,OAAO,KAAK,KAAA,CAAM,MAAA;QAC3D,WAAW,CAAC,OAAO,IAAA,CAAK,OAAO,EAAE,MAAA,IAAU,OAAO,KAAK,KAAA,CAAM,OAAA;QAC7D,CAAC,KAAK,KAAA,CAAM,MAAA,IAAU,CAAC,KAAK,KAAA,CAAM,OAAA,IAAW,OAAO,KAAK,KAAA;IAC3D;IAEA,0BAAyB,EAAG,KAAA,EAAO;cAAd,IAAA,CAAI,CAAA,GAAL;QAClB,MAAM,EAAC,IAAA,CAAI,CAAA,GAAI;QACf,IAAI,SAAS,cAAc;YACzB,MAAM,gBAAA,GAAmB;QAC3B;IACF;IAEA,mBAAuC;cAAzB,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA,EAAG,CAA7B;QACX,MAAM,EAAC,IAAA,CAAI,CAAA,GAAI;QACf,IAAI,SAAS,QAAQ;YACnB,YAAY,cAAc,kNAAA,EAA0B;gBAAC,QAAQ;YAAI,CAAC,GAAG,MAAM,CAAC;QAC9E,OAAO;YACL,MAAM,IAAI,MAAM,gCAAkD,OAAlB,KAAK,WAAA,CAAY,CAAC,EAAA,EAAG;QACvE;IACF;IAEA,YAAW,KAAK,EAAG;cAAP,IAAA,CAAI,CAAA;QACd,IAAI,KAAK,IAAA,CAAK,IAAA,KAAS,cAAc;YAEnC,MAAM,YAAQ,gMAAA,CAAY;YAC1B,MAAM,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,IAAA,CAAK,KAAK,IAAI;YACjC,KAAK,IAAA,GAAO,cAAc,OAAO,IAAI;QACvC;IACF;IAEA,OAAO;QACL,YAAW,EAAG,MAAgB,EAAG;kBAA1B,IAAA,CAAI,CAAA,GAAL,SAAS,eAAA,CAAe,CAAA;YAI5B,MAAM,YAAY,CAAC,CAAA;YACnB,IAAI,kBAAkB;YACtB,IAAI,qBAAqB;YACzB,KAAA,MAAW,OAAO,KAAK,IAAA,CAAM;gBAC3B,IAAI,IAAI,IAAA,CAAK,MAAA,KAAW,KAAK,IAAI,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,KAAS,gBAAgB;oBAMhE,IAAI,IAAA,CAAK,GAAA,CAAI;gBACf,OAAO;oBACL,MAAM,WAAW,YAAY,IAAI,IAAI;oBACrC,IAAI,UAAU;wBACZ,kBAAkB;wBAClB,MAAM,OAAA,CAAQ,QAAQ,IACpB,UAAU,IAAA,CAAK,GAAG,QAAQ,IAC1B,UAAU,IAAA,CAAK,QAAQ;oBAC3B,OAAO;wBACL,qBAAqB;oBACvB;gBACF;YACF;YACA,IAAI,mBAAmB,CAAC,oBAAoB;gBAE1C,UAAU,OAAA,CAAQ,CAAA,IAAK,gBAAgB,GAAA,CAAI,CAAC,CAAC;YAC/C;QACF;QACA,MAAK,CAAA,EAAG,KAAqC,EAAG;kBAAvC,QAAA,EAAU,gBAAA,EAAkB,QAAA,CAAQ,CAAA;YAC3C,IAAI,aAAa,YAAY,oBAAoB,UAAU;gBACzD,MAAM,IAAI,MAAM,uDAAuD;YACzE;QACF;IACF;IAEA,iBAAgB,QAAkB;cAAtB,IAAA,CAAI,CAAA,GAAL,SAAS,cAAA,CAAc,CAAA,EAAG,CAAlB;QACjB,IAAI,EAAC,GAAA,CAAG,CAAA,GAAI;QACZ,IAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;YACvD,MAAM,uBAAuB,KAAK,cAAc;YAChD,KAAK,GAAA,GAAM;QACb;IACF;AACF;AAEA,IAA6B,oBAAoB;IAC/C,oBAAmB,EAAG,MAAsD,EAAG;YAAjE,EAAC,IAAA,CAAI,CAAA,YAAI,4BAAA,EAA8B,uBAAA,CAAuB,CAAA;QAC1E,MAAM,EAAC,MAAA,EAAQ,GAAA,CAAG,CAAA,GAAI;QACtB,IAAI,CAAC,QAAQ;YAGX,wBAAwB,GAAA,CAAI,MAAM,CAAC;mBAAG,6BAA6B,GAAA,CAAI,GAAG,EAAE,GAAA,CAAI;wBAAC,EAAC,MAAAC,KAAAA,CAAI,CAAA;2BAAMA,KAAI,CAAC;;aAAC;QACpG;IACF;IAEA,gBAAgB;QACd,YAKE,EACA,MAKA,EACA;kBAXE,IAAA,EACA,MAAA,EACA,WAAA,EACA,IAAA,EACF,GAJA,SAKE,iBAAA,EACA,YAAA,EACA,4BAAA,EACA,QAAA,EACA,uBAAA,EACF;YAGA,MAAM,SAAS,kBAAkB,GAAA,CAAI,IAAI;YAGzC,IAAI,UAAU,SAAS,GAAA,CAAI,KAAK,MAAM,GAAG;gBAIvC,MAAMC,aAAY,UAAU,gBAAgB,KAAK,MAAM,GAAG,MAAM;gBAChE,wBAAwB,GAAA,CAAIA,YAAW,SAAS,GAAA,CAAI,KAAK,MAAM,CAAC;gBAChE,YAAYA,UAAS;gBACrB;YACF;YACA,SAAS,GAAA,CAAI,KAAK,MAAA,EAAQ,IAAI;YAG9B,6BAA6B,GAAA,CAAI,KAAK,MAAA,EAAQ,CAAC,CAAC;YAChD,IAAI,KAAK,IAAA,EAAM;gBACb,YAAY,8BAA8B,KAAK,IAAA,EAAM,CAAC,CAAC;YACzD;;YACA,MAAM,iBAAiB,6BAA6B,GAAA,eAAI,KAAK,IAAA,mDAAQ,KAAK,MAAM;YAChF,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAK;gBAO9C,MAAM,YAAY,cAAA,CAAe,CAAC,CAAA;gBAClC,IAAA,yFAAA;gBAAA,yEAAA;gBAGG,WAAW,UAAU,IAAA,IAAS,UAAU,WAAW,UAAU,MAAA,IAAA,yFAAA;gBAAA,kCAAA;gBAG9D,SAAS,UAAU,MAAA,EACnB;oBACA,eAAe,MAAA,CAAO,GAAG,CAAC;oBAC1B;gBACF;YACF;YACA,6BAA6B,GAAA,CAAI,KAAK,MAAM,EAAE,IAAA,CAAK;gBAAC;gBAAM;YAAM,CAAC;YACjE,IAAI,KAAK,IAAA,EAAM;gBACb,6BAA6B,GAAA,CAAI,KAAK,IAAI,EAAE,IAAA,CAAK;oBAAC;oBAAM;gBAAM,CAAC;YACjE;YAQA,IAAI,KAAK,IAAA,EAAM;gBACb,MAAM,qBAAqB,YAAY,cAAc,KAAK,IAAA,EAAM,aAAA,GAAA,IAAI,IAAI,CAAC;gBACzE,IAAI,2BAA2B;gBAC/B,IAAI,QAAQ;oBAEV,2BAA2B;gBAC7B,OAAO;oBACL,KAAA,MAAW,aAAa,mBAAmB,MAAA,CAAO,EAAG;wBACnD,IAAI,CAAC,UAAU,wBAAA,EAA0B;4BAEvC,2BAA2B;4BAC3B;wBACF;oBACF;gBACF;gBACA,aAAa,GAAA,CAAI,KAAK,IAAI,EAAE,GAAA,CAAI,MAAM;oBAAC;oBAAM;gBAAwB,CAAC;YACxE;QACF;QACA,WAAU,QAAY,EAAG;kBAAnB,IAAA,CAAI,CAAA,GAAL,OAAQ,EAAC,QAAA,CAAQ,CAAA;YACpB,SAAS,MAAA,CAAO,KAAK,MAAM;QAC7B;IACF;IAEA,OAAO;QACL,YAAW,EAAG,KAAA,EAAO;kBAAd,IAAA,CAAI,CAAA,GAAL;YAEJ,MAAM,SAAA,GAAY,MAAM,YAAA;YACxB,IAAI,KAAK,KAAA,EAAO;gBACd,MAAM,YAAA,GAAe,mBAAmB,MAAM,YAAA,EAAc,KAAK,KAAK;YACxE;QACF;QACA,MAAK,CAAA,EAAG,KAAA,EAAO;YACb,MAAM,YAAA,GAAe,MAAM,SAAA;QAC7B;IACF;IAEA,iBAAqC,EAAG,KAAA,EAAO;cAAnC,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA,GAA1B;QACT,MAAM,EAAC,WAAA,EAAa,GAAA,CAAG,CAAA,GAAI;QAK3B,IAAI,aAAa;YAEf,IAAI,SAAS;YACb,MAAQ,SAAS,OAAO,MAAA,CAAS;gBAC/B,IAAI,OAAO,IAAA,KAAS,oBAAA,CAAqB,OAAO,IAAA,KAAS,OAAO,OAAO,MAAA,KAAW,GAAA,GAAM;oBACtF;gBACF;YACF;YAGA,MAAM,uBAAA,CAAwB,GAAA,CAAI,MAAM,MAAM;YAC9C;QACF;QAEA,MAAM,kBAAkB,MAAM,gBAAA,CAAiB,GAAA,CAAI,GAAG;QAEtD,MAAM,oBAAoB,QAAQ;QAClC,MAAM,qBAAqB,oBACzB,gBAAgB,CAAC,IAAA,6FAAA;QAEjB,oBAAoB,iBAAiB,MAAM,iBAAA,EAAmB,IAAI;QACpE,IAAI,cAAc;QAClB,IAAI,CAAC,mBAAmB;YAEtB,MAAM,sBAAsB,iCAAiC,cAC3D,iBACA,CAAA,IAAK,EAAE,IAAA,KAAS,WAAW,CAAC,CAAC,EAAE,KAAA;YAEjC,MAAM,mBAAmB,sBACvB,mBAAmB,MAAM,WAAA,EAAa,mBAAmB,IACzD,MAAM,WAAA;YACR,IAAI,CAAC,cAAc,kBAAkB,MAAM,YAAY,GAAG;gBACxD,kBAAc,gMAAA,EAAY;oBACxB,OAAO,qBAAqB,gBAAgB;gBAC9C,CAAC;gBACD,YAAY,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,IAAA,CAAK,kBAAkB;YAClD;QACF;QACA,YAAY,cAAc,aAAa,MAAM,GAAG;YAAC,UAAU,CAAC;QAAiB,CAAC;IAChF;AACF;AAEA,IAA6B,mBAAmB;IAC9C,oBAAwC,EAAG,KAAA,EAAO;YAApC,EAAC,IAAA,EAAM,MAAA,EAAQ,WAAA,CAAW,CAAA;QACtC,IAAI,KAAK,MAAA,EAAQ;YACf,MAAM,oBAAA,GAAuB,KAAK,GAAA,CAAI,MAAM,oBAAA,EAAsB,KAAK,GAAG;YAE1E;QACF;QACA,MAAM,cAAc,MAAM,uBAAA,CAAwB,GAAA,CAAI,IAAI;QAC1D,MAAM,eAAe,YAAY,MAAA,CAAO,CAAA,SAAU,uBAAuB,QAAQ,IAAI,CAAC;QAKtF,IAAI,CAAC,aAAa,MAAA,EAAQ;YAGxB,YAAY,kBAAc,8MAAA,EAA0B;gBAAC,QAAQ;YAAI,CAAC,GAAG,MAAM,CAAC;QAC9E,OAAA,IAAW,aAAa,MAAA,GAAS,GAAG;YAElC,MAAM,YAAQ,gMAAA,EAAY;gBACxB,QAAQ;gBACR,MAAM,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,CAAA,aAAU,sMAAA,EAAkB;wBAC3D,MAAM;gCAAC,wMAAA,EAAoB,OAAO,MAAM,CAAC;yBAAA;oBAC3C,CAAC,CAAC;YACJ,CAAC;YACD,YAAY,cAAc,OAAO,MAAM,CAAC;QAC1C,OAAO;YACL,KAAK,GAAA,GAAM,YAAA,CAAa,CAAC,CAAA,CAAE,MAAA;QAC7B;IACF;IAEA,gBAAe,KAAK,EAAG,KAAA,EAAO;cAAd,IAAA,CAAI,CAAA;QAElB,KAAK,MAAA,GAAS,EAAE,MAAM,iBAAA;QACtB,IAAI,KAAK,IAAA,EAAM;YAGb,IAAI,MAAM,YAAA,CAAa,GAAA,CAAI,KAAK,IAAI,EAAE,GAAA,CAAI,IAAI,EAAE,wBAAA,EAA0B;gBACxE,OAAO,KAAK,IAAA;YACd;QACF;IACF;IAEA,OAAO;QACL,MAAK,KAAK,EAAG,KAAA,EAAO;kBAAd,IAAA,CAAI,CAAA;YAUR,MAAM,gBAAgB,KAAK,GAAA,CAAI,MAAM,oBAAA,GAAuB,MAAM,iBAAA,EAAmB,CAAC;YACtF,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,MAAM,mBAAe,yMAAA,CAAqB;gBAC1C,KAAK,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE,EAAE,IAAA,CAAK,IAAA,CAAK,YAAY;YACzC;QACF;IACF;IAEA,iBAAgB,EAAG,KAAA,EAAO;cAAd,IAAA,CAAI,CAAA,GAAL;QACT,IAAI,CAAC,KAAK,WAAA,IAAe,KAAK,GAAA,KAAQ,GAAG;YACvC;QACF;QAMA,KAAK,GAAA,GAAM,MAAM,uBAAA,CAAwB,GAAA,CAAI,IAAI,EAAE,MAAA;IACrD;AACF;AAMA,SAAS,oBAAoB,IAAA,EAAM;IACjC,IAAA,mLAAA,EAAS,MAAM;QACb,KAAI,KAAa,EAAG;kBAAf,IAAA,EAAM,MAAA,CAAM,CAAA;YACf,KAAK,MAAA,GAAS;QAChB;IACF,CAAC;AACH;AAEA,SAAS,cAAc,CAAA,EAAG,CAAA,EAAG;IAC3B,OAAO,EAAE,MAAA,KAAW,EAAE,MAAA,IAAU,EAAE,UAAA,KAAe,EAAE,UAAA;AACrD;AAEA,SAAS,uBAAuB,OAAA,EAAS,IAAA,EAAM;IAG7C,IAAI,iBAAiB;IACrB,GAAG;QACD,IAAI,eAAe,IAAA,KAAS,SAAS;YAEnC,OAAO;QACT;QACA,IAAI,eAAe,IAAA,KAAS,eAAe;YAEzC;QACF;QACA,IAAI,mBAAmB,SAAS;YAE9B,OAAO;QACT;QACA,MAAM,eAAe,QAAQ,eAAe,MAAM;QAClD,KAAA,MAAW,OAAO,aAAc;YAC9B,IAAI,QAAQ,gBAAgB;gBAE1B;YACF;YACA,IAAI,QAAQ,WAAW,aAAa,KAAK,OAAO,GAAG;gBACjD,OAAO;YACT;QACF;IACF,QAAU,iBAAiB,eAAe,MAAA,CAAA;IAC1C,MAAM,IAAI,MAAM,iBAAiB;AACnC;AAKA,SAAS,oBAAoB,GAAA,EAAK,SAAA,EAAW,EAAA,EAAI,GAAA,EAAK;IACpD,MAAM,QAAQ,MAAM,OAAA,CAAQ,GAAG,IAAI,CAAC,CAAA,GAAI,CAAC;IACzC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,GAAG,EAAG;QAC9C,IAAI,QAAQ,UAAU;YAEpB,MAAM,MAAA,GAAS,MAAM,OAAA,CAAQ,EAAE,IAAI,MAAM;QAC3C,OAAA,IAAW,SAAS,OAAO,UAAU,UAAU;YAC7C,KAAA,CAAM,GAAG,CAAA,GAAI,oBAAoB,OAAO,WAAW,OAAO,EAAE;QAC9D,OAAO;YACL,IAAI,QAAQ,UAAU,UAAU,kBAAkB;;gBAEhD,UAAU,GAAA,CAAI,yBAAO,UAAU,GAAA,CAAI,GAAG,4DAAK,GAAG;YAChD;YACA,KAAA,CAAM,GAAG,CAAA,GAAI;QACf;IACF;IACA,OAAO;AACT;AAEA,SAAS,gBAAgB,GAAA,EAAK;IAC5B,MAAM,OAAO,yMAAA,EAAiB,GAAG;IAIjC,KAAK,WAAA,GAAc;IACnB,OAAO;AACT;AAEA,SAAS,cAAc,IAAA,EAAM,QAAA,EAAU;IACrC,MAAM,UAAU,CAAC,CAAA;IACjB,MAAQ,OAAO,KAAK,MAAA,CAAS;QAC3B,IAAI,CAAC,YAAY,SAAS,IAAI,GAAG;YAC/B,QAAQ,IAAA,CAAK,IAAI;QACnB;IACF;IACA,OAAO;AACT;AAGA,SAAS,uBAAuB,IAAA,EAAM,GAAA,EAAK;IACzC,IAAI,IAAI,GAAA,CAAI,IAAI,GAAG;QACjB,OAAO,IAAI,GAAA,CAAI,IAAI;IACrB;IAEA,MAAM,SAAS,WAAI,IAAI,IAAI,EAAA,KAAgE,OAA5D,KAAK,OAAA,CAAQ,2DAA2C,GAAG,CAAC;IAC3F,IAAI,GAAA,CAAI,MAAM,MAAM;IACpB,OAAO;AACT;AAEA,SAAS,iCAAiC,SAAA,EAAW;IACnD,MAAM,YAAY;QAAC;QAAU,YAAY;KAAA;IACzC,MAAM,gBAAgB;QAAC,QAAQ,CAAC;QAAG,SAAS,CAAC;IAAC;IAC9C,UAAU,OAAA,CAAQ;YAAC,EAAC,KAAA,CAAK,CAAA,KAAM;QAC7B,UAAU,OAAA,CAAQ,CAAA,SAAQ;+BAMpB;YALJ,2BAAU,MAAA,kDAAN,aAAM,CAAS,IAAI,CAAA,EAAG;gBAExB,OAAO,cAAc,OAAA,CAAQ,IAAI,CAAA;gBACjC,cAAc,MAAA,CAAO,IAAI,CAAA,GAAI;YAC/B;YACA,4BAAU,OAAA,iEAAA,CAAU,IAAI,CAAA,EAAG;gBACzB,cAAc,OAAA,CAAQ,IAAI,CAAA,GAAI;YAChC;QACF,CAAC;IACH,CAAC;IACD,IAAI,CAAC,OAAO,IAAA,CAAK,cAAc,MAAM,EAAE,MAAA,EAAQ;QAC7C,OAAO,cAAc,MAAA;IACvB;IACA,IAAI,CAAC,OAAO,IAAA,CAAK,cAAc,OAAO,EAAE,MAAA,EAAQ;QAC9C,OAAO,cAAc,OAAA;IACvB;IACA,IAAI,cAAc,MAAA,IAAU,cAAc,OAAA,EAAS;QACjD,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,0BAAwC,EAAG;UAArB,MAAA,EAAQ,UAAA,CAAU,CAAA,GAAnB;IAC5B,MAAM,OAAO,CAAC;IACd,IAAI,UAAU,YAAY;QACxB,KAAK,MAAA,GAAS,CAAC;QACf,UAAA,CAAW,KAAK,MAAA,CAAO,MAAA,GAAS,IAAA;QAChC,cAAA,CAAe,KAAK,MAAA,CAAO,UAAA,GAAa,IAAA;IAC1C;IACA,IAAI,CAAC,UAAU,CAAC,YAAY;QAC1B,KAAK,OAAA,GAAU,CAAC;QAChB,CAAC,UAAA,CAAW,KAAK,OAAA,CAAQ,MAAA,GAAS,IAAA;QAClC,CAAC,cAAA,CAAe,KAAK,OAAA,CAAQ,UAAA,GAAa,IAAA;IAC5C;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,IAAA,EAAM;IACrB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,eAAe;IACjC;IAGA,MAAM,EAAC,IAAA,CAAI,CAAA,GAAI;IACf,OAAO,MAAM,OAAA,CAAQ,IAAI,IAAI,OAAQ,OAAO;QAAC,IAAI;KAAA,GAAI;AACvD;AAEA,SAAS,YAAY,GAAA,EAAK;IACxB,MAAM,kBAAkB,IAAI,IAAA,CAAK,CAAA,KAC/B,GAAG,IAAA,KAAS,kBACZ,kBAAkB,IAAI;YAAC,QAAQ;QAAK,CAAC,KACrC,CAAC,mBAAmB,EAAE,CACvB;IACD,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,IAAI,gBAAgB,IAAA,KAAS,gBAAgB;QAC3C,OAAO;IACT;IACA,IAAI,gBAAgB,IAAA,KAAS,uBAAuB;QAClD,OAAO,gBAAgB,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,CAAC,CAAA;IACvC;IACA,IAAI,gBAAgB,IAAA,KAAS,oBAAoB,gBAAgB,IAAA,KAAS,SAAS;QACjF,MAAM,iBAAiB,CAAC,CAAA;QAExB,KAAA,MAAW,OAAO,gBAAgB,IAAA,CAAM;YACtC,MAAM,WAAW,YAAY,IAAI,IAAI;YACrC,IAAI,CAAC,UAAU;gBAEb,OAAO;YACT;YACA,MAAM,OAAA,CAAQ,QAAQ,IACpB,eAAe,IAAA,CAAK,GAAG,QAAQ,IAC/B,eAAe,IAAA,CAAK,QAAQ;QAChC;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,aAAa,IAAA,EAAM,UAAA,EAAY;;IACtC,MAAM,2BAAe,IAAI,qCAAZ,WAAiB,CAAC,CAAA;IAC/B,KAAA,MAAW,OAAO,KAAM;QACtB,IAAI,QAAQ,cAAc,aAAa,KAAK,UAAU,GAAG;YACvD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAMA,SAAS,wBAAwB,EAAG;UAAP,IAAA,CAAI,CAAA,GAAL;IAC1B,OACE,SAAS,eACT,SAAS,eACT,SAAS;AAEb;AAMA,SAAS,sBAAsB,IAAA,EAAM;IACnC,MAAM,QAAQ;QACZ;QACA;QACA;KACF;IACA,OAAO,MAAM,QAAA,CAAS,KAAK,IAAI,KAC7B,KAAK,IAAA,KAAS,gBACd,KAAK,GAAA,IACL,MAAM,QAAA,CAAS,KAAK,IAAA,CAAK,IAAI;AAEjC;AAEA,SAAS,kBAAkB,IAAA,EAAM,OAAA,EAAS;IACxC,MAAM,OAAO;QACX,QAAQ;QACR,GAAG,OAAA;IACL;IACA,OACE,KAAK,IAAA,KAAS,yBAAA,CACb,KAAK,MAAA,KAAW,QAAQ,KAAK,MAAA,KAAW,KAAK,MAAA,KAC9C,KAAK,IAAA,CAAK,MAAA,KAAW,SACrB,yLAAA,EAAa,KAAK,IAAA,CAAK,CAAC,CAAA,EAAG;QACzB,MAAM;QACN,MAAM;IACR,CAAC;AAEL;AAGA,SAAS,mBAAmB,IAAA,EAAM;IAGhC,OAAO,wDAAwC,IAAA,CAAK,IAAI;AAC1D;AAGA,SAAS,cAAc,OAAA,EAAS,OAAA,EAAS;IACvC,MAAM,UAAM,0LAAA,EAAM,SAAS;QACzB,GAAG,OAAA;QAAA,qFAAA;QAAA,qDAAA;QAGH,oBAAoB;IACtB,CAAC;IACD,MAAM,OAAO,IAAI,IAAA;IACjB,IAAI,KAAK,MAAA,GAAS,KAAK,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,MAAA,GAAS,GAAG;QAC9C,WAAO,gMAAA,EAAY;YAAC,MAAM;QAAI,CAAC;IACjC;IACA,OAAO,IAAA,CAAK,CAAC,CAAA,CAAE,IAAA,CAAK,CAAC,CAAA;AACvB;AAEA,SAAS,UAAU,IAAA,EAAM,MAAA,EAAQ;IAC/B,KAAK,MAAA,GAAS;IACd,OAAO;AACT;AAEA,SAAS,UAAU,IAAA,EAAM,MAAA,EAAQ;IAC/B,KAAK,MAAA,GAAS;IACd,OAAO;AACT;AAEA,SAAS,cAAc,IAAA,EAAM,MAAA,EAAQ;IACnC,oBAAoB,IAAI;IACxB,KAAK,MAAA,GAAS;IACd,OAAO;AACT;;;ACh8BA,SAAS,SAAS,GAAA,EAAK,OAAA,EAAS;IAC9B,MAAM,OAAO,WAAW,OAAO;IAC/B,MAAM,kBAAkB,YAAY,KAAK,MAAA,EAAQ,QAAQ;IACzD,MAAM,kBAAkB,YAAY,KAAK,MAAA,EAAQ,QAAQ;IACzD,MAAM,iBAAiB,KAAK,KAAA,CAAM,cAAA;IAClC,IAAI,CAAC,OAAO,SAAA,CAAU,cAAc,KAAK,iBAAiB,KAAK,iBAAiB,IAAI;QAClF,MAAM,IAAI,MAAM,kCAAkC;IACpD;IAOA,IAAI,yBAAyB;IAC7B,IAAI,uBAAuB;IAC3B,IAAI,CAAC,iBAAiB;QACpB,MAAM,SAAS;YAAC,IAAI,KAAA,CAAM,UAAU;SAAA;YACpCI,mLAAAA,EAAS,KAAK,qBAAqB;YACjC,gBAAgB,IAAM,OAAO,EAAA,CAAG,CAAA,CAAE;YAClC,UAAU;gBAAC,OAAO,GAAA,CAAI;YAAC;YACvB,UAAS,KAAA,EAAO;gBAAC,OAAO,IAAA,CAAK,KAAK;YAAC;YACnC,kBAAkB;gBAChB,IAAI,OAAO,EAAA,CAAG,CAAA,CAAE,GAAG;oBACjB,yBAAyB;gBAC3B,OAAO;oBACL,uBAAuB;gBACzB;YACF;QACF,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,QAAQ,IAAI,KAAA,CAAM,MAAA;QAAA,+FAAA;QAAA,gDAAA;QAAA,8FAAA;QAAA,kDAAA;QAKlB,YAAY,CAAC,CAAA,CAAA,CAAG,IAAI,KAAA,CAAM,UAAA,IAAc,sBAAA,KAA2B,CAAC,oBAAA;IACtE;IACA,IAAwB,WAAW;IACnC,MAAM,QAAQ;QACZ,UAAU,KAAK,QAAA;QACf;QACA,YAAY,aAAA,GAAA,IAAI,IAAI;QACpB,cAAc;YACZ,QAAQ,IAAI,KAAA,CAAM,MAAA;YAClB,YAAY,IAAI,KAAA,CAAM,UAAA;QACxB;QACA,aAAa;QACb;QACA,WAAW,IAAI,UAAA;QACf;QACA,sBAAsB,CAAC,CAAA,CAAE,CAAC,mBAAmB,0BAA0B,oBAAA;QACvE,aAAa;QACb,UAAU;QACV,SAAS,KAAK,OAAA;IAChB;IACA,SAAS,IAAwB,IAAA,EAAM;QACrC,MAAM,QAAA,GAAW;QACjB,WAAW;QACX,MAAM,KAAK,eAAe,SAAA,CAAU,KAAK,IAAI,CAAA,EAAG,yBAAkC,EAAG,KAAZ,KAAK,IAAI,EAAA;QAClF,OAAO,GAAG,MAAM,OAAO,GAAG;IAC5B;IAEA,MAAM,SAAS;QACb,SAAS,IAAI,IAAA,CAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,GAAG;QAAA,0FAAA;QAEnC,OAAO,IAAI,IAAI,KAAK;QACpB,SAAS;YAAC,GAAG,IAAI,OAAA;QAAO;IAC1B;IACA,IAAI,CAAC,iBAAiB;QAEpB,OAAO,OAAO,OAAA,CAAQ,KAAA,CAAM,CAAA;QAC5B,OAAO,OAAA,CAAQ,OAAA,CAAQ,CAAA,GAAI;QAC3B,OAAO,OAAA,CAAQ,iBAAA,GAAoB;IACrC;IACA,OAAO,iBAAA,GAAoB,aAAA,GAAA,IAAI,IAAI;IACnC,OAAO,eAAA,GAAkB,CAAC,CAAA;IAC1B,MAAM,UAAA,CAAW,OAAA,CAAQ,CAAC,OAAO,QAAQ;QACvC,IAAI,MAAM,MAAA,EAAQ;YAChB,OAAO,eAAA,CAAgB,IAAA,CAAK,GAAG;QACjC;QACA,IAAI,MAAM,UAAA,EAAY;YACpB,YAAY,OAAO,iBAAA,EAAmB,MAAM,UAAA,EAAY,CAAC,CAAC,EAAE,IAAA,CAAK,GAAG;QACtE;IACF,CAAC;IAED,OAAO;AACT;AAEA,IAA6B,sBAAsB;IACjD,KAAK;QACH,YAAW,EAAG,KAAA,EAAO;kBAAd,IAAA,CAAI,CAAA,GAAL;YACJ,IAAI,WAAW,IAAI,GAAG;gBACpB,MAAM,cAAc,MAAM,cAAA,CAAe;gBACzC,MAAM,QAAA,CACJ,KAAK,KAAA,GACH,mBAAmB;oBAAC,YAAY;gBAAW,GAAG,KAAK,KAAK,EAAE,UAAA,GAC1D;YAEN;QACF;QACA,WAAU,EAAG,KAAA,EAAO;kBAAd,IAAA,CAAI,CAAA,GAAL;YACH,IAAI,WAAW,IAAI,GAAG;gBACpB,MAAM,OAAA,CAAQ;YAChB;QACF;IACF;IACA,eAAc,CAAA,EAAG,KAAA,EAAO;QAItB,MAAM,eAAA,CAAgB;IACxB;IACA,gBAAe,EAAG,KAAA,EAAO;cAAd,IAAA,CAAI,CAAA,GAAL;QACR,IAAI,YAAY,GAAG,KAAK,KAAK,CAAC,GAAG;YAC/B,MAAM,eAAA,CAAgB;QACxB;IACF;IACA,0BAA+B,EAAG,KAAA,EAAO;YAArB,EAAC,IAAA,EAAM,IAAA,CAAI,CAAA;QAC7B,KAAK;QACL,IAAI,8BAA8B,MAAM;YAAC,WAAW;QAAI,CAAC,EAAE,MAAA,EAAQ;YACjE,MAAM,eAAA,CAAgB;QACxB;IACF;IACA,mBAAkB,EAAG,KAAA,EAAO;cAAd,IAAA,CAAI,CAAA,GAAL;QACX,IACE,KAAK,IAAA,KAAS,cACd,kCAAkC,GAAA,CAAI,KAAK,KAAK,GAChD;YACA,MAAM,eAAA,CAAgB;QACxB;IACF;AACF;AAGA,IAAM,YAAY;IAAA;;EAAA,GAIhB,kBAAiB,EAAG,CAAA,EAAG,GAAA,EAAK;cAAf,IAAA,CAAI,CAAA,GAAL;QACV,OAAO,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,EAAE;IAC9B;IAAA;;EAAA,GAKA,gBAAuB;cAAZ,IAAA,EAAM,MAAA,CAAM,CAAA,EAAG,CAAhB;QAGR,IAAI,SAAS,cAAc;YACzB,OAAO;QACT;QACA,IAAI,SAAS,gBAAgB;YAC3B,OAAO;QACT;QAGA,IAAI,SAAS,iBAAiB;YAC5B,OAAO,SAAS,yBAAQ;QAC1B;QAGA,MAAM,IAAI,MAAM,8BAAkC,OAAJ,IAAI,EAAA,EAAG;IACvD;IAAA;;EAAA,GAKA,oBAAkB,EAAG,KAAA,EAAO;cAAb,GAAA,CAAG,CAAA,GAAJ;QACZ,IAAI,OAAO,QAAQ,UAAU;YAC3B,MAAM,IAAI,MAAM,6CAA6C;QAC/D;QACA,IACE,CAAC,MAAM,WAAA,IACP,MAAM,QAAA,KAAa,YACnB,MAAM,YAAA,CAAa,UAAA,IACnB,CAAC,MAAM,UAAA,CAAW,GAAA,CAAI,GAAG,EAAE,UAAA,EAC3B;YACA,MAAM,IAAI,MAAM,uGAAuG;QACzH;QACA,OAAO,OAAO;IAChB;IAAA;;EAAA,GAKA,gBAAe,IAAA,EAAM,KAAA,EAAO,GAAA,EAAK;QAC/B,MAAM,EAAC,IAAA,EAAM,IAAA,EAAM,MAAA,CAAM,CAAA,GAAI;QAC7B,MAAM,OAAO;YAAC,YAAY,MAAM,YAAA,CAAa,UAAA;QAAU;QAEvD,MAAM,SAAS,MAAM,SAAA,CAAU,GAAA,CAAI,IAAI;QACvC,IAAI,QAAQ;YAEV,KAAK,MAAA,GAAS;YAId,IAAI,SAAS,OAAO,MAAA,EAAQ;gBAC1B,KAAK,UAAA,GAAa,OAAO,MAAA;YAC3B;QACF;QACA,MAAM,UAAA,CAAW,GAAA,CAAI,QAAQ,IAAI;QACjC,OAAO,WAAI,OAAO,KAAS,OAAJ,IAAI,EAAA,OAAM,EAAE,EAA0B,OAAvB,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;IAC/D;IAAA;;EAAA,GAKA,gBAAgB,EAAG,KAAA,EAAO;cAAf,KAAA,CAAK,CAAA,GAAN;QACR,MAAM,OAAO,GAAG,KAAK;QACrB,MAAM,UAAU,cAAc,OAAO;YACnC,UAAU,MAAM,QAAA,CAAS,IAAA,KAAS;YAClC,aAAa,MAAM,WAAA;YACnB,UAAU,MAAM,QAAA;QAClB,CAAC;QACD,IAAI,YAAY,MAAM;YACpB,OAAO;QACT;QACA,IAAI,MAAM,oBAAA,IAAwB,MAAM,YAAA,CAAa,UAAA,IAAc,YAAY,IAAI,GAAG;YACpF,MAAM,QAAQ,wBAAwB,IAAI;YAC1C,OAAO,MAAM,WAAA,GACX,MAAM,IAAA,CAAK,EAAE,IACZ,MAAM,MAAA,GAAS,IAAI,IAAkB,OAAd,MAAM,IAAA,CAAK,EAAE,CAAC,EAAA,OAAM,KAAA,CAAM,CAAC,CAAA;QACvD;QACA,OAAO;IACT;IAAA;;EAAA,GAKA,gBAAe,IAAA,EAAM,KAAA,EAAO,GAAA,EAAK;QAC/B,MAAM,EAAC,IAAA,EAAM,MAAA,EAAQ,MAAA,CAAM,CAAA,GAAI;QAC/B,IAAI,EAAC,IAAA,CAAI,CAAA,GAAI;QACb,IAAI,SAAS,kBAAkB,CAAC,MAAM,QAAA,EAAU;YAC9C,MAAM,IAAI,MAAM,sDAAsD;QACxE;QAGA,IAAI,SAAS,4BAAA,IAAgC,MAAM,QAAA,IAAY,KAAK,IAAA,CAAK,eAAe,GAAG;YACzF,OAAO;oBAACF,oMAAAA,EAAgB,EAAE,GAAG;mBAAG,KAAK,MAAA,CAAO,CAAA,MAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;aAAA;QAC3E;QACA,MAAM,WAAW,IAAM,WAAI,SAAS,MAAM,EAAE,EAE5C,OADE,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,SAAS,iBAAiB,OAAO,EAAE,CACxD,EAAA;QACA,IAAI,CAAC,MAAM,WAAA,EAAa;YAMtB,IAAA,wFAAA;YAAA,CAEG,CAAC,MAAM,QAAA,IAAY,SAAS,6BAAA,KAC7B,CAAC,QACD;gBACA,MAAM,sBAAsB,KAAK,MAAA,CAC/B,CAAA,MAAO,IAAI,IAAA,KAAS,oBAAoB,IAAI,IAAA,KAAS,WAAW,IAAI,MAAA;gBAEtE,IAAI,oBAAoB,MAAA,EAAQ;oBAC9B,MAAM,YAAQC,gMAAAA,CAAY;oBAC1B,MAAM,gBAAgB,MAAM,IAAA,CAAK,CAAC,CAAA;oBAClC,MAAM,MAAA,GAAS;oBACf,cAAc,MAAA,GAAS;oBACvB,OAAO,KAAK,MAAA,CAAO,CAAA,MAAO,CAAC,oBAAoB,QAAA,CAAS,GAAG,CAAC;oBAC5D,KAAK,IAAA,GAAO;oBACZ,IAAI,KAAK,MAAA,EAAQ;wBACf,KAAK,MAAA,GAAS;wBACd,cAAc,IAAA,CAAK,IAAA,CAAK,IAAI;oBAC9B,OAAO;wBAIL,MAAM,IAAA,CAAK,GAAA,CAAI;oBACjB;oBACA,oBAAoB,OAAA,CAAQ,CAAA,OAAM;wBAChC,MAAM,SAASF,4MAAkB;4BAAC,MAAM;gCAAC,EAAE;6BAAA;wBAAC,CAAC;wBAC7C,GAAG,MAAA,GAAS;wBACZ,OAAO,MAAA,GAAS;wBAChB,MAAM,IAAA,CAAK,IAAA,CAAK,MAAM;oBACxB,CAAC;oBACD,OAAO,IAAI,KAAK;gBAClB;YACF;YAEA,MAAM,WAAA,GAAc;YACpB,MAAM,SAAS,SAAS;YACxB,MAAM,WAAA,GAAc;YACpB,OAAO;QACT;QAEA,MAAM,UAAU,IAAA,CAAK,CAAC,CAAA;QACtB,IAAA,2FAAA;QAEE,SAAS,WACT,CAAC,UACD,WAAA,wFAAA;QAAA,CAAA,CAGK,CAAC,MAAM,QAAA,IAAY,CAAC,MAAM,OAAA,KAC3B,OAAO,IAAA,KAAS,WAChB,CAAA,CAAE,SAAS,4BAAA,IAAgC,MAAM,QAAA,KAEjD,CAAC,MAAM,OAAA,IACP,OAAO,IAAA,KAAS,kBAAA,qDAAA;QAEhB,KAAK,MAAA,KAAW,KAChB,QAAQ,IAAA,KAAS,qBAAA,GAGrB;YAEA,OAAO,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,EAAE;QAC9B;QACA,IAAI,CAAC,MAAM,QAAA,IAAY,OAAO,IAAA,KAAS,kBAAkB;YACvD,MAAM,IAAI,MAAM,0DAA0D;QAC5E;QACA,OAAO,SAAS;IAClB;IAAA;;EAAA,GAKA,qBAAoB,IAAA,EAAM,KAAA,EAAO;QAC/B,MAAM,MAAM,KAAK,GAAA,CAAI,KAAA;QACrB,MAAM,MAAM,KAAK,GAAA,CAAI,KAAA;QACrB,MAAM,UAAU;YACd,UAAU;YACV,aAAa;YACb,UAAU,MAAM,QAAA;QAClB;QACA,MAAM,SAAS,cAAc,KAAK,OAAO;QACzC,MAAM,SAAS,cAAc,KAAK,OAAO;QACzC,MAAM,aAAa,aAAA,GAAA,IAAI,IAAI;QAC3B,IAAI,MAAM,oBAAA,IAAwB,MAAM,YAAA,CAAa,UAAA,EAAY;YAE/D,MAAM,oBAAoB,8BAA8B,IAAI;YAC5D,MAAM,SAAS,4BAA4B,iBAAiB;YAC5D,OAAO,OAAA,CAAQ,CAAA,UAAS;gBACtB,WAAW,GAAA,CACT,MAAM,OAAA,CAAQ,KAAK,IACjB,UAAG,cAAc,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,CAAC,EAAA,KAAoC,OAAhC,cAAc,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,CAAC,IACvE,cAAc,OAAO,OAAO;YAElC,CAAC;QACH;QAEA,OAAO,GAAa,MAAM,CAAhB,MAAM,EAAA,KAAa,CAAC,cAAuB;eAApB,UAAU;SAAA,CAAE,IAAA,CAAK,EAAE,CAAC;IACvD;IAAA;;EAAA,GAKA,mBAAsC,EAAG,KAAA,EAAO;cAAlC,IAAA,EAAM,MAAA,EAAQ,KAAA,EAAO,GAAA,CAAG,CAAA,GAAzB;QACX,IAAI,SAAS,OAAO;YAClB,OAAO,MAAM,YAAA,CAAa,MAAA,GACtB,MAAM,kBAAA,CAAmB,MAAA,IAAU,MAAM,WAAA,GAAe,MAAM,QAAA,sDAAA;YAEhE;QACJ;QACA,IAAI,SAAS,SAAS;YACpB,OAAO,SAAS,yBAAQ;QAC1B;QACA,IAAI,SAAS,YAAY;YACvB,IACE,MAAM,oBAAA,IACN,MAAM,YAAA,CAAa,UAAA,IACnB,kCAAkC,GAAA,CAAI,KAAK,GAC3C;gBAKA,MAAM,IAAI,MAAM,qBAA0B,OAAL,KAAK,EAAA,gEAAiE;YAC7G;YACA,OAAO,GAA6B,OAA1B,SAAS,yBAAQ,KAAK,mBAAA,KAA2B,KAAK,QAAtB,GAAM,OAAH,GAAG,EAAA,OAAM,EAAE,EAAQ,cAAA;QAClE;QACA,IAAI,SAAS,QAAQ;YACnB,OAAO,SAAS,yBAAQ;QAC1B;QAGA,MAAM,IAAI,MAAM,kCAAsC,OAAJ,IAAI,EAAA,EAAG;IAC3D;IAAA;;EAAA,GAKA,OAAM,IAAA,EAAM,KAAA,EAAO;QACjB,OAAA,2FAAA;QAAA,uFAAA;QAAA,iCAAA;QAAA,6BAAA;QAAA,gCAAA;QAAA,CAMG,MAAM,kBAAA,CAAmB,UAAA,GAAa,MAAM,EAAA,IAAA,CAC5C,KAAK,MAAA,GAAS,MAAM,EAAA,IAAA,CACpB,KAAK,MAAA,GAAS,MAAM,EAAA;IAIzB;IAAA;;EAAA,GAKA,YAAkC,EAAG,KAAA,EAAO,GAAA,EAAK;cAA1C,QAAAI,OAAAA,EAAQ,IAAA,EAAM,KAAA,EAAO,MAAA,CAAM,CAAA,GAA5B;QACJ,MAAM,eAAe,MAAM,YAAA;QAC3B,IAAI,OAAO;YACT,MAAM,YAAA,GAAe,mBAAmB,cAAc,KAAK;QAC7D;QACA,MAAM,WAAW,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,GAAG;QACvC,MAAM,SACJ,CAAC,MAAM,OAAA,IACP,KAAK,MAAA,KAAW,KAAA,aAAA;QAChB,OAAO,IAAA,KAAS,gBAChB,CAACA,WAAAA,CACA,CAAC,MAAM,WAAA,IAAe,CAAC,KAAA,IACrB,WAAW,YAAK,eAAeA,SAAQ,OAAO,MAAM,WAAW,CAAC,EAAW,OAAR,QAAQ,EAAA;QAChF,MAAM,YAAA,GAAe;QACrB,OAAO;IACT;IAAA;;EAAA,GAKA,0BAAuC,EAAG,CAAA,EAAG,GAAA,EAAK;cAA7B,IAAA,EAAM,IAAA,EAAM,MAAA,CAAM,CAAA,GAAnB;QAClB,MAAM,SAAS,GAAqC,OAAlC,SAAS,cAAc,KAAK,GAAG,EAAqB,gBAAT,MAAM,GAAG;QACtE,OAAO,YAAK,MAAM,EAA0B,OAAvB,KAAK,GAAA,CAAI,GAAG,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;IAC9C;IAAA;;EAAA,GAKA,YAAW,IAAA,EAAM,CAAA,EAAG,GAAA,EAAK;QACvB,OAAO,IAAI,KAAK,IAAI,IAAI,iBAAiB,IAAI;IAC/C;IAAA;;EAAA,GAKA,YAAW,KAAiB,EAAG,KAAA,EAAO;cAA1B,WAAA,EAAa,GAAA,CAAG,CAAA;QAC1B,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM,wDAAwD;QAC1E;QACA,MAAM,QAAQ,MAAM,cAAA;QAEpB,OAAO,QAAQ,IAAI,OAAY,OAAL,KAAK,EAAA,OAAM,uBAAO,GAAG,EAAM,KAAK;IAC5D;AACF;AAMA,IAAM,kBAAkB,aAAA,GAAA,IAAI,IAAI;IAC9B;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAM;IAAK;IAAK;IAAK;IAAK;CACnE;AAED,IAAM,uBAAuB,aAAA,GAAA,IAAI,IAAI;IACnC;IAAK;IAAM;IAAK;IAAA,+FAAA;IAAA,yFAAA;IAGhB;CACD;AAED,IAAM,4BAA4B,aAAA,GAAA,IAAI,IAAI;IACxC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAM;IAAK;IAAK;IAAK;IAAK;IAAA,+DAAA;IAEnD;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CACtF;AAED,IAAM,oBAAoB,aAAA,GAAA,IAAI,IAAI;IAChC;QAAE;QAAG,KAAK;KAAA;IAAA,iBAAA;IACV;QAAC;QAAI,KAAK;KAAA;IAAA,YAAA;IACV;QAAC;QAAI,KAAK;KAAA;IAAA,eAAA;IACV;QAAC;QAAI,KAAK;KAAA;IAAA,YAAA;IACV;QAAC;QAAI,KAAK;KAAA;IAAA,kBAAA;IACV;QAAC;QAAQ,SAAS;KAAA;IAAA,iBAAA;IAClB;QAAC;QAAQ,SAAS;KAAA;IAAA,sBAAA;IAClB;QAAC;QAAQ,SAAS;KAAA;CACnB;AAED,IAAM,UAAU;AAChB,SAAS,YAAY,IAAA,EAAM;IACzB,OAAO,QAAQ,IAAA,CAAK,IAAI;AAC1B;AAMA,SAAS,8BAA8B,IAAA,EAAM,OAAA,EAAS;IACpD,MAAM,YAAY,CAAC,CAzImD,CAyIlD,0DAAS,SAAA,AAvIvB;IAwIN,MAAM,MAAM,KAAK,GAAA,CAAI,KAAA;IACrB,MAAM,MAAM,KAAK,GAAA,CAAI,KAAA;IACrB,MAAM,QAAQ,CAAC,CAAA;IAIf,IAAK,MAAM,MAAA,CAAO,QAAQ,SAAU,OAAO,MAAA,KAAc,QAAQ,SAAW,OAAO,QAAU;QAC3F,OAAO;IACT;IACA,IAAA,IAAS,IAAI,KAAK,KAAK,KAAK,IAAK;QAC/B,MAAM,OAAO,GAAG,CAAC;QACjB,IAAI,CAAC,YAAY,IAAI,GAAG;YACtB;QACF;QACA,MAAM,oBAAoB,wBAAwB,IAAI,EAAE,MAAA,CAAO,CAAA,eAAc;YAC3E,MAAM,MAAM,WAAW,WAAA,CAAY,CAAC;YACpC,OAAO,MAAM,OAAO,MAAM;QAC5B,CAAC;QACD,IAAI,kBAAkB,MAAA,EAAQ;YAC5B,MAAM,IAAA,CAAK,GAAG,iBAAiB;YAC/B,IAAI,WAAW;gBACb;YACF;QACF;IACF;IACA,OAAO;AACT;AAGA,SAAS,cAAc,SAAA,OAA2C,EAAG;UAAlC,QAAA,EAAU,WAAA,EAAa,QAAA,CAAQ,CAAA,GAAhC;IAChC,IAAI,kBAAkB,GAAA,CAAI,SAAS,GAAG;QACpC,OAAO,kBAAkB,GAAA,CAAI,SAAS;IACxC;IACA,IAAA,+FAAA;IAEE,YAAY,MAAO,YAAY,OAAO,YAAY,OAAA,yEAAA;IAElD,YAAY,UAAA,wFAAA;IAEX,YAAY,gBAAgB,SAAS,GACtC;QAGA,OAAO,YAAY,MACjB,OAA2C,OAApC,UAAU,QAAA,CAAS,EAAE,EAAE,WAAA,CAAY,CAAC,EAAA,OAC3C,MAA2D,OAArD,UAAU,QAAA,CAAS,EAAE,EAAE,WAAA,CAAY,EAAE,QAAA,CAAS,GAAG,GAAG,CAAC;IAC/D;IACA,MAAM,cAAc,cACjB,WAAW,4BAA4B,uBACxC;IACF,MAAM,OAAO,GAAG,SAAS;IACzB,OAAA,CAAQ,YAAY,GAAA,CAAI,IAAI,IAAI,OAAO,EAAA,IAAM;AAC/C;AAEA,SAAS,4BAA4B,KAAA,EAAO;IAC1C,MAAM,aAAa,MAAM,GAAA,CAAI,CAAA,OAAQ,KAAK,WAAA,CAAY,CAAC,CAAC,EAAE,IAAA,CAAK,CAAC,GAAG,IAAM,IAAI,CAAC;IAC9E,MAAM,SAAS,CAAC,CAAA;IAChB,IAAI,QAAQ;IACZ,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAK;QAC1C,IAAI,UAAA,CAAW,IAAI,CAAC,CAAA,KAAM,UAAA,CAAW,CAAC,CAAA,GAAI,GAAG;YAC3C,kBAAA,mBAAA,QAAA,QAAU,UAAA,CAAW,CAAC,CAAA;QACxB,OAAA,IAAW,UAAU,MAAM;YACzB,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAC;QAC3B,OAAO;YACL,OAAO,IAAA,CAAK;gBAAC;gBAAO,UAAA,CAAW,CAAC,CAAC;aAAC;YAClC,QAAQ;QACV;IACF;IACA,OAAO;AACT;AAEA,SAAS,eAAeA,OAAAA,EAAQ,QAAA,EAAU,WAAA,EAAa;IACrD,IAAIA,SAAQ;QACV,OAAO;IACT;IACA,IAAI,OAAO;IACX,IAAI,YAAY,aAAa;QAC3B,MAAM,EAAC,MAAA,EAAQ,OAAA,CAAO,CAAA,GAAI;QAC1B,OAAA,CACG,wDAAQ,UAAA,IAAa,MAAM,EAAA,IAAA,kDAC3B,OAAQ,MAAA,IAAS,MAAM,EAAA,IAAA,CACvB,UAAU,MAAM,EAAA,IAAA,oDAChB,QAAS,UAAA,IAAa,MAAM,EAAA,IAAA,CAC5B,2DAAS,MAAA,IAAS,MAAM,EAAA;IAC7B;IACA,OAAO,GAAO,OAAJ,IAAI,EAAA;AAChB;AAMA,SAAS,sBAAgC,EAAG;UAAjB,IAAA,EAAM,GAAA,EAAK,GAAA,CAAG,CAAA,GAAf;IACxB,IAAI;IACJ,IAAI,CAAC,OAAO,QAAQ,GAAG;QACrB,OAAO;IACT,OAAA,IAAW,CAAC,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT,OAAA,IAAW,QAAQ,KAAK,QAAQ,UAAU;QACxC,OAAO;IACT,OAAA,IAAW,QAAQ,KAAK;QACtB,OAAO,IAAO,OAAH,GAAG,EAAA;IAChB,OAAO;QACL,OAAO,WAAI,GAAG,EAAA,KAA+B,OAA3B,QAAQ,WAAW,KAAK,GAAG,EAAA;IAC/C;IACA,OAAO,QAAO;QACZ,QAAQ;QACR,MAAM;QACN,YAAY;KACd,CAAA,CAAE,IAAI,CAAA;AACR;AAMA,SAAS,gBAAgB;UAAJ,IAAA,CAAI,CAAA,EAAG,CAAR;IAClB,OAAO,SAAS,oBACd,SAAS,WACT,SAAS;AACb;AAEA,SAAS,gBAAgB,KAAA,EAAO;IAC9B,OAAO,QAAQ,MAAM,QAAQ;AAC/B;AAMA,SAAS,qBAA4B;UAAX,IAAA,EAAM,KAAA,CAAK,CAAA,EAAG,CAAf;IACvB,OAAO,SAAS,eAAe,UAAU;AAC3C;;AC5nBA,IAAM,+NAgIJ,4DAhIF,MAAM,wBAAuB,OAAO;IAiCpB,qEAAA;IAGd,IAAI,SAAS;QACX,wLAAO,IAAA,EAAK,aAAY;IAC1B;IA+CA;;;;;EAAA,GAQA,KAAK,GAAA,EAAK;QAER,IAAI,kLAAC,IAAA,EAAK,YAAW;YACnB,MAAM,EAAC,WAAA,EAAa,GAAG,KAAI,CAAA,GAAI,IAAA,CAAK,UAAA;mMAC/B,WAAY,IAAI,iMAAe,IAAA,EAAK,WAAU,IAAA,CAAK,KAAA,EAAO,IAAI;QACrE;QAEA,MAAM,eAAe,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,MAAA;QACzC,MAAM,MAAM,IAAA,CAAK,SAAA;QAEjB,qLAAI,IAAA,EAAK,eAAc,iBAAiB,gBAAgB,KAAK;YAE3D,IAAA,CAAK,SAAA,GAAY;YAOjB,MAAM,8LAAQ,aAAK,eAAL,IAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAC;YAC3C,IAAI,OAAO;gBACT,4BAA4B,OAAO,KAAK,KAAK,IAAA,CAAK,UAAU;gBAC5D,IAAA,CAAK,SAAA,IAAa;YACpB;YACA,OAAO;QACT;QAEA,6LAAO,aAAK,eAAL,IAAA,EAAe,GAAG;IAC3B;IAnFA;;;;;EAAA,GAAA;;;;EAAA,GAaA,YAAY,OAAA,EAAS,KAAA,EAAO,OAAA,CAAS;QACnC,MAAM,cAAc,CAAC,EAAC,0DAAS,WAAA;QAC/B,IAAI,mBAAmB,QAAQ;YAG7B,IAAI,SAAS;gBACX,MAAM,IAAI,MAAM,8CAA8C;YAChE;YACA,MAAM,KAAK;YACX,KAAA,CAAM,IAAI,KAAK;;uBArDL,aAAA,GAAA;;;;;;;;;;;;;gBAoBF;;;EAAA,iMAME;mMA4BL,UAAW,GAAG,MAAA;YACnB,IAAI,cAAc,iBAAgB;uMAC3B,8LAAc,IAAG;uMACjB,2LAAW,IAAG;uMACd,4LAAY,IAAG;gBACpB,IAAA,CAAK,UAAA,GAAa,GAAG,UAAA;YACvB;QACF,OAAO;YACL,MAAM,OAAO;gBACX,gBAAgB,CAAC,CAAA;gBACjB,UAAU;gBACV,WAAW,CAAC,CAAA;gBACZ,GAAG,OAAA;YACL;YACA,KAAA,CAAM,cAAc,KAAK,SAAS,KAAK,wMApE3C;;uBAAc,IAAI,IAAI;wMAKtB;;uBAAY;;;4BAKZ;wMAKA;;uBAAW;wMAKX;;uBAAY;+LAMZ,cAAa;mMA2CJ,UAAW;mMACX,aAAc,iBAAiB,KAAK,cAAA,EAAgB,KAAK,SAAS;mMAClE,WAAY,KAAK,QAAA;YAEtB,IAAA,CAAK,UAAA,uBAAa,+BAAW,CAAC;QAChC;QACA,IAAI,CAAC,aAAa;mMACX,WAAY,IAAA;QACnB;IACF;AAsGF;AAEA,SAAS,4BAA4B,KAAA,EAAO,MAAA,EAAQ,KAAA,EAAO,UAAA,EAAY;IACrE,MAAM,KAAA,IAAS;IACf,MAAM,KAAA,GAAQ;IACd,IAAI,YAAY;QACd,MAAM,UAAU,MAAM,OAAA;QACtB,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAK;YACvC,MAAM,MAAM,OAAA,CAAQ,CAAC,CAAA;YACrB,IAAI,KAAK;gBAIP,OAAA,CAAQ,CAAC,CAAA,GAAI;oBAAC,GAAA,CAAI,CAAC,CAAA,GAAI;oBAAQ,GAAA,CAAI,CAAC,CAAA,GAAI,MAAM;iBAAA;YAChD;QACF;QACA,MAAM,eAAe,QAAQ,MAAA;QAC7B,IAAI,cAAc;YAChB,OAAO,IAAA,CAAK,YAAY,EAAE,OAAA,CAAQ,CAAA,QAAO;gBACvC,MAAM,MAAM,YAAA,CAAa,GAAG,CAAA;gBAC5B,IAAI,KAAK;oBACP,YAAA,CAAa,GAAG,CAAA,GAAI;wBAAC,GAAA,CAAI,CAAC,CAAA,GAAI;wBAAQ,GAAA,CAAI,CAAC,CAAA,GAAI,MAAM;qBAAA;gBACvD;YACF,CAAC;QACH;IACF;AACF;AAYA,SAAS,iBAAiB,cAAA,EAAgB,SAAA,EAAW;IACnD,MAAM,aAAa,aAAA,GAAA,IAAI,IAAI;IAC3B,KAAA,MAAW,OAAO,eAAgB;QAChC,WAAW,GAAA,CAAI,KAAK;YAClB,QAAQ;QACV,CAAC;IACH;IACA,KAAA,MAAW,CAAC,IAAI,IAAI,CAAA,IAAK,UAAW;QAClC,KAAA,MAAW,OAAO,KAAM;YACtB,YAAY,YAAY,KAAK,CAAC,CAAC,EAAE,UAAA,GAAa;QAChD;IACF;IACA,OAAO;AACT;AAMA,SAAS,cAAc,OAAA,EAAS;IAC9B,MAAM,KAAK;IACX,MAAM,MAAM,aAAA,GAAA,IAAI,IAAI;IACpB,IAAI,qBAAqB;IACzB,IAAI,cAAc;IAClB,IAAI;IACJ,MAAQ,QAAQ,GAAG,IAAA,CAAK,OAAO,EAAI;QACjC,MAAM,EAAC,GAAG,CAAA,EAAG,QAAQ,EAAC,OAAA,EAAS,IAAA,CAAI,CAAA,CAAC,CAAA,GAAI;QAIxC,IAAI,MAAM,KAAK;YACb;QACF,OAAA,IAAW,CAAC,oBAAoB;YAC9B,IAAI,SAAS;gBACX;gBACA,IAAI,MAAM;oBACR,IAAI,GAAA,CAAI,aAAa,IAAI;gBAC3B;YACF;QACF,OAAA,IAAW,MAAM,KAAK;YACpB;QACF;IACF;IACA,OAAO;AACT;;;;ACzOA,SAAS,SAAS,OAAA,EAAS,OAAA,EAAS;IAClC,MAAM,IAAI,gBAAgB,SAAS,OAAO;IAC1C,IAAI,EAAE,OAAA,EAAS;QACb,OAAO,IAAI,eAAe,EAAE,OAAA,EAAS,EAAE,KAAA,EAAO,EAAE,OAAO;IACzD;IACA,OAAO,IAAI,OAAO,EAAE,OAAA,EAAS,EAAE,KAAK;AACtC;AAYA,SAAS,gBAAgB,OAAA,EAAS,OAAA,EAAS;IACzC,MAAM,OAAO,WAAW,OAAO;IAC/B,MAAM,mBAAeC,0LAAAA,EAAM,SAAS;QAClC,OAAO,KAAK,KAAA;QACZ,+BAA+B;QAC/B,OAAO;YACL,cAAc,KAAK,KAAA,CAAM,YAAA;YACzB,YAAY,KAAK,KAAA,CAAM,UAAA;QACzB;QACA,uBAAuB,KAAK,KAAA,CAAM,mBAAA;QAClC,oBAAoB;IACtB,CAAC;IACD,MAAM,eAAe,UAAU,cAAc;QAC3C,UAAU,KAAK,QAAA;QACf,qBAAqB,KAAK,KAAA,CAAM,mBAAA;QAChC,eAAe,KAAK,aAAA;QACpB,kBAAkB,KAAK,MAAA;IACzB,CAAC;IACD,MAAM,YAAY,SAAS,cAAc,IAAI;IAC7C,MAAM,sBAAkB,kKAAA,EAAU,UAAU,OAAA,EAAS;QACnD,kBAAkB,UAAU,iBAAA;QAC5B,gBAAgB,UAAU,eAAA;QAC1B,MAAM;IACR,CAAC;IACD,MAAM,uBAAmB,uJAAA,EAAW,gBAAgB,OAAO;IAC3D,MAAM,mBAAe,mJAAA,EAAO,iBAAiB,OAAA,EAAS;QACpD,kBAAkB,gBAAgB,gBAAA;QAClC,gBAAgB,gBAAgB,cAAA;IAClC,CAAC;IACD,MAAM,UAAU;QACd,SAAS,aAAa,OAAA;QACtB,OAAO,UAAG,KAAK,UAAA,GAAa,MAAM,EAAE,SAAG,KAAK,MAAA,GAAS,MAAM,EAAE,SAAG,UAAU,KAAK,EAA0C,OAAvC,UAAU,OAAA,CAAQ,OAAA,CAAQ,CAAA,GAAI,MAAM,GAAG;IAC3H;IACA,IAAI,KAAK,aAAA,EAAe;QACtB,IAAI,KAAK,iBAAA,KAAsB,UAAU;YACvC,MAAM,IAAI,MAAM,oCAAoC;QACtD;IACF,OAAO;QAEL,MAAM,iBAAiB,aAAa,cAAA,CAAe,IAAA,CAAK,CAAC,GAAG,IAAM,IAAI,CAAC;QAEvE,MAAM,YAAY,MAAM,IAAA,CAAK,aAAa,gBAAgB;QAC1D,MAAM,WAAW,aAAa,SAAA;QAC9B,MAAM,cAAc,QAAQ,OAAA,CAAQ,MAAA,IAAU,KAAK,iBAAA;QACnD,IAAI,eAAe,MAAA,IAAU,UAAU,MAAA,IAAU,YAAY,aAAa;YACxE,QAAQ,OAAA,GAAU;gBAChB,GAAI,eAAe,MAAA,IAAU;oBAAC;gBAAc,CAAA;gBAC5C,GAAI,UAAU,MAAA,IAAU;oBAAC;gBAAS,CAAA;gBAClC,GAAI,YAAY;oBAAC;gBAAQ,CAAA;gBACzB,GAAI,eAAe;oBAAC;gBAAW,CAAA;YACjC;QACF;IACF;IACA,OAAO;AACT;;kBDoBY,GAAA,EAAK;IAEb,iLAAA,IAAA,EAAK,WAAU,SAAA,GAAY,IAAA,CAAK,SAAA;IAChC,MAAM,4WAAc,QAAN,IAAA,EAAW,IAAA,kLAAK,IAAA,EAAK,YAAW,GAAG;IACjD,IAAA,CAAK,SAAA,oLAAY,IAAA,EAAK,WAAU,SAAA;IAEhC,IAAI,CAAC,SAAS,kLAAC,IAAA,EAAK,aAAY,IAAA,EAAM;QACpC,OAAO;IACT;IAEA,MAAM,YAAY,CAAC;WAAG,KAAK;KAAA;IAE3B,MAAM,MAAA,GAAS;IACf,IAAI;IACJ,IAAI,IAAA,CAAK,UAAA,EAAY;QACnB,cAAc,CAAC;eAAG,MAAM,OAAO;SAAA;QAC/B,MAAM,OAAA,CAAQ,MAAA,GAAS;IACzB;IACA,MAAM,aAAa;QAAC,CAAC;KAAA;IACrB,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;;QACzC,MAAM,EAAC,MAAA,EAAQ,UAAA,CAAU,CAAA,IAAI,oNAAA,EAAK,aAAY,GAAA,CAAI,CAAC,wFAAK,CAAC;QACzD,IAAI,QAAQ;YACV,WAAW,IAAA,CAAK,IAAI;QACtB,OAAO;YACL,WAAW,IAAA,CAAK,MAAM,MAAM;YAC5B,MAAM,IAAA,CAAK,SAAA,CAAU,CAAC,CAAC;YACvB,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,MAAM,OAAA,CAAQ,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;YACnC;QACF;QAGA,IAAI,cAAc,SAAA,CAAU,CAAC,CAAA,KAAM,KAAA,GAAW;YAC5C,MAAM,KAAK,UAAA,CAAW,UAAU,CAAA;YAChC,IAAI,CAAC,IAAI;gBACP,MAAM,IAAI,MAAM,gCAAkC,OAAF,EAAE,EAAA,EAAG;YACvD;YACA,KAAA,CAAM,EAAE,CAAA,GAAI,SAAA,CAAU,CAAC,CAAA;YACvB,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,MAAM,OAAA,CAAQ,EAAE,CAAA,GAAI,WAAA,CAAY,CAAC,CAAA;YACnC;YACA,IAAI,MAAM,MAAA,EAAQ;gBAChB,IAAI,kLAAC,IAAA,EAAK,WAAU;2MAEb,UAAW,cAAc,IAAA,CAAK,MAAM;gBAC3C;gBACA,MAAM,wLAAO,IAAA,EAAK,UAAS,GAAA,CAAI,UAAU;gBACzC,IAAI,MAAM;oBACR,MAAM,MAAA,CAAO,IAAI,CAAA,GAAI,SAAA,CAAU,CAAC,CAAA;oBAChC,IAAI,IAAA,CAAK,UAAA,EAAY;wBACnB,MAAM,OAAA,CAAQ,MAAA,CAAO,IAAI,CAAA,GAAI,WAAA,CAAY,CAAC,CAAA;oBAC5C;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40shikijs/engine-javascript/dist/index.mjs"], "sourcesContent": ["export { createJavaScriptRegexEngine, defaultJavaScriptRegexConstructor } from './engine-compile.mjs';\nexport { createJavaScriptRawEngine } from './engine-raw.mjs';\nexport { J as JavaScriptScanner } from './shared/engine-javascript.hzpS1_41.mjs';\nimport 'oniguruma-to-es';\n"], "names": [], "mappings": ";AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40shikijs/engine-javascript/dist/shared/engine-javascript.hzpS1_41.mjs"], "sourcesContent": ["const MAX = 4294967295;\nclass JavaScriptScanner {\n  constructor(patterns, options = {}) {\n    this.patterns = patterns;\n    this.options = options;\n    const {\n      forgiving = false,\n      cache,\n      regexConstructor\n    } = options;\n    if (!regexConstructor) {\n      throw new Error(\"Option `regexConstructor` is not provided\");\n    }\n    this.regexps = patterns.map((p) => {\n      if (typeof p !== \"string\") {\n        return p;\n      }\n      const cached = cache?.get(p);\n      if (cached) {\n        if (cached instanceof RegExp) {\n          return cached;\n        }\n        if (forgiving)\n          return null;\n        throw cached;\n      }\n      try {\n        const regex = regexConstructor(p);\n        cache?.set(p, regex);\n        return regex;\n      } catch (e) {\n        cache?.set(p, e);\n        if (forgiving)\n          return null;\n        throw e;\n      }\n    });\n  }\n  regexps;\n  findNextMatchSync(string, startPosition, _options) {\n    const str = typeof string === \"string\" ? string : string.content;\n    const pending = [];\n    function toResult(index, match, offset = 0) {\n      return {\n        index,\n        captureIndices: match.indices.map((indice) => {\n          if (indice == null) {\n            return {\n              start: MAX,\n              end: MAX,\n              length: 0\n            };\n          }\n          return {\n            start: indice[0] + offset,\n            end: indice[1] + offset,\n            length: indice[1] - indice[0]\n          };\n        })\n      };\n    }\n    for (let i = 0; i < this.regexps.length; i++) {\n      const regexp = this.regexps[i];\n      if (!regexp)\n        continue;\n      try {\n        regexp.lastIndex = startPosition;\n        const match = regexp.exec(str);\n        if (!match)\n          continue;\n        if (match.index === startPosition) {\n          return toResult(i, match, 0);\n        }\n        pending.push([i, match, 0]);\n      } catch (e) {\n        if (this.options.forgiving)\n          continue;\n        throw e;\n      }\n    }\n    if (pending.length) {\n      const minIndex = Math.min(...pending.map((m) => m[1].index));\n      for (const [i, match, offset] of pending) {\n        if (match.index === minIndex) {\n          return toResult(i, match, offset);\n        }\n      }\n    }\n    return null;\n  }\n}\n\nexport { JavaScriptScanner as J };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,MAAM;AACZ,MAAM;IAsCJ,kBAAkB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE;QACjD,MAAM,MAAM,OAAO,WAAW,WAAW,SAAS,OAAO,OAAO;QAChE,MAAM,UAAU,EAAE;QAClB,SAAS,SAAS,KAAK,EAAE,KAAK;gBAAE,SAAA,iEAAS;YACvC,OAAO;gBACL;gBACA,gBAAgB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;oBACjC,IAAI,UAAU,MAAM;wBAClB,OAAO;4BACL,OAAO;4BACP,KAAK;4BACL,QAAQ;wBACV;oBACF;oBACA,OAAO;wBACL,OAAO,MAAM,CAAC,EAAE,GAAG;wBACnB,KAAK,MAAM,CAAC,EAAE,GAAG;wBACjB,QAAQ,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;oBAC/B;gBACF;YACF;QACF;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC5C,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YAC9B,IAAI,CAAC,QACH;YACF,IAAI;gBACF,OAAO,SAAS,GAAG;gBACnB,MAAM,QAAQ,OAAO,IAAI,CAAC;gBAC1B,IAAI,CAAC,OACH;gBACF,IAAI,MAAM,KAAK,KAAK,eAAe;oBACjC,OAAO,SAAS,GAAG,OAAO;gBAC5B;gBACA,QAAQ,IAAI,CAAC;oBAAC;oBAAG;oBAAO;iBAAE;YAC5B,EAAE,OAAO,GAAG;gBACV,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB;gBACF,MAAM;YACR;QACF;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,CAAC,KAAK;YAC1D,KAAK,MAAM,CAAC,GAAG,OAAO,OAAO,IAAI,QAAS;gBACxC,IAAI,MAAM,KAAK,KAAK,UAAU;oBAC5B,OAAO,SAAS,GAAG,OAAO;gBAC5B;YACF;QACF;QACA,OAAO;IACT;IAvFA,YAAY,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAE;QAoCpC,+KAAA,WAAA,KAAA;QAnCE,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,MAAM,EACJ,YAAY,KAAK,EACjB,KAAK,EACL,gBAAgB,EACjB,GAAG;QACJ,IAAI,CAAC,kBAAkB;YACrB,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC;YAC3B,IAAI,OAAO,MAAM,UAAU;gBACzB,OAAO;YACT;YACA,MAAM,SAAS,kBAAA,4BAAA,MAAO,GAAG,CAAC;YAC1B,IAAI,QAAQ;gBACV,IAAI,kBAAkB,QAAQ;oBAC5B,OAAO;gBACT;gBACA,IAAI,WACF,OAAO;gBACT,MAAM;YACR;YACA,IAAI;gBACF,MAAM,QAAQ,iBAAiB;gBAC/B,kBAAA,4BAAA,MAAO,GAAG,CAAC,GAAG;gBACd,OAAO;YACT,EAAE,OAAO,GAAG;gBACV,kBAAA,4BAAA,MAAO,GAAG,CAAC,GAAG;gBACd,IAAI,WACF,OAAO;gBACT,MAAM;YACR;QACF;IACF;AAqDF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40shikijs/engine-javascript/dist/engine-compile.mjs"], "sourcesContent": ["import { toRegExp } from 'oniguruma-to-es';\nimport { J as JavaScriptScanner } from './shared/engine-javascript.hzpS1_41.mjs';\n\nfunction defaultJavaScriptRegexConstructor(pattern, options) {\n  return toRegExp(\n    pattern,\n    {\n      global: true,\n      hasIndices: true,\n      // This has no benefit for the standard JS engine, but it avoids a perf penalty for\n      // precompiled grammars when constructing extremely long patterns that aren't always used\n      lazyCompileLength: 3e3,\n      rules: {\n        // Needed since TextMate grammars merge backrefs across patterns\n        allowOrphanBackrefs: true,\n        // Improves search performance for generated regexes\n        asciiWordBoundaries: true,\n        // Follow `vscode-oniguruma` which enables this Oniguruma option by default\n        captureGroup: true,\n        // Oniguruma uses depth limit `20`; lowered here to keep regexes shorter and maybe\n        // sometimes faster, but can be increased if issues reported due to low limit\n        recursionLimit: 5,\n        // Oniguruma option for `^`->`\\A`, `$`->`\\Z`; improves search performance without any\n        // change in meaning since TM grammars search line by line\n        singleline: true\n      },\n      ...options\n    }\n  );\n}\nfunction createJavaScriptRegexEngine(options = {}) {\n  const _options = Object.assign(\n    {\n      target: \"auto\",\n      cache: /* @__PURE__ */ new Map()\n    },\n    options\n  );\n  _options.regexConstructor ||= (pattern) => defaultJavaScriptRegexConstructor(pattern, { target: _options.target });\n  return {\n    createScanner(patterns) {\n      return new JavaScriptScanner(patterns, _options);\n    },\n    createString(s) {\n      return {\n        content: s\n      };\n    }\n  };\n}\n\nexport { createJavaScriptRegexEngine, defaultJavaScriptRegexConstructor };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,SAAS,kCAAkC,OAAO,EAAE,OAAO;IACzD,OAAO,IAAA,4KAAQ,EACb,SACA;QACE,QAAQ;QACR,YAAY;QACZ,mFAAmF;QACnF,yFAAyF;QACzF,mBAAmB;QACnB,OAAO;YACL,gEAAgE;YAChE,qBAAqB;YACrB,oDAAoD;YACpD,qBAAqB;YACrB,2EAA2E;YAC3E,cAAc;YACd,kFAAkF;YAClF,6EAA6E;YAC7E,gBAAgB;YAChB,qFAAqF;YACrF,0DAA0D;YAC1D,YAAY;QACd;QACA,GAAG,OAAO;IACZ;AAEJ;AACA,SAAS;QAA4B,UAAA,iEAAU,CAAC;QAQ9C;IAPA,MAAM,YAAW,OAAO,MAAM,CAC5B;QACE,QAAQ;QACR,OAAO,aAAa,GAAG,IAAI;IAC7B,GACA;IAEF,CAAA,WAAA,WAAS,qBAAT,SAAS,mBAAqB,CAAC,UAAY,kCAAkC,SAAS;YAAE,QAAQ,UAAS,MAAM;QAAC;IAChH,OAAO;QACL,eAAc,QAAQ;YACpB,OAAO,IAAI,gNAAiB,CAAC,UAAU;QACzC;QACA,cAAa,CAAC;YACZ,OAAO;gBACL,SAAS;YACX;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40shikijs/engine-javascript/dist/engine-raw.mjs"], "sourcesContent": ["import { J as JavaScriptScanner } from './shared/engine-javascript.hzpS1_41.mjs';\n\nfunction createJavaScriptRawEngine() {\n  const options = {\n    cache: /* @__PURE__ */ new Map(),\n    regexConstructor: () => {\n      throw new Error(\"JavaScriptRawEngine: only support precompiled grammar\");\n    }\n  };\n  return {\n    createScanner(patterns) {\n      return new JavaScriptScanner(patterns, options);\n    },\n    createString(s) {\n      return {\n        content: s\n      };\n    }\n  };\n}\n\nexport { createJavaScriptRawEngine };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS;IACP,MAAM,UAAU;QACd,OAAO,aAAa,GAAG,IAAI;QAC3B,kBAAkB;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IACA,OAAO;QACL,eAAc,QAAQ;YACpB,OAAO,IAAI,gNAAiB,CAAC,UAAU;QACzC;QACA,cAAa,CAAC;YACZ,OAAO;gBACL,SAAS;YACX;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}]}