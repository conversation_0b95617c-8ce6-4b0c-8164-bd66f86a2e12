---
title: Get special event
full: true
_openapi:
  method: GET
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Get details about a special event.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

Get details about a special event.

<APIPage document={"./content/docs/api-reference/openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"get"}]} webhooks={[]} hasHead={false} />