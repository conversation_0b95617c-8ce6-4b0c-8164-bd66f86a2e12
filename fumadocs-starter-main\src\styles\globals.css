@import 'tailwindcss' source(none);
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';
@import 'fumadocs-twoslash/twoslash.css';
@import 'fumadocs-openapi/css/preset.css';
@import 'tw-animate-css';

@source ".";
@source "../components";
@source "../content";
@source "../app";

@custom-variant dark (&:is(.dark *));

@theme {
  @keyframes stroke {
    from {
      stroke-dasharray: 1000;
    }

    to {
      stroke-dasharray: 1000;
      stroke-dashoffset: 2000;
    }
  }

  --animate-stroke: stroke 5s linear infinite;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  --color-gradient-radial: radial-gradient(circle, var(--tw-gradient-stops));
  --color-repeat-gradient-to-r: repeating-linear-gradient(
    to right,
    var(--tw-gradient-stops)
  );
  --color-repeat-gradient-to-br: repeating-linear-gradient(
    to bottom right,
    var(--tw-gradient-stops)
  );
}

@theme {
  --color-background: var(--color-fd-background);
  --color-foreground: var(--color-fd-foreground);

  --color-card: var(--color-fd-card);
  --color-card-foreground: var(--color-fd-card-foreground);

  --color-popover: var(--color-fd-popover);
  --color-popover-foreground: var(--color-fd-popover-foreground);

  --color-primary: var(--color-fd-primary);
  --color-primary-foreground: var(--color-fd-primary-foreground);

  --color-secondary: var(--color-fd-secondary);
  --color-secondary-foreground: var(--color-fd-secondary-foreground);

  --color-muted: var(--color-fd-muted);
  --color-muted-foreground: var(--color-fd-muted-foreground);

  --color-accent: var(--color-fd-accent);
  --color-accent-foreground: var(--color-fd-accent-foreground);

  --color-destructive: var(--color-fd-destructive);
  --color-destructive-foreground: var(--color-fd-destructive-foreground);

  --color-border: var(--color-fd-border);
  --color-input: var(--color-fd-border);
  --color-ring: var(--color-fd-ring);
}

@layer base {
  :root {
    --app-color: hsl(220deg 91% 54%);
    --api-reference-color: hsl(250 80% 54%);
    --changelog-color: var(--color-fd-foreground);
  }

  .dark {
    --app-color: hsl(217deg 92% 76%);
    --api-reference-color: hsl(250 100% 80%);
    --changelog-color: var(--color-fd-foreground);
  }
}

@layer base {
  body {
    overscroll-behavior-y: none;
    background-color: var(--color-fd-background);
  }

  ::selection {
    background-color: var(--color-accent);
    color: var(--color-foreground);
  }

  .dark ::selection {
    background-color: var(--color-accent);
    color: var(--color-foreground);
  }

  .app {
    --color-fd-primary: var(--app-color) !important;
  }

  .api-reference {
    --color-fd-primary: var(--api-reference-color) !important;
  }

  .changelog {
    --color-fd-primary: var(--changelog-color) !important;
  }
}

.dark [data-hide-on-theme='dark'],
.light [data-hide-on-theme='light'] {
  display: none;
}
