/**
 * Parse JSX (text).
 *
 * @param {Acorn | undefined} acorn
 *   Acorn parser to use (optional).
 * @param {Options} options
 *   Configuration.
 * @returns {Construct}
 *   Construct.
 */
export function jsxText(acorn: Acorn | undefined, options: Options): Construct;
import type { Acorn } from 'micromark-util-events-to-acorn';
import type { Options } from 'micromark-extension-mdx-jsx';
import type { Construct } from 'micromark-util-types';
//# sourceMappingURL=jsx-text.d.ts.map