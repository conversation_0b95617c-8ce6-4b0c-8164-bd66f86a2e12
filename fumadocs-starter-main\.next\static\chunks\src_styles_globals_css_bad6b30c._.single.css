/* [project]/src/styles/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-border-style: solid;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-content: "";
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --radix-collapsible-content-height: 0px;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-blur: 0;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-blur: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }

    ::backdrop {
      --tw-border-style: solid;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-content: "";
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --radix-collapsible-content-height: 0px;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-blur: 0;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-blur: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-red-400: #ff6568;
    --color-red-500: #fb2c36;
    --color-red-600: #e40014;
    --color-orange-400: #ff8b1a;
    --color-orange-600: #f05100;
    --color-amber-500: #f99c00;
    --color-yellow-400: #fac800;
    --color-yellow-600: #cd8900;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-neutral-950: #0a0a0a;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-sm: 40rem;
    --container-sm: 24rem;
    --container-lg: 32rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-extrabold: 800;
    --tracking-tight: -.025em;
    --radius-xs: .125rem;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-fd-background: #f5f5f5;
    --color-fd-foreground: #0a0a0a;
    --color-fd-muted: #f5f5f5;
    --color-fd-muted-foreground: #737373;
    --color-fd-popover: #fafafa;
    --color-fd-popover-foreground: #272727;
    --color-fd-card: #f1f1f1;
    --color-fd-card-foreground: #0a0a0a;
    --color-fd-border: rgba(158, 158, 158, .2);
    --color-fd-primary: #171717;
    --color-fd-primary-foreground: #fafafa;
    --color-fd-secondary: #ededed;
    --color-fd-secondary-foreground: #171717;
    --color-fd-accent: rgba(209, 209, 209, .5);
    --color-fd-accent-foreground: #171717;
    --color-fd-ring: #a3a3a3;
    --color-fd-info: #3080ff;
    --color-fd-warning: #f99c00;
    --color-fd-error: #fb2c36;
    --color-fd-success: #00c758;
    --fd-sidebar-mobile-offset: 100%;
    --spacing-fd-container: 1400px;
    --fd-page-width: 1200px;
    --fd-sidebar-width: 0px;
    --fd-toc-width: 0px;
    --fd-layout-width: 100vw;
    --fd-banner-height: 0px;
    --fd-nav-height: 0px;
    --fd-tocnav-height: 0px;
    --color-fd-diff-remove: rgba(200, 10, 100, .12);
    --color-fd-diff-remove-symbol: #e60a64;
    --color-fd-diff-add: rgba(14, 180, 100, .1);
    --color-fd-diff-add-symbol: #0ac864;
    --animate-fd-fade-in: fd-fade-in .3s ease;
    --animate-fd-fade-out: fd-fade-out .3s ease;
    --animate-fd-dialog-in: fd-dialog-in .3s cubic-bezier(.16, 1, .3, 1);
    --animate-fd-dialog-out: fd-dialog-out .3s cubic-bezier(.16, 1, .3, 1);
    --animate-fd-popover-in: fd-popover-in .13s ease;
    --animate-fd-popover-out: fd-popover-out .13s ease;
    --animate-fd-collapsible-down: fd-collapsible-down .15s cubic-bezier(.45, 0, .55, 1);
    --animate-fd-collapsible-up: fd-collapsible-up .15s cubic-bezier(.45, 0, .55, 1);
    --animate-fd-accordion-down: fd-accordion-down .2s ease-out;
    --animate-fd-accordion-up: fd-accordion-up .2s ease-out;
    --animate-fd-nav-menu-in: fd-nav-menu-in .2s ease;
    --animate-fd-nav-menu-out: fd-nav-menu-out .2s ease;
    --animate-fd-enterFromLeft: fd-enterFromLeft .25s ease;
    --animate-fd-enterFromRight: fd-enterFromRight .25s ease;
    --animate-fd-sidebar-in: fd-sidebar-in .25s ease;
    --animate-fd-sidebar-out: fd-sidebar-out .25s ease;
    --animate-fd-exitToLeft: fd-exitToLeft .25s ease;
    --animate-fd-exitToRight: fd-exitToRight .25s ease;
    --color-twoslash-error-foreground: #d45656;
    --color-twoslash-error: rgba(212, 86, 86, .125);
    --color-twoslash-tag-foreground: #3772cf;
    --color-twoslash-tag: rgba(55, 114, 207, .125);
    --color-twoslash-tag-warn-foreground: #c37d0d;
    --color-twoslash-tag-warn: rgba(195, 125, 13, .125);
    --color-twoslash-tag-annotate-foreground: #1ba673;
    --color-twoslash-tag-annotate: rgba(27, 166, 115, .125);
    --color-background: var(--color-fd-background);
    --color-foreground: var(--color-fd-foreground);
    --color-popover: var(--color-fd-popover);
    --color-popover-foreground: var(--color-fd-popover-foreground);
    --color-primary: var(--color-fd-primary);
    --color-primary-foreground: var(--color-fd-primary-foreground);
    --color-secondary: var(--color-fd-secondary);
    --color-secondary-foreground: var(--color-fd-secondary-foreground);
    --color-muted: var(--color-fd-muted);
    --color-muted-foreground: var(--color-fd-muted-foreground);
    --color-accent: var(--color-fd-accent);
    --color-accent-foreground: var(--color-fd-accent-foreground);
    --color-destructive: var(--color-fd-destructive);
    --color-border: var(--color-fd-border);
    --color-input: var(--color-fd-border);
    --color-ring: var(--color-fd-ring);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-400: color(display-p3 .933534 .431676 .423491);
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-red-600: color(display-p3 .830323 .140383 .133196);
      --color-orange-400: color(display-p3 .950192 .561807 .211017);
      --color-orange-600: color(display-p3 .887467 .341665 .0219962);
      --color-amber-500: color(display-p3 .93994 .620584 .0585367);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-yellow-600: color(display-p3 .776342 .542492 .041709);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-neutral-950: color(display-p3 .0393882 .0393882 .0393882);
      --color-fd-info: color(display-p3 .266422 .491219 .988624);
      --color-fd-warning: color(display-p3 .93994 .620584 .0585367);
      --color-fd-error: color(display-p3 .903738 .262579 .253307);
      --color-fd-success: color(display-p3 .308734 .774754 .374307);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-400: lab(63.7053% 60.7449 31.3109);
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-red-600: lab(48.4493% 77.4328 61.5452);
      --color-orange-400: lab(70.0429% 42.5156 75.8207);
      --color-orange-600: lab(57.1026% 64.2584 89.8886);
      --color-amber-500: lab(72.7183% 31.8672 97.9407);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-yellow-600: lab(62.7799% 22.4198 86.1544);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-blue-400: lab(65.0361% -1.42062 -56.9803);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-neutral-950: lab(2.75381% 0 0);
      --color-fd-info: lab(54.1736% 13.3368 -74.6839);
      --color-fd-warning: lab(72.7183% 31.8672 97.9407);
      --color-fd-error: lab(55.4814% 75.0732 48.8528);
      --color-fd-success: lab(70.5521% -66.5147 45.8072);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  *, :after, :before {
    border-color: var(--color-fd-border, currentColor);
  }

  ::backdrop {
    border-color: var(--color-fd-border, currentColor);
  }

  ::-webkit-file-upload-button {
    border-color: var(--color-fd-border, currentColor);
  }

  ::file-selector-button {
    border-color: var(--color-fd-border, currentColor);
  }

  body {
    background-color: var(--color-fd-background);
    color: var(--color-fd-foreground);
  }

  [data-rmiz-modal-overlay="visible"] {
    background-color: var(--color-fd-background);
  }

  :root, #nd-docs-layout {
    --fd-layout-offset: max(calc(50vw - var(--fd-layout-width) / 2), 0px);
  }

  :root {
    --app-color: #1f66f4;
    --api-reference-color: #4b2ce8;
    --changelog-color: var(--color-fd-foreground);
  }

  .dark {
    --app-color: #89b5fa;
    --api-reference-color: #a9f;
    --changelog-color: var(--color-fd-foreground);
  }

  body {
    overscroll-behavior-y: none;
    background-color: var(--color-fd-background);
  }

  ::selection, .dark ::selection {
    background-color: var(--color-accent);
    color: var(--color-foreground);
  }

  .app {
    --color-fd-primary: var(--app-color) !important;
  }

  .api-reference {
    --color-fd-primary: var(--api-reference-color) !important;
  }

  .changelog {
    --color-fd-primary: var(--changelog-color) !important;
  }
}

@layer components;

@layer utilities {
  .\@container {
    container-type: inline-size;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .fd-step:before {
    background-color: var(--color-fd-secondary);
    color: var(--color-fd-secondary-foreground);
    content: counter(step);
    counter-increment: step;
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
    border-radius: 3.40282e38px;
    justify-content: center;
    align-items: center;
    font-size: .875rem;
    line-height: 1.25rem;
    display: flex;
    position: absolute;
  }

  .fd-step:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * -4);
  }

  .fd-step:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * -4);
  }

  .fd-step:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * -4);
  }

  .fd-step:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * -4);
  }

  .fd-step:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * -4);
  }

  .fd-step:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * -4);
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .fd-steps {
    counter-reset: step;
    margin-left: calc(var(--spacing) * 2);
    border-left-style: var(--tw-border-style);
    padding-left: calc(var(--spacing) * 6);
    border-left-width: 1px;
    position: relative;
  }

  @media (min-width: 40rem) {
    .fd-steps {
      margin-left: calc(var(--spacing) * 4);
    }

    .fd-steps {
      padding-left: calc(var(--spacing) * 7);
    }
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-x-2 {
    inset-inline: calc(var(--spacing) * 2);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .inset-y-1 {
    inset-block: calc(var(--spacing) * 1);
  }

  .start-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 0);
  }

  .start-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 0);
  }

  .start-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 0);
  }

  .start-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 0);
  }

  .start-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 0);
  }

  .start-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 0);
  }

  .start-2\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 2.5);
  }

  .start-2\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 2.5);
  }

  .start-2\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 2.5);
  }

  .start-2\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 2.5);
  }

  .start-2\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 2.5);
  }

  .start-2\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 2.5);
  }

  .start-4\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 4.5);
  }

  .start-4\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 4.5);
  }

  .start-4\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: calc(var(--spacing) * 4.5);
  }

  .start-4\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 4.5);
  }

  .start-4\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 4.5);
  }

  .start-4\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(var(--spacing) * 4.5);
  }

  .end-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 0);
  }

  .end-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 0);
  }

  .end-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 0);
  }

  .end-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 0);
  }

  .end-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 0);
  }

  .end-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 0);
  }

  .end-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 1);
  }

  .end-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 1);
  }

  .end-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 1);
  }

  .end-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 1);
  }

  .end-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 1);
  }

  .end-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 1);
  }

  .end-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 2);
  }

  .end-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 2);
  }

  .end-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: calc(var(--spacing) * 2);
  }

  .end-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 2);
  }

  .end-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 2);
  }

  .end-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(var(--spacing) * 2);
  }

  .-top-1\.5 {
    top: calc(var(--spacing) * -1.5);
  }

  .-top-16 {
    top: calc(var(--spacing) * -16);
  }

  .top-\(--fd-banner-height\) {
    top: var(--fd-banner-height);
  }

  .top-\(--fd-sidebar-top\) {
    top: var(--fd-sidebar-top);
  }

  .top-\(--fd-top\) {
    top: var(--fd-top);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[112px\] {
    top: 112px;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .bottom-\(--fd-sidebar-margin\) {
    bottom: var(--fd-sidebar-margin);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-1\.5 {
    bottom: calc(var(--spacing) * 1.5);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2, .left-\[50\%\] {
    left: 50%;
  }

  .z-2 {
    z-index: 2;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[-1\] {
    z-index: -1;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .col-start-1 {
    grid-column-start: 1;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .container {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 1400px;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * .5);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .\!my-0 {
    margin-block: calc(var(--spacing) * 0) !important;
  }

  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-auto {
    margin-top: auto;
    margin-bottom: auto;
  }

  .-ms-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1);
  }

  .-ms-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1);
  }

  .-ms-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1);
  }

  .-ms-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1);
  }

  .-ms-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1);
  }

  .-ms-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1);
  }

  .-ms-1\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-ms-1\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-ms-1\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-ms-1\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .-ms-1\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .-ms-1\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .ms-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 2);
  }

  .ms-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 2);
  }

  .ms-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 2);
  }

  .ms-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 2);
  }

  .ms-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 2);
  }

  .ms-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 2);
  }

  .ms-auto:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: auto;
  }

  .ms-auto:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: auto;
  }

  .ms-auto:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: auto;
  }

  .ms-auto:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: auto;
  }

  .ms-auto:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: auto;
  }

  .ms-auto:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: auto;
  }

  .ms-px:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1px;
  }

  .ms-px:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1px;
  }

  .ms-px:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1px;
  }

  .ms-px:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1px;
  }

  .ms-px:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1px;
  }

  .ms-px:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1px;
  }

  .-me-0\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -.5);
  }

  .-me-0\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -.5);
  }

  .-me-0\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -.5);
  }

  .-me-0\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -.5);
  }

  .-me-0\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -.5);
  }

  .-me-0\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -.5);
  }

  .-me-1\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .-me-1\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .-me-1\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * -1.5);
  }

  .-me-1\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-me-1\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-me-1\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .me-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * 2);
  }

  .me-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * 2);
  }

  .me-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: calc(var(--spacing) * 2);
  }

  .me-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * 2);
  }

  .me-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * 2);
  }

  .me-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: calc(var(--spacing) * 2);
  }

  .me-auto:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: auto;
  }

  .me-auto:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: auto;
  }

  .me-auto:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: auto;
  }

  .me-auto:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: auto;
  }

  .me-auto:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: auto;
  }

  .me-auto:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: auto;
  }

  .prose {
    color: var(--tw-prose-body);
    max-width: none;
    font-size: 1rem;
    line-height: 1.75rem;
  }

  .prose :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-lead);
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    font-size: 1.25em;
    line-height: 1.6;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    list-style-type: disc;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1rem;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1rem;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1rem;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1rem;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1rem;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1rem;
  }

  .prose :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
    margin-bottom: .5em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: .375em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: .375em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: .375em;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 0;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 0;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 0;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 0;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 0;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 0;
  }

  .prose :where(.prose > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.25em;
    font-weight: 600;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
  }

  .prose :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-bold);
    font-weight: 500;
  }

  .prose :where(a strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(blockquote strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(thead th strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    list-style-type: decimal;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 1.625em;
  }

  .prose :where(ol[type="A"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="A" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="I"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="I" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="1"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: decimal;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-counters);
    font-weight: 400;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-bullets);
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-quotes);
    quotes: "“""”""‘""’";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    font-style: italic;
    font-weight: 500;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: .25rem;
    border-left-color: var(--tw-prose-quote-borders);
    padding-left: 1em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: .25rem;
    border-left-color: var(--tw-prose-quote-borders);
    padding-left: 1em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: .25rem;
    border-left-color: var(--tw-prose-quote-borders);
    padding-left: 1em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: .25rem;
    border-right-color: var(--tw-prose-quote-borders);
    padding-right: 1em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: .25rem;
    border-right-color: var(--tw-prose-quote-borders);
    padding-right: 1em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: .25rem;
    border-right-color: var(--tw-prose-quote-borders);
    padding-right: 1em;
  }

  .prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):before {
    content: open-quote;
  }

  .prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: close-quote;
  }

  .prose :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 800;
    font-size: var(--text-3xl);
    margin-top: 0;
    margin-bottom: .888889em;
    line-height: 1.11111;
  }

  .prose :where(h1 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 900;
  }

  .prose :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 2em;
    margin-bottom: 1em;
    font-size: 1.5em;
    font-weight: 600;
    line-height: 1.33333;
  }

  .prose :where(h2 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 800;
  }

  .prose :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.6em;
    margin-bottom: .6em;
    font-size: 1.25em;
    font-weight: 600;
    line-height: 1.6;
  }

  .prose :where(h3 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.5em;
    margin-bottom: .5em;
    font-weight: 600;
    line-height: 1.5;
  }

  .prose :where(h4 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
    display: block;
  }

  .prose :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .1875em;
    padding-bottom: .1875em;
    color: var(--tw-prose-kbd);
    box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);
    border-radius: .3125rem;
    font-family: inherit;
    font-size: .875em;
    font-weight: 500;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: .375em;
    padding-right: .375em;
  }

  .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border: 1px solid;
    border-color: var(--color-fd-border);
    background: var(--color-fd-muted);
    color: var(--tw-prose-code);
    border-radius: 5px;
    padding: 3px;
    font-size: 13px;
    font-weight: 400;
  }

  .prose :where(a code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(h1 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: var(--text-2xl);
  }

  .prose :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .875em;
  }

  .prose :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .9em;
  }

  .prose :where(h4 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(blockquote code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(thead th code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    table-layout: auto;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--color-fd-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-fd-border);
    width: 100%;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: .875em;
    line-height: 1.71429;
    overflow: hidden;
  }

  .prose :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
  }

  .prose :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-captions);
    margin-top: .857143em;
    font-size: .875em;
    line-height: 1.42857;
  }

  .prose :where(a:not([data-card])):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-links);
    text-underline-offset: 3.5px;
    text-decoration: underline;
    -webkit-text-decoration-color: var(--color-fd-primary);
    text-decoration-color: var(--color-fd-primary);
    font-weight: 500;
    text-decoration-thickness: 1.5px;
    transition: opacity .2s;
  }

  .prose :where(a:not([data-card]):hover):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    opacity: .8;
  }

  .prose {
    --tw-prose-body: rgba(10, 10, 10, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .prose {
      --tw-prose-body: color-mix(in oklab, var(--color-fd-foreground) 90%, transparent);
    }
  }

  .prose {
    --tw-prose-headings: var(--color-fd-foreground);
    --tw-prose-lead: var(--color-fd-foreground);
    --tw-prose-links: var(--color-fd-foreground);
    --tw-prose-bold: var(--color-fd-foreground);
    --tw-prose-counters: var(--color-fd-muted-foreground);
    --tw-prose-bullets: var(--color-fd-muted-foreground);
    --tw-prose-hr: var(--color-fd-border);
    --tw-prose-quotes: var(--color-fd-foreground);
    --tw-prose-quote-borders: var(--color-fd-border);
    --tw-prose-captions: var(--color-fd-foreground);
    --tw-prose-code: var(--color-fd-foreground);
    --tw-prose-th-borders: var(--color-fd-border);
    --tw-prose-td-borders: var(--color-fd-border);
    --tw-prose-kbd: var(--color-fd-foreground);
    --tw-prose-kbd-shadows: rgba(23, 23, 23, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .prose {
      --tw-prose-kbd-shadows: color-mix(in oklab, var(--color-fd-primary) 50%, transparent);
    }
  }

  .prose :where(.prose > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(.prose > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    text-align: start;
    padding: calc(var(--spacing) * 2.5);
    background: var(--color-fd-muted);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(th):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(th:not(tr:last-child *), td:not(tr:last-child *)):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    text-align: start;
    padding: calc(var(--spacing) * 2.5);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(td):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: 1px solid var(--color-fd-border);
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: none;
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: none;
  }

  .prose :where(tfoot th, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-top-width: 1px;
    border-top-color: var(--tw-prose-th-borders);
  }

  .prose :where(thead th, thead td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-th-borders);
  }

  .prose-no-margin > :first-child {
    margin-top: 0;
  }

  .prose-no-margin > :last-child {
    margin-bottom: 0;
  }

  .-mt-px {
    margin-top: -1px;
  }

  .mt-\(--fd-top\) {
    margin-top: var(--fd-top);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mt-px {
    margin-top: 1px;
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-auto {
    margin-bottom: auto;
  }

  .fd-scroll-container::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  .fd-scroll-container::-webkit-scrollbar-thumb {
    background: var(--color-fd-border);
    border-radius: 5px;
  }

  .fd-scroll-container::-webkit-scrollbar-track {
    background: none;
  }

  .fd-scroll-container::-webkit-scrollbar-corner {
    display: none;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .\!size-5\.5 {
    width: calc(var(--spacing) * 5.5) !important;
    height: calc(var(--spacing) * 5.5) !important;
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-4\.5 {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }

  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .size-6\.5 {
    width: calc(var(--spacing) * 6.5);
    height: calc(var(--spacing) * 6.5);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-\(--fd-animated-height\) {
    height: var(--fd-animated-height);
  }

  .h-\(--fd-height\) {
    height: var(--fd-height);
  }

  .h-\(--fd-nav-height\) {
    height: var(--fd-nav-height);
  }

  .h-\(--fd-tocnav-height\) {
    height: var(--fd-tocnav-height);
  }

  .h-\(--radix-navigation-menu-viewport-height\) {
    height: var(--radix-navigation-menu-viewport-height);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-9\.5 {
    height: calc(var(--spacing) * 9.5);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-\[64rem\] {
    height: 64rem;
  }

  .h-\[240px\] {
    height: 240px;
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: -moz-fit-content;
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }

  .max-h-\[50vh\] {
    max-height: 50vh;
  }

  .max-h-\[80svh\] {
    max-height: 80svh;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-\[460px\] {
    max-height: 460px;
  }

  .max-h-\[600px\] {
    max-height: 600px;
  }

  .max-h-\[calc\(100dvh-240px\)\] {
    max-height: calc(100dvh - 240px);
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-10 {
    min-height: calc(var(--spacing) * 10);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-\(--fd-toc-width\) {
    width: var(--fd-toc-width);
  }

  .w-\(--radix-popover-trigger-width\) {
    width: var(--radix-popover-trigger-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-0\.5 {
    width: calc(var(--spacing) * .5);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/4 {
    width: 25%;
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-\[30\%\] {
    width: 30%;
  }

  .w-\[45\%\] {
    width: 45%;
  }

  .w-\[85\%\] {
    width: 85%;
  }

  .w-\[calc\(100\%-1rem\)\] {
    width: calc(100% - 1rem);
  }

  .w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-px {
    width: 1px;
  }

  .max-w-\(--fd-page-width\) {
    max-width: var(--fd-page-width);
  }

  .max-w-\[98vw\] {
    max-width: 98vw;
  }

  .max-w-\[240px\] {
    max-width: 240px;
  }

  .max-w-\[380px\] {
    max-width: 380px;
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-\[600px\] {
    max-width: 600px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-screen-sm {
    max-width: var(--breakpoint-sm);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[220px\] {
    min-width: 220px;
  }

  .min-w-\[240px\] {
    min-width: 240px;
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-auto {
    flex: auto;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow-0 {
    flex-grow: 0;
  }

  .origin-\(--radix-hover-card-content-transform-origin\) {
    transform-origin: var(--radix-hover-card-content-transform-origin);
  }

  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .origin-\[top_center\] {
    transform-origin: top;
  }

  .-translate-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\(--fd-sidebar-offset\) {
    --tw-translate-x: var(--fd-sidebar-offset);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-full {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-rotate-90 {
    rotate: -90deg;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-in {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .resize-none {
    resize: none;
  }

  .scroll-m-20 {
    scroll-margin: calc(var(--spacing) * 20);
  }

  .scroll-m-24 {
    scroll-margin: calc(var(--spacing) * 24);
  }

  .scroll-m-28 {
    scroll-margin: calc(var(--spacing) * 28);
  }

  .list-none {
    list-style-type: none;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-row-reverse {
    flex-direction: row-reverse;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-3\.5 {
    gap: calc(var(--spacing) * 3.5);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-24 {
    gap: calc(var(--spacing) * 24);
  }

  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }

  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-fd-border > :not(:last-child)) {
    border-color: var(--color-fd-border);
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-bl-lg {
    border-bottom-left-radius: var(--radius-lg);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-x {
    border-inline-style: var(--tw-border-style);
    border-left-width: 1px;
    border-right-width: 1px;
  }

  .border-s:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-s:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-s:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-s:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-s:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-s:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-s-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-s-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-s-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-s-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-s-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-s-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-e:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-e:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-e:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-e:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-e:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-e:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-e-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-e-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-e-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-e-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-e-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-e-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-border {
    border-color: var(--color-border);
  }

  .border-fd-foreground\/10 {
    border-color: rgba(10, 10, 10, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-fd-foreground\/10 {
      border-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }

  .border-fd-foreground\/20 {
    border-color: rgba(10, 10, 10, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-fd-foreground\/20 {
      border-color: color-mix(in oklab, var(--color-fd-foreground) 20%, transparent);
    }
  }

  .border-fd-primary\/20 {
    border-color: rgba(23, 23, 23, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-fd-primary\/20 {
      border-color: color-mix(in oklab, var(--color-fd-primary) 20%, transparent);
    }
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-s-amber-500\/50:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(249, 156, 0, .5);
  }

  .border-s-amber-500\/50:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(249, 156, 0, .5);
  }

  .border-s-amber-500\/50:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(249, 156, 0, .5);
  }

  .border-s-amber-500\/50:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(249, 156, 0, .5);
  }

  .border-s-amber-500\/50:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(249, 156, 0, .5);
  }

  .border-s-amber-500\/50:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(249, 156, 0, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .border-s-blue-500\/50:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(48, 128, 255, .5);
  }

  .border-s-blue-500\/50:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(48, 128, 255, .5);
  }

  .border-s-blue-500\/50:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(48, 128, 255, .5);
  }

  .border-s-blue-500\/50:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(48, 128, 255, .5);
  }

  .border-s-blue-500\/50:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(48, 128, 255, .5);
  }

  .border-s-blue-500\/50:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(48, 128, 255, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .border-s-green-500\/50:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(0, 199, 88, .5);
  }

  .border-s-green-500\/50:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(0, 199, 88, .5);
  }

  .border-s-green-500\/50:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(0, 199, 88, .5);
  }

  .border-s-green-500\/50:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(0, 199, 88, .5);
  }

  .border-s-green-500\/50:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(0, 199, 88, .5);
  }

  .border-s-green-500\/50:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(0, 199, 88, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .border-s-red-500\/50:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(251, 44, 54, .5);
  }

  .border-s-red-500\/50:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(251, 44, 54, .5);
  }

  .border-s-red-500\/50:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-color: rgba(251, 44, 54, .5);
  }

  .border-s-red-500\/50:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(251, 44, 54, .5);
  }

  .border-s-red-500\/50:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(251, 44, 54, .5);
  }

  .border-s-red-500\/50:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-color: rgba(251, 44, 54, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .bg-\(--callout-color\)\/50 {
    background-color: var(--callout-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\(--callout-color\)\/50 {
      background-color: color-mix(in oklab, var(--callout-color) 50%, transparent);
    }
  }

  .bg-\(--shiki-light-bg\) {
    background-color: var(--shiki-light-bg);
  }

  .bg-accent {
    background-color: var(--color-accent);
  }

  .bg-background {
    background-color: var(--color-background);
  }

  .bg-black\/30 {
    background-color: rgba(0, 0, 0, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/30 {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: rgba(0, 0, 0, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-destructive {
    background-color: var(--color-destructive);
  }

  .bg-fd-accent {
    background-color: var(--color-fd-accent);
  }

  .bg-fd-accent\/30 {
    background-color: rgba(209, 209, 209, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-accent\/30 {
      background-color: color-mix(in oklab, var(--color-fd-accent) 30%, transparent);
    }
  }

  .bg-fd-background {
    background-color: var(--color-fd-background);
  }

  .bg-fd-background\/50 {
    background-color: rgba(245, 245, 245, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-background\/50 {
      background-color: color-mix(in oklab, var(--color-fd-background) 50%, transparent);
    }
  }

  .bg-fd-background\/80 {
    background-color: rgba(245, 245, 245, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-background\/80 {
      background-color: color-mix(in oklab, var(--color-fd-background) 80%, transparent);
    }
  }

  .bg-fd-border {
    background-color: var(--color-fd-border);
  }

  .bg-fd-card {
    background-color: var(--color-fd-card);
  }

  .bg-fd-foreground\/10 {
    background-color: rgba(10, 10, 10, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-foreground\/10 {
      background-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }

  .bg-fd-muted {
    background-color: var(--color-fd-muted);
  }

  .bg-fd-popover {
    background-color: var(--color-fd-popover);
  }

  .bg-fd-popover\/60 {
    background-color: rgba(250, 250, 250, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-popover\/60 {
      background-color: color-mix(in oklab, var(--color-fd-popover) 60%, transparent);
    }
  }

  .bg-fd-popover\/80 {
    background-color: rgba(250, 250, 250, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-popover\/80 {
      background-color: color-mix(in oklab, var(--color-fd-popover) 80%, transparent);
    }
  }

  .bg-fd-primary {
    background-color: var(--color-fd-primary);
  }

  .bg-fd-primary\/10 {
    background-color: rgba(23, 23, 23, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-primary\/10 {
      background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);
    }
  }

  .bg-fd-secondary {
    background-color: var(--color-fd-secondary);
  }

  .bg-fd-secondary\/50 {
    background-color: rgba(237, 237, 237, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-fd-secondary\/50 {
      background-color: color-mix(in oklab, var(--color-fd-secondary) 50%, transparent);
    }
  }

  .bg-popover {
    background-color: var(--color-popover);
  }

  .bg-primary {
    background-color: var(--color-primary);
  }

  .bg-secondary {
    background-color: var(--color-secondary);
  }

  .bg-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .bg-linear-to-b {
    --tw-gradient-position: to bottom;
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-b {
      --tw-gradient-position: to bottom in oklab;
    }
  }

  .bg-linear-to-b {
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-muted {
    --tw-gradient-from: var(--color-muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-secondary {
    --tw-gradient-to: var(--color-secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .\[mask-image\:linear-gradient\(to_bottom\,transparent\,white_16px\,white_calc\(100\%-16px\)\,transparent\)\] {
    -webkit-mask-image: linear-gradient(rgba(0, 0, 0, 0), #fff 16px, #fff calc(100% - 16px), rgba(0, 0, 0, 0));
    mask-image: linear-gradient(rgba(0, 0, 0, 0), #fff 16px, #fff calc(100% - 16px), rgba(0, 0, 0, 0));
  }

  .fill-\(--callout-color\) {
    fill: var(--callout-color);
  }

  .fill-amber-500 {
    fill: var(--color-amber-500);
  }

  .fill-blue-500 {
    fill: var(--color-blue-500);
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-fd-primary {
    fill: var(--color-fd-primary);
  }

  .fill-green-500 {
    fill: var(--color-green-500);
  }

  .fill-primary {
    fill: var(--color-primary);
  }

  .fill-red-500 {
    fill: var(--color-red-500);
  }

  .stroke-current\/25 {
    stroke: currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .stroke-current\/25 {
      stroke: color-mix(in oklab, currentcolor 25%, transparent);
    }
  }

  .stroke-fd-foreground\/10 {
    stroke: rgba(10, 10, 10, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .stroke-fd-foreground\/10 {
      stroke: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\.5 {
    padding: calc(var(--spacing) * .5);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .\!px-1 {
    padding-inline: calc(var(--spacing) * 1) !important;
  }

  .px-\(--fd-layout-offset\) {
    padding-inline: var(--fd-layout-offset);
  }

  .px-0\.5 {
    padding-inline: calc(var(--spacing) * .5);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .ps-\(--sidebar-item-offset\):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--sidebar-item-offset);
  }

  .ps-\(--sidebar-item-offset\):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--sidebar-item-offset);
  }

  .ps-\(--sidebar-item-offset\):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--sidebar-item-offset);
  }

  .ps-\(--sidebar-item-offset\):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--sidebar-item-offset);
  }

  .ps-\(--sidebar-item-offset\):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--sidebar-item-offset);
  }

  .ps-\(--sidebar-item-offset\):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--sidebar-item-offset);
  }

  .ps-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 1);
  }

  .ps-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 1);
  }

  .ps-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 1);
  }

  .ps-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 1);
  }

  .ps-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 1);
  }

  .ps-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 1);
  }

  .ps-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2);
  }

  .ps-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2);
  }

  .ps-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2);
  }

  .ps-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2);
  }

  .ps-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2);
  }

  .ps-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2);
  }

  .ps-2\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .ps-2\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .ps-2\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .ps-2\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .ps-2\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .ps-2\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .ps-3:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 3);
  }

  .ps-3:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 3);
  }

  .ps-3:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 3);
  }

  .ps-3:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 3);
  }

  .ps-3:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 3);
  }

  .ps-3:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 3);
  }

  .ps-4:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4);
  }

  .ps-4:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4);
  }

  .ps-4:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4);
  }

  .ps-4:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4);
  }

  .ps-4:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4);
  }

  .ps-4:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4);
  }

  .ps-4\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4.5);
  }

  .ps-4\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4.5);
  }

  .ps-4\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 4.5);
  }

  .ps-4\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4.5);
  }

  .ps-4\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4.5);
  }

  .ps-4\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 4.5);
  }

  .ps-6:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 6);
  }

  .ps-6:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 6);
  }

  .ps-6:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 6);
  }

  .ps-6:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 6);
  }

  .ps-6:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 6);
  }

  .ps-6:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 6);
  }

  .ps-8:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 8);
  }

  .ps-8:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 8);
  }

  .ps-8:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--spacing) * 8);
  }

  .ps-8:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 8);
  }

  .ps-8:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 8);
  }

  .ps-8:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--spacing) * 8);
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: calc(var(--fd-layout-offset)  + var(--fd-sidebar-width));
  }

  .pe-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2);
  }

  .pe-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2);
  }

  .pe-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2);
  }

  .pe-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2);
  }

  .pe-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2);
  }

  .pe-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2);
  }

  .pe-2\.5:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pe-2\.5:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pe-2\.5:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pe-2\.5:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .pe-2\.5:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .pe-2\.5:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .pe-4:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 4);
  }

  .pe-4:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 4);
  }

  .pe-4:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-right: calc(var(--spacing) * 4);
  }

  .pe-4:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 4);
  }

  .pe-4:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 4);
  }

  .pe-4:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-left: calc(var(--spacing) * 4);
  }

  .pt-\(--fd-nav-height\) {
    padding-top: var(--fd-nav-height);
  }

  .pt-\(--fd-tocnav-height\) {
    padding-top: var(--fd-tocnav-height);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-2\.5 {
    padding-top: calc(var(--spacing) * 2.5);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-12 {
    padding-top: calc(var(--spacing) * 12);
  }

  .pt-14 {
    padding-top: calc(var(--spacing) * 14);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pl-48 {
    padding-left: calc(var(--spacing) * 48);
  }

  .text-center {
    text-align: center;
  }

  .text-end {
    text-align: end;
  }

  .text-left {
    text-align: left;
  }

  .text-start {
    text-align: start;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[13px\] {
    font-size: 13px;
  }

  .text-\[15px\] {
    font-size: 15px;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .\[overflow-wrap\:anywhere\] {
    overflow-wrap: anywhere;
  }

  .break-all {
    word-break: break-all;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-\(--tab-color\) {
    color: var(--tab-color);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-fd-accent-foreground {
    color: var(--color-fd-accent-foreground);
  }

  .text-fd-card {
    color: var(--color-fd-card);
  }

  .text-fd-card-foreground {
    color: var(--color-fd-card-foreground);
  }

  .text-fd-foreground {
    color: var(--color-fd-foreground);
  }

  .text-fd-foreground\/80 {
    color: rgba(10, 10, 10, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-fd-foreground\/80 {
      color: color-mix(in oklab, var(--color-fd-foreground) 80%, transparent);
    }
  }

  .text-fd-muted-foreground {
    color: var(--color-fd-muted-foreground);
  }

  .text-fd-muted-foreground\/50 {
    color: rgba(115, 115, 115, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-fd-muted-foreground\/50 {
      color: color-mix(in oklab, var(--color-fd-muted-foreground) 50%, transparent);
    }
  }

  .text-fd-muted-foreground\/70 {
    color: rgba(115, 115, 115, .7);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-fd-muted-foreground\/70 {
      color: color-mix(in oklab, var(--color-fd-muted-foreground) 70%, transparent);
    }
  }

  .text-fd-popover-foreground {
    color: var(--color-fd-popover-foreground);
  }

  .text-fd-popover-foreground\/80 {
    color: rgba(39, 39, 39, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-fd-popover-foreground\/80 {
      color: color-mix(in oklab, var(--color-fd-popover-foreground) 80%, transparent);
    }
  }

  .text-fd-primary {
    color: var(--color-fd-primary);
  }

  .text-fd-primary-foreground {
    color: var(--color-fd-primary-foreground);
  }

  .text-fd-primary\/50 {
    color: rgba(23, 23, 23, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-fd-primary\/50 {
      color: color-mix(in oklab, var(--color-fd-primary) 50%, transparent);
    }
  }

  .text-fd-secondary-foreground {
    color: var(--color-fd-secondary-foreground);
  }

  .text-foreground {
    color: var(--color-foreground);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-muted-foreground {
    color: var(--color-muted-foreground);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-popover-foreground {
    color: var(--color-popover-foreground);
  }

  .text-primary {
    color: var(--color-primary);
  }

  .text-primary-foreground {
    color: var(--color-primary-foreground);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-red-400\/80 {
    color: rgba(255, 101, 104, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-red-400\/80 {
      color: color-mix(in oklab, var(--color-red-400) 80%, transparent);
    }
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-secondary-foreground {
    color: var(--color-secondary-foreground);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .line-through {
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }

  .underline {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-70 {
    opacity: .7;
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 rgba(0, 0, 0, 0);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgba(0, 0, 0, .05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-black\/50 {
    --tw-shadow-color: rgba(0, 0, 0, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-black\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-fd-primary\/30 {
    --tw-shadow-color: rgba(23, 23, 23, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-fd-primary\/30 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-fd-primary) 30%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-border {
    --tw-ring-color: var(--color-border);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--color-background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xs {
    --tw-backdrop-blur: blur(var(--blur-xs));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[height\] {
    transition-property: height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[padding\] {
    transition-property: padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\] {
    transition-property: width, height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-400 {
    --tw-duration: .4s;
    transition-duration: .4s;
  }

  .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .\[--fd-nav-height\:0px\] {
    --fd-nav-height: 0px;
  }

  .\[--fd-nav-height\:56px\] {
    --fd-nav-height: 56px;
  }

  .\[scrollbar-width\:none\] {
    scrollbar-width: none;
  }

  .running {
    animation-play-state: running;
  }

  :is(.\*\:col-start-1 > *) {
    grid-column-start: 1;
  }

  :is(.\*\:row-start-1 > *) {
    grid-row-start: 1;
  }

  :is(.\*\:mx-auto > *) {
    margin-left: auto;
    margin-right: auto;
  }

  :is(.\*\:my-auto > *) {
    margin-top: auto;
    margin-bottom: auto;
  }

  :is(.\*\:flex > *) {
    display: flex;
  }

  :is(.\*\:w-\(--fd-sidebar-width\) > *) {
    width: var(--fd-sidebar-width);
  }

  :is(.\*\:max-w-fd-container > *) {
    max-width: var(--spacing-fd-container);
  }

  :is(.\*\:flex-col > *) {
    flex-direction: column;
  }

  :is(.\*\:border-b > *) {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  :is(.\*\:bg-fd-card > *) {
    background-color: var(--color-fd-card);
  }

  .group-data-\[state\=active\]\:bg-fd-primary:is(:where(.group)[data-state="active"] *) {
    background-color: var(--color-fd-primary);
  }

  .group-data-\[state\=open\]\:rotate-90:is(:where(.group)[data-state="open"] *) {
    rotate: 90deg;
  }

  .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }

  .group-data-\[state\=open\]\/accordion\:rotate-90:is(:where(.group\/accordion)[data-state="open"] *) {
    rotate: 90deg;
  }

  @media (hover: hover) {
    .peer-hover\:opacity-100:is(:where(.peer):hover ~ *) {
      opacity: 1;
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
    opacity: .7;
  }

  .placeholder\:text-fd-muted-foreground::placeholder {
    color: var(--color-fd-muted-foreground);
  }

  .first\:ms-1:first-child:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 1);
  }

  .first\:ms-1:first-child:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 1);
  }

  .first\:ms-1:first-child:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(var(--spacing) * 1);
  }

  .first\:ms-1:first-child:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 1);
  }

  .first\:ms-1:first-child:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 1);
  }

  .first\:ms-1:first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(var(--spacing) * 1);
  }

  .first\:rounded-tr-xl:first-child {
    border-top-right-radius: var(--radius-xl);
  }

  .first\:border-t-0:first-child {
    border-top-style: var(--tw-border-style);
    border-top-width: 0;
  }

  .first\:pt-0:first-child {
    padding-top: calc(var(--spacing) * 0);
  }

  .last\:mb-0:last-child {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .last\:rounded-b-xl:last-child {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }

  .last\:border-b:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .last\:pb-0:last-child {
    padding-bottom: calc(var(--spacing) * 0);
  }

  :is(.\*\:last\:border-b-0 > *):last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .empty\:mb-0:empty {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .empty\:hidden:empty {
    display: none;
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--color-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--color-destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-fd-accent:hover {
      background-color: var(--color-fd-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-fd-accent\/50:hover {
      background-color: rgba(209, 209, 209, .25);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-fd-accent\/50:hover {
        background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-fd-accent\/80:hover {
      background-color: rgba(209, 209, 209, .4);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-fd-accent\/80:hover {
        background-color: color-mix(in oklab, var(--color-fd-accent) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-fd-primary\/80:hover {
      background-color: rgba(23, 23, 23, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-fd-primary\/80:hover {
        background-color: color-mix(in oklab, var(--color-fd-primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: rgba(23, 23, 23, .9);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: rgba(237, 237, 237, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--color-secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--color-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-fd-accent-foreground:hover {
      color: var(--color-fd-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-fd-accent-foreground\/80:hover {
      color: rgba(23, 23, 23, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-fd-accent-foreground\/80:hover {
        color: color-mix(in oklab, var(--color-fd-accent-foreground) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-fd-popover-foreground\/50:hover {
      color: rgba(39, 39, 39, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-fd-popover-foreground\/50:hover {
        color: color-mix(in oklab, var(--color-fd-popover-foreground) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-primary\/80:hover {
      color: rgba(23, 23, 23, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-primary\/80:hover {
        color: color-mix(in oklab, var(--color-primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-80:hover {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:transition-none:hover {
      transition-property: none;
    }
  }

  .focus\:bg-fd-accent:focus {
    background-color: var(--color-fd-accent);
  }

  .focus\:text-fd-accent-foreground:focus {
    color: var(--color-fd-accent-foreground);
  }

  .focus\:ring:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-fd-ring:focus {
    --tw-ring-color: var(--color-fd-ring);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--color-ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--color-ring);
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--color-destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-fd-ring:focus-visible {
    --tw-ring-color: var(--color-fd-ring);
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: rgba(163, 163, 163, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
    }
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-focus-visible\:bg-fd-accent:has(:focus-visible) {
    background-color: var(--color-fd-accent);
  }

  :is(.\*\:has-\[\+\:last-child\[data-empty\=true\]\]\:border-b-0 > *):has( + :last-child[data-empty="true"]) {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--color-destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--color-destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:text-fd-primary[data-active="true"] {
    color: var(--color-fd-primary);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:absolute *)[data-active="true"]:before {
    content: var(--tw-content);
    position: absolute;
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:inset-y-2\.5 *)[data-active="true"]:before {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 2.5);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:before {
    content: var(--tw-content);
  }

  :-webkit-any(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * 2.5);
  }

  :-moz-any(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * 2.5);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: calc(var(--spacing) * 2.5);
  }

  :-webkit-any(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * 2.5);
  }

  :-moz-any(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * 2.5);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:start-2\.5 *)[data-active="true"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: calc(var(--spacing) * 2.5);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:w-px *)[data-active="true"]:before {
    content: var(--tw-content);
    width: 1px;
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:bg-fd-primary *)[data-active="true"]:before {
    content: var(--tw-content);
    background-color: var(--color-fd-primary);
  }

  :is(.\*\*\:data-\[active\=true\]\:before\:content-\[\'\'\] *)[data-active="true"]:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .data-\[collapsed\=false\]\:hidden[data-collapsed="false"] {
    display: none;
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  :is(.\*\:data-\[empty\=true\]\:border-b-0 > *)[data-empty="true"] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .data-\[motion\=from-end\]\:animate-fd-enterFromRight[data-motion="from-end"] {
    animation: var(--animate-fd-enterFromRight);
  }

  .data-\[motion\=from-start\]\:animate-fd-enterFromLeft[data-motion="from-start"] {
    animation: var(--animate-fd-enterFromLeft);
  }

  .data-\[motion\=to-end\]\:animate-fd-exitToRight[data-motion="to-end"] {
    animation: var(--animate-fd-exitToRight);
  }

  .data-\[motion\=to-start\]\:animate-fd-exitToLeft[data-motion="to-start"] {
    animation: var(--animate-fd-exitToLeft);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[state\=active\]\:border-fd-primary[data-state="active"] {
    border-color: var(--color-fd-primary);
  }

  .data-\[state\=active\]\:text-fd-primary[data-state="active"] {
    color: var(--color-fd-primary);
  }

  .data-\[state\=closed\]\:animate-fd-accordion-up[data-state="closed"] {
    animation: var(--animate-fd-accordion-up);
  }

  .data-\[state\=closed\]\:animate-fd-collapsible-up[data-state="closed"] {
    animation: var(--animate-fd-collapsible-up);
  }

  .data-\[state\=closed\]\:animate-fd-dialog-out[data-state="closed"] {
    animation: var(--animate-fd-dialog-out);
  }

  .data-\[state\=closed\]\:animate-fd-fade-out[data-state="closed"] {
    animation: var(--animate-fd-fade-out);
  }

  .data-\[state\=closed\]\:animate-fd-nav-menu-out[data-state="closed"] {
    animation: var(--animate-fd-nav-menu-out);
  }

  .data-\[state\=closed\]\:animate-fd-popover-out[data-state="closed"] {
    animation: var(--animate-fd-popover-out);
  }

  .data-\[state\=closed\]\:animate-fd-sidebar-out[data-state="closed"] {
    animation: var(--animate-fd-sidebar-out);
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=hidden\]\:animate-fd-fade-out[data-state="hidden"] {
    animation: var(--animate-fd-fade-out);
  }

  .data-\[state\=inactive\]\:hidden[data-state="inactive"] {
    display: none;
  }

  .data-\[state\=open\]\:animate-fd-accordion-down[data-state="open"] {
    animation: var(--animate-fd-accordion-down);
  }

  .data-\[state\=open\]\:animate-fd-collapsible-down[data-state="open"] {
    animation: var(--animate-fd-collapsible-down);
  }

  .data-\[state\=open\]\:animate-fd-dialog-in[data-state="open"] {
    animation: var(--animate-fd-dialog-in);
  }

  .data-\[state\=open\]\:animate-fd-fade-in[data-state="open"] {
    animation: var(--animate-fd-fade-in);
  }

  .data-\[state\=open\]\:animate-fd-nav-menu-in[data-state="open"] {
    animation: var(--animate-fd-nav-menu-in);
  }

  .data-\[state\=open\]\:animate-fd-popover-in[data-state="open"] {
    animation: var(--animate-fd-popover-in);
  }

  .data-\[state\=open\]\:animate-fd-sidebar-in[data-state="open"] {
    animation: var(--animate-fd-sidebar-in);
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:rounded-b-none[data-state="open"] {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--color-accent);
  }

  .data-\[state\=open\]\:bg-fd-accent[data-state="open"] {
    background-color: var(--color-fd-accent);
  }

  .data-\[state\=open\]\:bg-fd-accent\/50[data-state="open"] {
    background-color: rgba(209, 209, 209, .25);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-fd-accent\/50[data-state="open"] {
      background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);
    }
  }

  .data-\[state\=open\]\:text-fd-accent-foreground[data-state="open"] {
    color: var(--color-fd-accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--color-muted-foreground);
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media not (min-width: 80rem) {
    
  }

  @media not (min-width: 80rem) {
    .max-xl\:hidden {
      display: none;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:hidden {
      display: none;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:rounded-b-2xl {
      border-bottom-right-radius: var(--radius-2xl);
      border-bottom-left-radius: var(--radius-2xl);
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:shadow-lg {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:top-12 {
      top: calc(var(--spacing) * 12);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:hidden {
      display: none;
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:rounded-md {
      border-radius: var(--radius-md);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:border {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:bg-\(--tab-color\)\/10 {
      background-color: var(--tab-color);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .max-md\:bg-\(--tab-color\)\/10 {
        background-color: color-mix(in oklab, var(--tab-color) 10%, transparent);
      }
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:bg-fd-secondary {
      background-color: var(--color-fd-secondary);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:p-1\.5 {
      padding: calc(var(--spacing) * 1.5);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:backdrop-blur-xs {
      --tw-backdrop-blur: blur(var(--blur-xs));
      -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
      backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:mt-2 {
      margin-top: calc(var(--spacing) * 2);
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mr-6 {
      margin-right: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-end {
      align-items: flex-end;
    }
  }

  @media (min-width: 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (min-width: 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 40rem) {
    .sm\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:pr-6 {
      padding-right: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-start {
      text-align: start;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\(--fd-api-info-top\) {
      top: var(--fd-api-info-top);
    }
  }

  @media (min-width: 48rem) {
    .md\:top-\[calc\(50\%-250px\)\] {
      top: calc(50% - 250px);
    }
  }

  @media (min-width: 48rem) {
    .md\:bottom-12 {
      bottom: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:mx-auto {
      margin-left: auto;
      margin-right: auto;
    }
  }

  @media (min-width: 48rem) {
    .md\:mt-1 {
      margin-top: calc(var(--spacing) * 1);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-auto {
      margin-bottom: auto;
    }
  }

  @media (min-width: 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (min-width: 48rem) {
    .md\:size-5 {
      width: calc(var(--spacing) * 5);
      height: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:\[--fd-nav-height\:0px\] {
      --fd-nav-height: 0px;
    }
  }

  @media (min-width: 48rem) {
    .md\:\[--fd-nav-height\:64px\] {
      --fd-nav-height: 64px;
    }
  }

  @media (min-width: 48rem) {
    .md\:\[--fd-sidebar-width\:268px\] {
      --fd-sidebar-width: 268px;
    }
  }

  @media (min-width: 48rem) {
    .md\:\[--fd-sidebar-width\:286px\] {
      --fd-sidebar-width: 286px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[160px\] {
      width: 160px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 64rem) {
    .lg\:\[--fd-nav-height\:104px\] {
      --fd-nav-height: 104px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:\[--fd-sidebar-width\:286px\] {
      --fd-sidebar-width: 286px;
    }
  }

  @media (min-width: 80rem) {
    
  }

  @media (min-width: 80rem) {
    .xl\:hidden {
      display: none;
    }
  }

  @media (min-width: 80rem) {
    .xl\:w-\[400px\] {
      width: 400px;
    }
  }

  @media (min-width: 80rem) {
    .xl\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 80rem) {
    .xl\:items-start {
      align-items: flex-start;
    }
  }

  @media (min-width: 80rem) {
    .xl\:px-12 {
      padding-inline: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 80rem) {
    .xl\:pt-12 {
      padding-top: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 80rem) {
    .xl\:\[--fd-toc-width\:286px\] {
      --fd-toc-width: 286px;
    }
  }

  @container (width < 32rem) {
    .\@max-lg\:col-span-full {
      grid-column: 1 / -1;
    }
  }

  @container (width >= 28rem) {
    .\@md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  .rtl\:-translate-x-\(--fd-sidebar-offset\):where(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rtl\:-translate-x-\(--fd-sidebar-offset\):where(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rtl\:-translate-x-\(--fd-sidebar-offset\):where(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rtl\:-scale-x-100:where(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-scale-x: calc(100% * -1);
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rtl\:-scale-x-100:where(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-scale-x: calc(100% * -1);
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rtl\:-scale-x-100:where(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    --tw-scale-x: calc(100% * -1);
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rtl\:rotate-180:where(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    rotate: 180deg;
  }

  .rtl\:rotate-180:where(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    rotate: 180deg;
  }

  .rtl\:rotate-180:where(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    rotate: 180deg;
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--color-input);
  }

  .dark\:bg-\(--shiki-dark-bg\):is(.dark *) {
    background-color: var(--shiki-dark-bg);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--color-destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-destructive) 60%, transparent);
    }
  }

  .dark\:bg-fd-secondary\/50:is(.dark *) {
    background-color: rgba(237, 237, 237, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-fd-secondary\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-fd-secondary) 50%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: rgba(158, 158, 158, .06);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
    }
  }

  .dark\:bg-neutral-950:is(.dark *) {
    background-color: var(--color-neutral-950);
  }

  .dark\:text-blue-400:is(.dark *) {
    color: var(--color-blue-400);
  }

  .dark\:text-green-400:is(.dark *) {
    color: var(--color-green-400);
  }

  .dark\:text-orange-400:is(.dark *) {
    color: var(--color-orange-400);
  }

  .dark\:text-red-400:is(.dark *) {
    color: var(--color-red-400);
  }

  .dark\:text-yellow-400:is(.dark *) {
    color: var(--color-yellow-400);
  }

  .dark\:\[--color-fd-background\:var\(--color-neutral-950\)\]:is(.dark *) {
    --color-fd-background: var(--color-neutral-950);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: rgba(209, 209, 209, .25);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: rgba(158, 158, 158, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--color-destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--color-destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:size-3\.5 svg {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&_svg\]\:size-4 svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\]\:size-4\.5 svg {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }

  .\[\&_svg\]\:size-5 svg {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .\[\&_svg\]\:size-full svg {
    width: 100%;
    height: 100%;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\]\:text-fd-muted-foreground svg {
    color: var(--color-fd-muted-foreground);
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>figure\:only-child\]\:-m-4 > figure:only-child {
    margin: calc(var(--spacing) * -4);
  }

  .\[\&\>figure\:only-child\]\:border-none > figure:only-child {
    --tw-border-style: none;
    border-style: none;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--color-accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--color-destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: rgba(23, 23, 23, .9);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: rgba(237, 237, 237, .9);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--color-secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--color-accent-foreground);
    }
  }

  a[data-active="true"] .\[a\[data-active\=true\]_\&\]\:from-primary\/60 {
    --tw-gradient-from: rgba(23, 23, 23, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    a[data-active="true"] .\[a\[data-active\=true\]_\&\]\:from-primary\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--color-primary) 60%, transparent);
    }
  }

  a[data-active="true"] .\[a\[data-active\=true\]_\&\]\:from-primary\/60 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  a[data-active="true"] .\[a\[data-active\=true\]_\&\]\:to-primary {
    --tw-gradient-to: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  a[data-active="true"] .\[a\[data-active\=true\]_\&\]\:text-primary-foreground {
    color: var(--color-primary-foreground);
  }
}

.dark {
  --color-fd-background: #121212;
  --color-fd-foreground: #ebebeb;
  --color-fd-muted: #212121;
  --color-fd-muted-foreground: rgba(179, 179, 179, .8);
  --color-fd-popover: #1e1e1e;
  --color-fd-popover-foreground: #dedede;
  --color-fd-card: #191919;
  --color-fd-card-foreground: #fafafa;
  --color-fd-border: rgba(102, 102, 102, .2);
  --color-fd-primary: #fafafa;
  --color-fd-primary-foreground: #171717;
  --color-fd-secondary: #212121;
  --color-fd-secondary-foreground: #ebebeb;
  --color-fd-accent: rgba(104, 104, 104, .3);
  --color-fd-accent-foreground: #e6e6e6;
  --color-fd-ring: #8c8c8c;
}

.dark #nd-sidebar {
  --color-fd-muted: #292929;
  --color-fd-secondary: #2e2e2e;
  --color-fd-muted-foreground: #b8b8b8;
}

.shiki:not(.not-fumadocs-codeblock *) {
  --padding-left: calc(var(--spacing) * 3);
  --padding-right: calc(var(--spacing) * 3);
}

.shiki:not(.not-fumadocs-codeblock *) code span {
  color: var(--shiki-light);
}

.shiki:not(.not-fumadocs-codeblock *) code .line {
  min-height: 1lh;
  padding-left: var(--padding-left);
  padding-right: var(--padding-right);
  position: relative;
}

.shiki:not(.not-fumadocs-codeblock *).has-focused code .line:not(.focused) {
  filter: blur(2px);
  transition: filter .2s;
}

.shiki:not(.not-fumadocs-codeblock *).has-focused:hover code .line:not(.focused) {
  filter: blur();
}

.shiki:not(.not-fumadocs-codeblock *)[data-line-numbers] code .twoslash-meta-line {
  padding-left: calc(var(--padding-left)  + 7 * var(--spacing));
}

.shiki:not(.not-fumadocs-codeblock *)[data-line-numbers] code .line {
  counter-increment: line;
  padding-left: calc(var(--padding-left)  + 7 * var(--spacing));
}

.shiki:not(.not-fumadocs-codeblock *)[data-line-numbers] code .line:after {
  content: counter(line);
  color: var(--fd-counter-color, #737373);
  position: absolute;
}

@supports (color: color-mix(in lab, red, red)) {
  .shiki:not(.not-fumadocs-codeblock *)[data-line-numbers] code .line:after {
    color: color-mix(in oklab, var(--fd-counter-color, var(--color-fd-muted-foreground)) 60%, transparent);
  }
}

.shiki:not(.not-fumadocs-codeblock *)[data-line-numbers] code .line:after {
  top: calc(var(--spacing) * 0);
  left: calc(var(--spacing) * 4);
}

.shiki:not(.not-fumadocs-codeblock *) code .diff:before {
  left: var(--spacing);
  position: absolute;
}

.shiki:not(.not-fumadocs-codeblock *) code .diff.remove {
  opacity: .7;
  --fd-counter-color: var(--color-fd-diff-remove-symbol);
  background-color: var(--color-fd-diff-remove);
}

.shiki:not(.not-fumadocs-codeblock *) code .diff.remove:before {
  content: "-";
  color: var(--color-fd-diff-remove-symbol);
}

.shiki:not(.not-fumadocs-codeblock *) code .diff.add {
  --fd-counter-color: var(--color-fd-diff-add-symbol);
  background-color: var(--color-fd-diff-add);
}

.shiki:not(.not-fumadocs-codeblock *) code .diff.add:before {
  content: "+";
  color: var(--color-fd-diff-add-symbol);
}

.shiki:not(.not-fumadocs-codeblock *) code .highlighted {
  --fd-counter-color: var(--color-fd-primary);
  background-color: rgba(23, 23, 23, .1);
}

@supports (color: color-mix(in lab, red, red)) {
  .shiki:not(.not-fumadocs-codeblock *) code .highlighted {
    background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);
  }
}

.shiki:not(.not-fumadocs-codeblock *) code .highlighted-word {
  border-radius: var(--radius-md);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: rgba(23, 23, 23, .3);
  margin-top: -1px;
  margin-bottom: -1px;
  padding: 1px;
}

@supports (color: color-mix(in lab, red, red)) {
  .shiki:not(.not-fumadocs-codeblock *) code .highlighted-word {
    border-color: color-mix(in oklab, var(--color-fd-primary) 30%, transparent);
  }
}

.shiki:not(.not-fumadocs-codeblock *) code .highlighted-word {
  background-color: rgba(23, 23, 23, .1);
}

@supports (color: color-mix(in lab, red, red)) {
  .shiki:not(.not-fumadocs-codeblock *) code .highlighted-word {
    background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);
  }
}

.shiki:not(.not-fumadocs-codeblock *) code .highlighted-word {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.dark .shiki:not(.not-fumadocs-codeblock *) code span {
  color: var(--shiki-dark);
}

[dir="rtl"] {
  --fd-sidebar-mobile-offset: -100%;
}

@media not (min-width: 80rem) {
  #nd-docs-layout:has([data-toc-popover]) {
    --fd-tocnav-height: calc(var(--spacing) * 10);
  }
}

@property --radix-collapsible-content-height {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

.twoslash-meta-line {
  display: flex;
}

.twoslash-completion-list, .twoslash-popup-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: normal;
  border: 1px solid var(--color-fd-border);
  z-index: 8;
  width: 20rem;
  color: var(--color-fd-popover-foreground);
  background-color: var(--color-fd-popover);
  border-radius: 6px;
  flex-direction: column;
  margin-top: 8px;
  margin-right: -20rem;
  padding: 8px;
  display: flex;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
}

.twoslash-completion-cursor {
  flex-direction: column;
  display: inline-flex;
}

.twoslash-completion-list:hover {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  user-select: auto;
}

.twoslash-popup-arrow {
  pointer-events: none;
  border-top-width: 1px;
  border-right-width: 1px;
  border-color: var(--color-fd-primary);
  background-color: var(--color-fd-popover);
  width: 6px;
  height: 6px;
  position: absolute;
  top: -4px;
  left: 1em;
  transform: rotate(-45deg);
}

.twoslash-popup-docs-tag {
  flex-direction: row;
  gap: .5em;
  display: flex;
}

.twoslash-popup-docs-tag-name {
  font-weight: 600;
}

.twoslash-popup-code {
  overflow-wrap: anywhere;
  font-size: 13px !important;
}

.twoslash-popup-code .line {
  padding-left: 0 !important;
}

.twoslash-popup-docs, .twoslash-popup-docs-tags {
  margin-top: 8px !important;
  font-size: 14px !important;
  line-height: 20px !important;
}

.twoslash:hover .twoslash-hover {
  border-color: currentColor;
}

.twoslash .twoslash-hover {
  border-color: rgba(0, 0, 0, 0);
  border-bottom-style: dotted;
  border-bottom-width: 1px;
  transition: border .3s;
  position: relative;
}

.twoslash .twoslash-error-line {
  background-color: var(--twoslash-error-bg);
  border-left: 3px solid var(--color-twoslash-error-foreground);
  color: var(--color-twoslash-error-foreground);
  margin: .2em 0;
  padding: 6px 12px;
  position: relative;
}

.twoslash .twoslash-error {
  -webkit-text-decoration: wavy underline var(--color-twoslash-error-foreground);
  text-decoration: wavy underline var(--color-twoslash-error-foreground);
  padding-bottom: 2px;
}

.twoslash-completion-cursor:before {
  content: " ";
  background-color: var(--color-fd-foreground);
  width: 1px;
  height: 1.4em;
}

.twoslash-completion-list {
  display: inline-flex;
}

.twoslash-completion-list li {
  text-overflow: ellipsis;
  white-space: nowrap;
  align-items: center;
  gap: .5rem;
  font-size: 13px;
  display: inline-flex;
  overflow: hidden;
}

.twoslash-completion-list li span.twoslash-completions-unmatched {
  color: var(--color-fd-muted-foreground);
}

.twoslash-completion-list .deprecated {
  opacity: .5;
  text-decoration: line-through;
}

.twoslash-completion-list .twoslash-completions-icon {
  width: 1em;
  color: var(--color-fd-muted-foreground);
  flex: none;
}

.twoslash .twoslash-tag-line {
  background-color: var(--color-twoslash-tag);
  border-left: 3px solid var(--color-twoslash-tag-foreground);
  color: var(--color-twoslash-tag-foreground);
  align-items: center;
  gap: .3em;
  margin: .2em 0;
  padding: 6px 10px;
  display: flex;
  position: relative;
}

.twoslash .twoslash-tag-line .twoslash-tag-icon {
  width: 1.1em;
  color: inherit;
}

.twoslash .twoslash-tag-line.twoslash-tag-error-line {
  background-color: var(--color-twoslash-error);
  border-left: 3px solid var(--color-twoslash-error-foreground);
  color: var(--color-twoslash-error-foreground);
}

.twoslash .twoslash-tag-line.twoslash-tag-warn-line {
  background-color: var(--color-twoslash-tag-warn);
  border-left: 3px solid var(--color-twoslash-tag-warn-foreground);
  color: var(--color-twoslash-tag-warn-foreground);
}

.twoslash .twoslash-tag-line.twoslash-tag-annotate-line {
  background-color: var(--color-twoslash-tag-annotate);
  border-left: 3px solid var(--color-twoslash-tag-annotate-foreground);
  color: var(--color-twoslash-tag-annotate-foreground);
}

@media (prefers-reduced-motion: reduce) {
  .twoslash * {
    transition: none !important;
  }
}

.fd-twoslash-popover {
  z-index: 50;
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-fd-border);
  background-color: var(--color-fd-popover);
  min-width: 240px;
  max-width: min(450px, 96vw);
  max-height: 400px;
  padding: calc(var(--spacing) * 3);
  font-size: var(--text-sm);
  color: var(--color-fd-popover-foreground);
  box-shadow: var(--shadow-md);
  transform-origin: var(--radix-popover-content-transform-origin);
  overflow: auto;
}

.fd-twoslash-popover[data-state="open"] {
  animation: var(--animate-fd-popover-in);
}

.fd-twoslash-popover[data-state="closed"] {
  animation: var(--animate-fd-popover-out);
}

.fd-twoslash-popover:focus-visible {
  outline-style: none;
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-blur {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-blur {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

.dark [data-hide-on-theme="dark"], .light [data-hide-on-theme="light"] {
  display: none;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes fd-sidebar-in {
  from {
    transform: translateX(var(--fd-sidebar-mobile-offset));
  }
}

@keyframes fd-sidebar-out {
  to {
    transform: translateX(var(--fd-sidebar-mobile-offset));
  }
}

@keyframes fd-collapsible-down {
  from {
    opacity: 0;
    height: 0;
  }

  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes fd-collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
  }

  to {
    opacity: 0;
    height: 0;
  }
}

@keyframes fd-accordion-down {
  from {
    opacity: .5;
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes fd-accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    opacity: .5;
    height: 0;
  }
}

@keyframes fd-dialog-in {
  from {
    opacity: 0;
    transform: scale(1.06);
  }

  to {
    transform: scale(1);
  }
}

@keyframes fd-dialog-out {
  from {
    transform: scale(1);
  }

  to {
    opacity: 0;
    transform: scale(1.04);
  }
}

@keyframes fd-popover-in {
  from {
    opacity: 0;
    transform: scale(.7);
  }
}

@keyframes fd-popover-out {
  to {
    opacity: 0;
    transform: scale(.7);
  }
}

@keyframes fd-fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fd-fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes fd-enterFromRight {
  from {
    opacity: 0;
    transform: translateX(200px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fd-enterFromLeft {
  from {
    opacity: 0;
    transform: translateX(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fd-exitToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(200px);
  }
}

@keyframes fd-exitToLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(-200px);
  }
}

@keyframes fd-nav-menu-in {
  from {
    opacity: 0;
    height: 0;
  }

  to {
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }
}

@keyframes fd-nav-menu-out {
  from {
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }

  to {
    opacity: 0;
    height: 0;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
    filter: blur(var(--tw-enter-blur, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
    filter: blur(var(--tw-exit-blur, 0));
  }
}

/*# sourceMappingURL=src_styles_globals_css_bad6b30c._.single.css.map*/