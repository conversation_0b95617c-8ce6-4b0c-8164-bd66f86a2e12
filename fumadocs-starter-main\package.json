{"name": "fumadocs-starter", "version": "1.0.0", "private": true, "scripts": {"build": "bun run build:pre && next build && bun run build:post", "build:pre": "bun ./scripts/pre-build.mts", "build:post": "bun ./scripts/post-build.mts", "dev": "next dev --turbopack", "start": "next start", "postinstall": "fumadocs-mdx", "check": "biome check .", "check:spelling": "cspell -c .cspell.jsonc --no-progress --no-summary --no-must-find-files --unique --cache", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "lint": "fumadocs-mdx && bun ./scripts/lint.mts && biome lint", "lint:write": "biome lint --write .", "lint:unsafe": "biome lint --write --unsafe .", "format": "biome format --write .", "typecheck": "tsc --noEmit --incremental false", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@ai-sdk/openai": "^2.0.16", "@ai-sdk/react": "^2.0.16", "@bprogress/next": "^3.2.12", "@fumadocs/mdx-remote": "^1.4.0", "@inkeep/ai-api": "^0.8.0", "@next/bundle-analyzer": "^15.3.4", "@next/env": "^15.3.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@shikijs/rehype": "^3.7.0", "@t3-oss/env-nextjs": "^0.13.8", "ai": "^5.0.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fumadocs-core": "15.6.12", "fumadocs-docgen": "^2.1.0", "fumadocs-mdx": "11.7.5", "fumadocs-openapi": "9.2.0", "fumadocs-twoslash": "^3.1.6", "fumadocs-typescript": "^4.0.6", "fumadocs-ui": "15.6.12", "hast-util-to-jsx-runtime": "^2.3.6", "jiti": "1", "katex": "^0.16.22", "lucide-react": "^0.522.0", "mermaid": "^11.7.0", "motion": "^12.18.1", "next": "^15.5.0", "next-themes": "^0.4.6", "oxc-transform": "^0.56.5", "react": "^19.1.1", "react-dom": "^19.1.1", "rehype-katex": "^7.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-mdx": "^3.1.0", "remark-rehype": "^11.1.2", "remark-stringify": "^11.0.0", "rimraf": "^6.0.1", "shiki": "^3.11.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "twoslash": "^0.3.4", "unist-util-visit": "^5.0.0", "zod": "^4.0.17"}, "devDependencies": {"cspell": "^9.1.1", "@cspell/dict-bash": "^4.2.0", "@biomejs/biome": "2.2.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@fumadocs/cli": "^0.2.1", "@tailwindcss/postcss": "^4.1.10", "@types/hast": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "chokidar": "^4.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "gray-matter": "^4.0.3", "lefthook": "^1.11.14", "lint-staged": "^16.1.2", "next-validate-link": "^1.5.2", "postcss": "^8.5.6", "prettier": "^3.6.0", "source-map-support": "^0.5.21", "tailwindcss": "^4.1.10", "tinyglobby": "^0.2.14", "ts-morph": "^26.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"ajv": "^8.16.0"}, "engines": {"node": ">=22"}, "trustedDependencies": ["@tailwindcss/oxide", "esbuild", "lefthook", "sharp", "unrs-resolver"]}