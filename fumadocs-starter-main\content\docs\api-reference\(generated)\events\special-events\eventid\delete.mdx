---
title: Delete special event
full: true
_openapi:
  method: DELETE
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: >-
          Delete a special event from the collection. Allows museum to cancel
          planned events.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

Delete a special event from the collection. Allows museum to cancel planned events.

<APIPage document={"./content/docs/api-reference/openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"delete"}]} webhooks={[]} hasHead={false} />