(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/@shikijs/langs/dist/csv.mjs [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
const lang = Object.freeze(JSON.parse("{\"displayName\":\"CSV\",\"fileTypes\":[\"csv\"],\"name\":\"csv\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"rainbow1\"},\"2\":{\"name\":\"keyword.rainbow2\"},\"3\":{\"name\":\"entity.name.function.rainbow3\"},\"4\":{\"name\":\"comment.rainbow4\"},\"5\":{\"name\":\"string.rainbow5\"},\"6\":{\"name\":\"variable.parameter.rainbow6\"},\"7\":{\"name\":\"constant.numeric.rainbow7\"},\"8\":{\"name\":\"entity.name.type.rainbow8\"},\"9\":{\"name\":\"markup.bold.rainbow9\"},\"10\":{\"name\":\"invalid.rainbow10\"}},\"match\":\"( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?( *\\\"(?:[^\\\"]*\\\"\\\")*[^\\\"]*\\\" *(?:,|$)|[^,]*(?:,|$))?\",\"name\":\"rainbowgroup\"}],\"scopeName\":\"text.csv\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}),
]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_csv_mjs_9b3aec8d._.js.map