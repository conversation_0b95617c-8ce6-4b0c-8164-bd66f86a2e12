import type { InferMetaType, InferPageType } from 'fumadocs-core/source'
import { loader } from 'fumadocs-core/source'
import { attachFile, createOpenAPI } from 'fumadocs-openapi/server'
import { icons } from 'lucide-react'
import { createElement } from 'react'
import { docs } from '@/.source'

export const source = loader({
  baseUrl: '/docs',
  icon(icon) {
    if (icon && icon in icons)
      return createElement(icons[icon as keyof typeof icons])
  },
  source: docs.toFumadocsSource(),
  pageTree: {
    attachFile,
  },
})

export const openapi = createOpenAPI({
  proxyUrl: '/api/proxy',
  shikiOptions: {
    themes: {
      dark: 'vesper',
      light: 'vitesse-light',
    },
  },
})

export type Page = InferPageType<typeof source>
export type Meta = InferMetaType<typeof source>
