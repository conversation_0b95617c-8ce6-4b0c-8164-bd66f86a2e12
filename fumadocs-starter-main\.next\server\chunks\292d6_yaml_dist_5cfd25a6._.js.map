{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/identity.js"], "sourcesContent": ["'use strict';\n\nconst ALIAS = Symbol.for('yaml.alias');\nconst DOC = Symbol.for('yaml.document');\nconst MAP = Symbol.for('yaml.map');\nconst PAIR = Symbol.for('yaml.pair');\nconst SCALAR = Symbol.for('yaml.scalar');\nconst SEQ = Symbol.for('yaml.seq');\nconst NODE_TYPE = Symbol.for('yaml.node.type');\nconst isAlias = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === ALIAS;\nconst isDocument = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === DOC;\nconst isMap = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === MAP;\nconst isPair = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === PAIR;\nconst isScalar = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SCALAR;\nconst isSeq = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SEQ;\nfunction isCollection(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case MAP:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nfunction isNode(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case ALIAS:\n            case MAP:\n            case SCALAR:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nconst hasAnchor = (node) => (isScalar(node) || isCollection(node)) && !!node.anchor;\n\nexports.ALIAS = ALIAS;\nexports.DOC = DOC;\nexports.MAP = MAP;\nexports.NODE_TYPE = NODE_TYPE;\nexports.PAIR = PAIR;\nexports.SCALAR = SCALAR;\nexports.SEQ = SEQ;\nexports.hasAnchor = hasAnchor;\nexports.isAlias = isAlias;\nexports.isCollection = isCollection;\nexports.isDocument = isDocument;\nexports.isMap = isMap;\nexports.isNode = isNode;\nexports.isPair = isPair;\nexports.isScalar = isScalar;\nexports.isSeq = isSeq;\n"], "names": [], "mappings": "AAEA,MAAM,QAAQ,OAAO,GAAG,CAAC;AACzB,MAAM,MAAM,OAAO,GAAG,CAAC;AACvB,MAAM,MAAM,OAAO,GAAG,CAAC;AACvB,MAAM,OAAO,OAAO,GAAG,CAAC;AACxB,MAAM,SAAS,OAAO,GAAG,CAAC;AAC1B,MAAM,MAAM,OAAO,GAAG,CAAC;AACvB,MAAM,YAAY,OAAO,GAAG,CAAC;AAC7B,MAAM,UAAU,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AACpF,MAAM,aAAa,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AACvF,MAAM,QAAQ,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AAClF,MAAM,SAAS,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AACnF,MAAM,WAAW,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AACrF,MAAM,QAAQ,CAAC,OAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,IAAI,CAAC,UAAU,KAAK;AAClF,SAAS,aAAa,IAAI;IACtB,IAAI,QAAQ,OAAO,SAAS,UACxB,OAAQ,IAAI,CAAC,UAAU;QACnB,KAAK;QACL,KAAK;YACD,OAAO;IACf;IACJ,OAAO;AACX;AACA,SAAS,OAAO,IAAI;IAChB,IAAI,QAAQ,OAAO,SAAS,UACxB,OAAQ,IAAI,CAAC,UAAU;QACnB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;IACf;IACJ,OAAO;AACX;AACA,MAAM,YAAY,CAAC,OAAS,CAAC,SAAS,SAAS,aAAa,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM;AAEnF,QAAQ,KAAK,GAAG;AAChB,QAAQ,GAAG,GAAG;AACd,QAAQ,GAAG,GAAG;AACd,QAAQ,SAAS,GAAG;AACpB,QAAQ,IAAI,GAAG;AACf,QAAQ,MAAM,GAAG;AACjB,QAAQ,GAAG,GAAG;AACd,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG;AAClB,QAAQ,YAAY,GAAG;AACvB,QAAQ,UAAU,GAAG;AACrB,QAAQ,KAAK,GAAG;AAChB,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/visit.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('./nodes/identity.js');\n\nconst BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove node');\n/**\n * Apply a visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nfunction visit(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (identity.isDocument(node)) {\n        const cd = visit_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        visit_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisit.SKIP = SKIP;\n/** Remove the current node */\nvisit.REMOVE = REMOVE;\nfunction visit_(key, node, visitor, path) {\n    const ctrl = callVisitor(key, node, visitor, path);\n    if (identity.isNode(ctrl) || identity.isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visit_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (identity.isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = visit_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (identity.isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = visit_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = visit_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\n/**\n * Apply an async visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `Promise`: Must resolve to one of the following values\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nasync function visitAsync(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (identity.isDocument(node)) {\n        const cd = await visitAsync_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        await visitAsync_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisitAsync.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisitAsync.SKIP = SKIP;\n/** Remove the current node */\nvisitAsync.REMOVE = REMOVE;\nasync function visitAsync_(key, node, visitor, path) {\n    const ctrl = await callVisitor(key, node, visitor, path);\n    if (identity.isNode(ctrl) || identity.isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visitAsync_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (identity.isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = await visitAsync_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (identity.isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = await visitAsync_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = await visitAsync_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\nfunction initVisitor(visitor) {\n    if (typeof visitor === 'object' &&\n        (visitor.Collection || visitor.Node || visitor.Value)) {\n        return Object.assign({\n            Alias: visitor.Node,\n            Map: visitor.Node,\n            Scalar: visitor.Node,\n            Seq: visitor.Node\n        }, visitor.Value && {\n            Map: visitor.Value,\n            Scalar: visitor.Value,\n            Seq: visitor.Value\n        }, visitor.Collection && {\n            Map: visitor.Collection,\n            Seq: visitor.Collection\n        }, visitor);\n    }\n    return visitor;\n}\nfunction callVisitor(key, node, visitor, path) {\n    if (typeof visitor === 'function')\n        return visitor(key, node, path);\n    if (identity.isMap(node))\n        return visitor.Map?.(key, node, path);\n    if (identity.isSeq(node))\n        return visitor.Seq?.(key, node, path);\n    if (identity.isPair(node))\n        return visitor.Pair?.(key, node, path);\n    if (identity.isScalar(node))\n        return visitor.Scalar?.(key, node, path);\n    if (identity.isAlias(node))\n        return visitor.Alias?.(key, node, path);\n    return undefined;\n}\nfunction replaceNode(key, path, node) {\n    const parent = path[path.length - 1];\n    if (identity.isCollection(parent)) {\n        parent.items[key] = node;\n    }\n    else if (identity.isPair(parent)) {\n        if (key === 'key')\n            parent.key = node;\n        else\n            parent.value = node;\n    }\n    else if (identity.isDocument(parent)) {\n        parent.contents = node;\n    }\n    else {\n        const pt = identity.isAlias(parent) ? 'alias' : 'scalar';\n        throw new Error(`Cannot replace node with ${pt} parent`);\n    }\n}\n\nexports.visit = visit;\nexports.visitAsync = visitAsync;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,QAAQ,OAAO;AACrB,MAAM,OAAO,OAAO;AACpB,MAAM,SAAS,OAAO;AACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BC,GACD,SAAS,MAAM,IAAI,EAAE,OAAO;IACxB,MAAM,WAAW,YAAY;IAC7B,IAAI,SAAS,UAAU,CAAC,OAAO;QAC3B,MAAM,KAAK,OAAO,MAAM,KAAK,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC;YAAC;SAAK;QACrE,IAAI,OAAO,QACP,KAAK,QAAQ,GAAG;IACxB,OAEI,OAAO,MAAM,MAAM,UAAU,OAAO,MAAM,CAAC,EAAE;AACrD;AACA,kEAAkE;AAClE,+DAA+D;AAC/D,mCAAmC;AACnC,yCAAyC,GACzC,MAAM,KAAK,GAAG;AACd,kDAAkD,GAClD,MAAM,IAAI,GAAG;AACb,4BAA4B,GAC5B,MAAM,MAAM,GAAG;AACf,SAAS,OAAO,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;IACpC,MAAM,OAAO,YAAY,KAAK,MAAM,SAAS;IAC7C,IAAI,SAAS,MAAM,CAAC,SAAS,SAAS,MAAM,CAAC,OAAO;QAChD,YAAY,KAAK,MAAM;QACvB,OAAO,OAAO,KAAK,MAAM,SAAS;IACtC;IACA,IAAI,OAAO,SAAS,UAAU;QAC1B,IAAI,SAAS,YAAY,CAAC,OAAO;YAC7B,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;gBACxC,MAAM,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS;gBAC7C,IAAI,OAAO,OAAO,UACd,IAAI,KAAK;qBACR,IAAI,OAAO,OACZ,OAAO;qBACN,IAAI,OAAO,QAAQ;oBACpB,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG;oBACrB,KAAK;gBACT;YACJ;QACJ,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;YAC5B,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YACjC,MAAM,KAAK,OAAO,OAAO,KAAK,GAAG,EAAE,SAAS;YAC5C,IAAI,OAAO,OACP,OAAO;iBACN,IAAI,OAAO,QACZ,KAAK,GAAG,GAAG;YACf,MAAM,KAAK,OAAO,SAAS,KAAK,KAAK,EAAE,SAAS;YAChD,IAAI,OAAO,OACP,OAAO;iBACN,IAAI,OAAO,QACZ,KAAK,KAAK,GAAG;QACrB;IACJ;IACA,OAAO;AACX;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,eAAe,WAAW,IAAI,EAAE,OAAO;IACnC,MAAM,WAAW,YAAY;IAC7B,IAAI,SAAS,UAAU,CAAC,OAAO;QAC3B,MAAM,KAAK,MAAM,YAAY,MAAM,KAAK,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC;YAAC;SAAK;QAChF,IAAI,OAAO,QACP,KAAK,QAAQ,GAAG;IACxB,OAEI,MAAM,YAAY,MAAM,MAAM,UAAU,OAAO,MAAM,CAAC,EAAE;AAChE;AACA,kEAAkE;AAClE,+DAA+D;AAC/D,mCAAmC;AACnC,yCAAyC,GACzC,WAAW,KAAK,GAAG;AACnB,kDAAkD,GAClD,WAAW,IAAI,GAAG;AAClB,4BAA4B,GAC5B,WAAW,MAAM,GAAG;AACpB,eAAe,YAAY,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;IAC/C,MAAM,OAAO,MAAM,YAAY,KAAK,MAAM,SAAS;IACnD,IAAI,SAAS,MAAM,CAAC,SAAS,SAAS,MAAM,CAAC,OAAO;QAChD,YAAY,KAAK,MAAM;QACvB,OAAO,YAAY,KAAK,MAAM,SAAS;IAC3C;IACA,IAAI,OAAO,SAAS,UAAU;QAC1B,IAAI,SAAS,YAAY,CAAC,OAAO;YAC7B,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;gBACxC,MAAM,KAAK,MAAM,YAAY,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS;gBACxD,IAAI,OAAO,OAAO,UACd,IAAI,KAAK;qBACR,IAAI,OAAO,OACZ,OAAO;qBACN,IAAI,OAAO,QAAQ;oBACpB,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG;oBACrB,KAAK;gBACT;YACJ;QACJ,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;YAC5B,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YACjC,MAAM,KAAK,MAAM,YAAY,OAAO,KAAK,GAAG,EAAE,SAAS;YACvD,IAAI,OAAO,OACP,OAAO;iBACN,IAAI,OAAO,QACZ,KAAK,GAAG,GAAG;YACf,MAAM,KAAK,MAAM,YAAY,SAAS,KAAK,KAAK,EAAE,SAAS;YAC3D,IAAI,OAAO,OACP,OAAO;iBACN,IAAI,OAAO,QACZ,KAAK,KAAK,GAAG;QACrB;IACJ;IACA,OAAO;AACX;AACA,SAAS,YAAY,OAAO;IACxB,IAAI,OAAO,YAAY,YACnB,CAAC,QAAQ,UAAU,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK,GAAG;QACvD,OAAO,OAAO,MAAM,CAAC;YACjB,OAAO,QAAQ,IAAI;YACnB,KAAK,QAAQ,IAAI;YACjB,QAAQ,QAAQ,IAAI;YACpB,KAAK,QAAQ,IAAI;QACrB,GAAG,QAAQ,KAAK,IAAI;YAChB,KAAK,QAAQ,KAAK;YAClB,QAAQ,QAAQ,KAAK;YACrB,KAAK,QAAQ,KAAK;QACtB,GAAG,QAAQ,UAAU,IAAI;YACrB,KAAK,QAAQ,UAAU;YACvB,KAAK,QAAQ,UAAU;QAC3B,GAAG;IACP;IACA,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;IACzC,IAAI,OAAO,YAAY,YACnB,OAAO,QAAQ,KAAK,MAAM;IAC9B,IAAI,SAAS,KAAK,CAAC,OACf,OAAO,QAAQ,GAAG,GAAG,KAAK,MAAM;IACpC,IAAI,SAAS,KAAK,CAAC,OACf,OAAO,QAAQ,GAAG,GAAG,KAAK,MAAM;IACpC,IAAI,SAAS,MAAM,CAAC,OAChB,OAAO,QAAQ,IAAI,GAAG,KAAK,MAAM;IACrC,IAAI,SAAS,QAAQ,CAAC,OAClB,OAAO,QAAQ,MAAM,GAAG,KAAK,MAAM;IACvC,IAAI,SAAS,OAAO,CAAC,OACjB,OAAO,QAAQ,KAAK,GAAG,KAAK,MAAM;IACtC,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,IAAI,EAAE,IAAI;IAChC,MAAM,SAAS,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IACpC,IAAI,SAAS,YAAY,CAAC,SAAS;QAC/B,OAAO,KAAK,CAAC,IAAI,GAAG;IACxB,OACK,IAAI,SAAS,MAAM,CAAC,SAAS;QAC9B,IAAI,QAAQ,OACR,OAAO,GAAG,GAAG;aAEb,OAAO,KAAK,GAAG;IACvB,OACK,IAAI,SAAS,UAAU,CAAC,SAAS;QAClC,OAAO,QAAQ,GAAG;IACtB,OACK;QACD,MAAM,KAAK,SAAS,OAAO,CAAC,UAAU,UAAU;QAChD,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,GAAG,OAAO,CAAC;IAC3D;AACJ;AAEA,QAAQ,KAAK,GAAG;AAChB,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/doc/directives.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar visit = require('../visit.js');\n\nconst escapeChars = {\n    '!': '%21',\n    ',': '%2C',\n    '[': '%5B',\n    ']': '%5D',\n    '{': '%7B',\n    '}': '%7D'\n};\nconst escapeTagName = (tn) => tn.replace(/[!,[\\]{}]/g, ch => escapeChars[ch]);\nclass Directives {\n    constructor(yaml, tags) {\n        /**\n         * The directives-end/doc-start marker `---`. If `null`, a marker may still be\n         * included in the document's stringified representation.\n         */\n        this.docStart = null;\n        /** The doc-end marker `...`.  */\n        this.docEnd = false;\n        this.yaml = Object.assign({}, Directives.defaultYaml, yaml);\n        this.tags = Object.assign({}, Directives.defaultTags, tags);\n    }\n    clone() {\n        const copy = new Directives(this.yaml, this.tags);\n        copy.docStart = this.docStart;\n        return copy;\n    }\n    /**\n     * During parsing, get a Directives instance for the current document and\n     * update the stream state according to the current version's spec.\n     */\n    atDocument() {\n        const res = new Directives(this.yaml, this.tags);\n        switch (this.yaml.version) {\n            case '1.1':\n                this.atNextDocument = true;\n                break;\n            case '1.2':\n                this.atNextDocument = false;\n                this.yaml = {\n                    explicit: Directives.defaultYaml.explicit,\n                    version: '1.2'\n                };\n                this.tags = Object.assign({}, Directives.defaultTags);\n                break;\n        }\n        return res;\n    }\n    /**\n     * @param onError - May be called even if the action was successful\n     * @returns `true` on success\n     */\n    add(line, onError) {\n        if (this.atNextDocument) {\n            this.yaml = { explicit: Directives.defaultYaml.explicit, version: '1.1' };\n            this.tags = Object.assign({}, Directives.defaultTags);\n            this.atNextDocument = false;\n        }\n        const parts = line.trim().split(/[ \\t]+/);\n        const name = parts.shift();\n        switch (name) {\n            case '%TAG': {\n                if (parts.length !== 2) {\n                    onError(0, '%TAG directive should contain exactly two parts');\n                    if (parts.length < 2)\n                        return false;\n                }\n                const [handle, prefix] = parts;\n                this.tags[handle] = prefix;\n                return true;\n            }\n            case '%YAML': {\n                this.yaml.explicit = true;\n                if (parts.length !== 1) {\n                    onError(0, '%YAML directive should contain exactly one part');\n                    return false;\n                }\n                const [version] = parts;\n                if (version === '1.1' || version === '1.2') {\n                    this.yaml.version = version;\n                    return true;\n                }\n                else {\n                    const isValid = /^\\d+\\.\\d+$/.test(version);\n                    onError(6, `Unsupported YAML version ${version}`, isValid);\n                    return false;\n                }\n            }\n            default:\n                onError(0, `Unknown directive ${name}`, true);\n                return false;\n        }\n    }\n    /**\n     * Resolves a tag, matching handles to those defined in %TAG directives.\n     *\n     * @returns Resolved tag, which may also be the non-specific tag `'!'` or a\n     *   `'!local'` tag, or `null` if unresolvable.\n     */\n    tagName(source, onError) {\n        if (source === '!')\n            return '!'; // non-specific tag\n        if (source[0] !== '!') {\n            onError(`Not a valid tag: ${source}`);\n            return null;\n        }\n        if (source[1] === '<') {\n            const verbatim = source.slice(2, -1);\n            if (verbatim === '!' || verbatim === '!!') {\n                onError(`Verbatim tags aren't resolved, so ${source} is invalid.`);\n                return null;\n            }\n            if (source[source.length - 1] !== '>')\n                onError('Verbatim tags must end with a >');\n            return verbatim;\n        }\n        const [, handle, suffix] = source.match(/^(.*!)([^!]*)$/s);\n        if (!suffix)\n            onError(`The ${source} tag has no suffix`);\n        const prefix = this.tags[handle];\n        if (prefix) {\n            try {\n                return prefix + decodeURIComponent(suffix);\n            }\n            catch (error) {\n                onError(String(error));\n                return null;\n            }\n        }\n        if (handle === '!')\n            return source; // local tag\n        onError(`Could not resolve tag: ${source}`);\n        return null;\n    }\n    /**\n     * Given a fully resolved tag, returns its printable string form,\n     * taking into account current tag prefixes and defaults.\n     */\n    tagString(tag) {\n        for (const [handle, prefix] of Object.entries(this.tags)) {\n            if (tag.startsWith(prefix))\n                return handle + escapeTagName(tag.substring(prefix.length));\n        }\n        return tag[0] === '!' ? tag : `!<${tag}>`;\n    }\n    toString(doc) {\n        const lines = this.yaml.explicit\n            ? [`%YAML ${this.yaml.version || '1.2'}`]\n            : [];\n        const tagEntries = Object.entries(this.tags);\n        let tagNames;\n        if (doc && tagEntries.length > 0 && identity.isNode(doc.contents)) {\n            const tags = {};\n            visit.visit(doc.contents, (_key, node) => {\n                if (identity.isNode(node) && node.tag)\n                    tags[node.tag] = true;\n            });\n            tagNames = Object.keys(tags);\n        }\n        else\n            tagNames = [];\n        for (const [handle, prefix] of tagEntries) {\n            if (handle === '!!' && prefix === 'tag:yaml.org,2002:')\n                continue;\n            if (!doc || tagNames.some(tn => tn.startsWith(prefix)))\n                lines.push(`%TAG ${handle} ${prefix}`);\n        }\n        return lines.join('\\n');\n    }\n}\nDirectives.defaultYaml = { explicit: false, version: '1.2' };\nDirectives.defaultTags = { '!!': 'tag:yaml.org,2002:' };\n\nexports.Directives = Directives;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,cAAc;IAChB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACT;AACA,MAAM,gBAAgB,CAAC,KAAO,GAAG,OAAO,CAAC,cAAc,CAAA,KAAM,WAAW,CAAC,GAAG;AAC5E,MAAM;IACF,YAAY,IAAI,EAAE,IAAI,CAAE;QACpB;;;SAGC,GACD,IAAI,CAAC,QAAQ,GAAG;QAChB,+BAA+B,GAC/B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW,EAAE;QACtD,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW,EAAE;IAC1D;IACA,QAAQ;QACJ,MAAM,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;QAChD,KAAK,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAC7B,OAAO;IACX;IACA;;;KAGC,GACD,aAAa;QACT,MAAM,MAAM,IAAI,WAAW,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;QAC/C,OAAQ,IAAI,CAAC,IAAI,CAAC,OAAO;YACrB,KAAK;gBACD,IAAI,CAAC,cAAc,GAAG;gBACtB;YACJ,KAAK;gBACD,IAAI,CAAC,cAAc,GAAG;gBACtB,IAAI,CAAC,IAAI,GAAG;oBACR,UAAU,WAAW,WAAW,CAAC,QAAQ;oBACzC,SAAS;gBACb;gBACA,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW;gBACpD;QACR;QACA,OAAO;IACX;IACA;;;KAGC,GACD,IAAI,IAAI,EAAE,OAAO,EAAE;QACf,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG;gBAAE,UAAU,WAAW,WAAW,CAAC,QAAQ;gBAAE,SAAS;YAAM;YACxE,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW;YACpD,IAAI,CAAC,cAAc,GAAG;QAC1B;QACA,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,MAAM,KAAK;QACxB,OAAQ;YACJ,KAAK;gBAAQ;oBACT,IAAI,MAAM,MAAM,KAAK,GAAG;wBACpB,QAAQ,GAAG;wBACX,IAAI,MAAM,MAAM,GAAG,GACf,OAAO;oBACf;oBACA,MAAM,CAAC,QAAQ,OAAO,GAAG;oBACzB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;oBACpB,OAAO;gBACX;YACA,KAAK;gBAAS;oBACV,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;oBACrB,IAAI,MAAM,MAAM,KAAK,GAAG;wBACpB,QAAQ,GAAG;wBACX,OAAO;oBACX;oBACA,MAAM,CAAC,QAAQ,GAAG;oBAClB,IAAI,YAAY,SAAS,YAAY,OAAO;wBACxC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;wBACpB,OAAO;oBACX,OACK;wBACD,MAAM,UAAU,aAAa,IAAI,CAAC;wBAClC,QAAQ,GAAG,CAAC,yBAAyB,EAAE,SAAS,EAAE;wBAClD,OAAO;oBACX;gBACJ;YACA;gBACI,QAAQ,GAAG,CAAC,kBAAkB,EAAE,MAAM,EAAE;gBACxC,OAAO;QACf;IACJ;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE,OAAO,EAAE;QACrB,IAAI,WAAW,KACX,OAAO,KAAK,mBAAmB;QACnC,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;YACnB,QAAQ,CAAC,iBAAiB,EAAE,QAAQ;YACpC,OAAO;QACX;QACA,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;YACnB,MAAM,WAAW,OAAO,KAAK,CAAC,GAAG,CAAC;YAClC,IAAI,aAAa,OAAO,aAAa,MAAM;gBACvC,QAAQ,CAAC,kCAAkC,EAAE,OAAO,YAAY,CAAC;gBACjE,OAAO;YACX;YACA,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAC9B,QAAQ;YACZ,OAAO;QACX;QACA,MAAM,GAAG,QAAQ,OAAO,GAAG,OAAO,KAAK,CAAC;QACxC,IAAI,CAAC,QACD,QAAQ,CAAC,IAAI,EAAE,OAAO,kBAAkB,CAAC;QAC7C,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;QAChC,IAAI,QAAQ;YACR,IAAI;gBACA,OAAO,SAAS,mBAAmB;YACvC,EACA,OAAO,OAAO;gBACV,QAAQ,OAAO;gBACf,OAAO;YACX;QACJ;QACA,IAAI,WAAW,KACX,OAAO,QAAQ,YAAY;QAC/B,QAAQ,CAAC,uBAAuB,EAAE,QAAQ;QAC1C,OAAO;IACX;IACA;;;KAGC,GACD,UAAU,GAAG,EAAE;QACX,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,EAAG;YACtD,IAAI,IAAI,UAAU,CAAC,SACf,OAAO,SAAS,cAAc,IAAI,SAAS,CAAC,OAAO,MAAM;QACjE;QACA,OAAO,GAAG,CAAC,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC7C;IACA,SAAS,GAAG,EAAE;QACV,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAC1B;YAAC,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO;SAAC,GACvC,EAAE;QACR,MAAM,aAAa,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI;QACJ,IAAI,OAAO,WAAW,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,IAAI,QAAQ,GAAG;YAC/D,MAAM,OAAO,CAAC;YACd,MAAM,KAAK,CAAC,IAAI,QAAQ,EAAE,CAAC,MAAM;gBAC7B,IAAI,SAAS,MAAM,CAAC,SAAS,KAAK,GAAG,EACjC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG;YACzB;YACA,WAAW,OAAO,IAAI,CAAC;QAC3B,OAEI,WAAW,EAAE;QACjB,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,WAAY;YACvC,IAAI,WAAW,QAAQ,WAAW,sBAC9B;YACJ,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,UAAU,CAAC,UAC1C,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,QAAQ;QAC7C;QACA,OAAO,MAAM,IAAI,CAAC;IACtB;AACJ;AACA,WAAW,WAAW,GAAG;IAAE,UAAU;IAAO,SAAS;AAAM;AAC3D,WAAW,WAAW,GAAG;IAAE,MAAM;AAAqB;AAEtD,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/doc/anchors.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar visit = require('../visit.js');\n\n/**\n * Verify that the input string is a valid anchor.\n *\n * Will throw on errors.\n */\nfunction anchorIsValid(anchor) {\n    if (/[\\x00-\\x19\\s,[\\]{}]/.test(anchor)) {\n        const sa = JSON.stringify(anchor);\n        const msg = `Anchor must not contain whitespace or control characters: ${sa}`;\n        throw new Error(msg);\n    }\n    return true;\n}\nfunction anchorNames(root) {\n    const anchors = new Set();\n    visit.visit(root, {\n        Value(_key, node) {\n            if (node.anchor)\n                anchors.add(node.anchor);\n        }\n    });\n    return anchors;\n}\n/** Find a new anchor name with the given `prefix` and a one-indexed suffix. */\nfunction findNewAnchor(prefix, exclude) {\n    for (let i = 1; true; ++i) {\n        const name = `${prefix}${i}`;\n        if (!exclude.has(name))\n            return name;\n    }\n}\nfunction createNodeAnchors(doc, prefix) {\n    const aliasObjects = [];\n    const sourceObjects = new Map();\n    let prevAnchors = null;\n    return {\n        onAnchor: (source) => {\n            aliasObjects.push(source);\n            prevAnchors ?? (prevAnchors = anchorNames(doc));\n            const anchor = findNewAnchor(prefix, prevAnchors);\n            prevAnchors.add(anchor);\n            return anchor;\n        },\n        /**\n         * With circular references, the source node is only resolved after all\n         * of its child nodes are. This is why anchors are set only after all of\n         * the nodes have been created.\n         */\n        setAnchors: () => {\n            for (const source of aliasObjects) {\n                const ref = sourceObjects.get(source);\n                if (typeof ref === 'object' &&\n                    ref.anchor &&\n                    (identity.isScalar(ref.node) || identity.isCollection(ref.node))) {\n                    ref.node.anchor = ref.anchor;\n                }\n                else {\n                    const error = new Error('Failed to resolve repeated object (this should not happen)');\n                    error.source = source;\n                    throw error;\n                }\n            }\n        },\n        sourceObjects\n    };\n}\n\nexports.anchorIsValid = anchorIsValid;\nexports.anchorNames = anchorNames;\nexports.createNodeAnchors = createNodeAnchors;\nexports.findNewAnchor = findNewAnchor;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ;;;;CAIC,GACD,SAAS,cAAc,MAAM;IACzB,IAAI,sBAAsB,IAAI,CAAC,SAAS;QACpC,MAAM,KAAK,KAAK,SAAS,CAAC;QAC1B,MAAM,MAAM,CAAC,0DAA0D,EAAE,IAAI;QAC7E,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AACA,SAAS,YAAY,IAAI;IACrB,MAAM,UAAU,IAAI;IACpB,MAAM,KAAK,CAAC,MAAM;QACd,OAAM,IAAI,EAAE,IAAI;YACZ,IAAI,KAAK,MAAM,EACX,QAAQ,GAAG,CAAC,KAAK,MAAM;QAC/B;IACJ;IACA,OAAO;AACX;AACA,6EAA6E,GAC7E,SAAS,cAAc,MAAM,EAAE,OAAO;IAClC,IAAK,IAAI,IAAI,GAAG,MAAM,EAAE,EAAG;QACvB,MAAM,OAAO,GAAG,SAAS,GAAG;QAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,OACb,OAAO;IACf;AACJ;AACA,SAAS,kBAAkB,GAAG,EAAE,MAAM;IAClC,MAAM,eAAe,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,IAAI,cAAc;IAClB,OAAO;QACH,UAAU,CAAC;YACP,aAAa,IAAI,CAAC;YAClB,eAAe,CAAC,cAAc,YAAY,IAAI;YAC9C,MAAM,SAAS,cAAc,QAAQ;YACrC,YAAY,GAAG,CAAC;YAChB,OAAO;QACX;QACA;;;;SAIC,GACD,YAAY;YACR,KAAK,MAAM,UAAU,aAAc;gBAC/B,MAAM,MAAM,cAAc,GAAG,CAAC;gBAC9B,IAAI,OAAO,QAAQ,YACf,IAAI,MAAM,IACV,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI,KAAK,SAAS,YAAY,CAAC,IAAI,IAAI,CAAC,GAAG;oBAClE,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;gBAChC,OACK;oBACD,MAAM,QAAQ,IAAI,MAAM;oBACxB,MAAM,MAAM,GAAG;oBACf,MAAM;gBACV;YACJ;QACJ;QACA;IACJ;AACJ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,WAAW,GAAG;AACtB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/doc/applyReviver.js"], "sourcesContent": ["'use strict';\n\n/**\n * Applies the JSON.parse reviver algorithm as defined in the ECMA-262 spec,\n * in section 24.5.1.1 \"Runtime Semantics: InternalizeJSONProperty\" of the\n * 2021 edition: https://tc39.es/ecma262/#sec-json.parse\n *\n * Includes extensions for handling Map and Set objects.\n */\nfunction applyReviver(reviver, obj, key, val) {\n    if (val && typeof val === 'object') {\n        if (Array.isArray(val)) {\n            for (let i = 0, len = val.length; i < len; ++i) {\n                const v0 = val[i];\n                const v1 = applyReviver(reviver, val, String(i), v0);\n                // eslint-disable-next-line @typescript-eslint/no-array-delete\n                if (v1 === undefined)\n                    delete val[i];\n                else if (v1 !== v0)\n                    val[i] = v1;\n            }\n        }\n        else if (val instanceof Map) {\n            for (const k of Array.from(val.keys())) {\n                const v0 = val.get(k);\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    val.delete(k);\n                else if (v1 !== v0)\n                    val.set(k, v1);\n            }\n        }\n        else if (val instanceof Set) {\n            for (const v0 of Array.from(val)) {\n                const v1 = applyReviver(reviver, val, v0, v0);\n                if (v1 === undefined)\n                    val.delete(v0);\n                else if (v1 !== v0) {\n                    val.delete(v0);\n                    val.add(v1);\n                }\n            }\n        }\n        else {\n            for (const [k, v0] of Object.entries(val)) {\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    delete val[k];\n                else if (v1 !== v0)\n                    val[k] = v1;\n            }\n        }\n    }\n    return reviver.call(obj, key, val);\n}\n\nexports.applyReviver = applyReviver;\n"], "names": [], "mappings": "AAEA;;;;;;CAMC,GACD,SAAS,aAAa,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACxC,IAAI,OAAO,OAAO,QAAQ,UAAU;QAChC,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;gBAC5C,MAAM,KAAK,GAAG,CAAC,EAAE;gBACjB,MAAM,KAAK,aAAa,SAAS,KAAK,OAAO,IAAI;gBACjD,8DAA8D;gBAC9D,IAAI,OAAO,WACP,OAAO,GAAG,CAAC,EAAE;qBACZ,IAAI,OAAO,IACZ,GAAG,CAAC,EAAE,GAAG;YACjB;QACJ,OACK,IAAI,eAAe,KAAK;YACzB,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI,IAAI,IAAK;gBACpC,MAAM,KAAK,IAAI,GAAG,CAAC;gBACnB,MAAM,KAAK,aAAa,SAAS,KAAK,GAAG;gBACzC,IAAI,OAAO,WACP,IAAI,MAAM,CAAC;qBACV,IAAI,OAAO,IACZ,IAAI,GAAG,CAAC,GAAG;YACnB;QACJ,OACK,IAAI,eAAe,KAAK;YACzB,KAAK,MAAM,MAAM,MAAM,IAAI,CAAC,KAAM;gBAC9B,MAAM,KAAK,aAAa,SAAS,KAAK,IAAI;gBAC1C,IAAI,OAAO,WACP,IAAI,MAAM,CAAC;qBACV,IAAI,OAAO,IAAI;oBAChB,IAAI,MAAM,CAAC;oBACX,IAAI,GAAG,CAAC;gBACZ;YACJ;QACJ,OACK;YACD,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,OAAO,OAAO,CAAC,KAAM;gBACvC,MAAM,KAAK,aAAa,SAAS,KAAK,GAAG;gBACzC,IAAI,OAAO,WACP,OAAO,GAAG,CAAC,EAAE;qBACZ,IAAI,OAAO,IACZ,GAAG,CAAC,EAAE,GAAG;YACjB;QACJ;IACJ;IACA,OAAO,QAAQ,IAAI,CAAC,KAAK,KAAK;AAClC;AAEA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/toJS.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('./identity.js');\n\n/**\n * Recursively convert any node or its contents to native JavaScript\n *\n * @param value - The input value\n * @param arg - If `value` defines a `toJSON()` method, use this\n *   as its first argument\n * @param ctx - Conversion context, originally set in Document#toJS(). If\n *   `{ keep: true }` is not set, output should be suitable for JSON\n *   stringification.\n */\nfunction toJS(value, arg, ctx) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n    if (Array.isArray(value))\n        return value.map((v, i) => toJS(v, String(i), ctx));\n    if (value && typeof value.toJSON === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        if (!ctx || !identity.hasAnchor(value))\n            return value.toJSON(arg, ctx);\n        const data = { aliasCount: 0, count: 1, res: undefined };\n        ctx.anchors.set(value, data);\n        ctx.onCreate = res => {\n            data.res = res;\n            delete ctx.onCreate;\n        };\n        const res = value.toJSON(arg, ctx);\n        if (ctx.onCreate)\n            ctx.onCreate(res);\n        return res;\n    }\n    if (typeof value === 'bigint' && !ctx?.keep)\n        return Number(value);\n    return value;\n}\n\nexports.toJS = toJS;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ;;;;;;;;;CASC,GACD,SAAS,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG;IACzB,+DAA+D;IAC/D,IAAI,MAAM,OAAO,CAAC,QACd,OAAO,MAAM,GAAG,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,OAAO,IAAI;IAClD,IAAI,SAAS,OAAO,MAAM,MAAM,KAAK,YAAY;QAC7C,6DAA6D;QAC7D,IAAI,CAAC,OAAO,CAAC,SAAS,SAAS,CAAC,QAC5B,OAAO,MAAM,MAAM,CAAC,KAAK;QAC7B,MAAM,OAAO;YAAE,YAAY;YAAG,OAAO;YAAG,KAAK;QAAU;QACvD,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO;QACvB,IAAI,QAAQ,GAAG,CAAA;YACX,KAAK,GAAG,GAAG;YACX,OAAO,IAAI,QAAQ;QACvB;QACA,MAAM,MAAM,MAAM,MAAM,CAAC,KAAK;QAC9B,IAAI,IAAI,QAAQ,EACZ,IAAI,QAAQ,CAAC;QACjB,OAAO;IACX;IACA,IAAI,OAAO,UAAU,YAAY,CAAC,KAAK,MACnC,OAAO,OAAO;IAClB,OAAO;AACX;AAEA,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/Node.js"], "sourcesContent": ["'use strict';\n\nvar applyReviver = require('../doc/applyReviver.js');\nvar identity = require('./identity.js');\nvar toJS = require('./toJS.js');\n\nclass NodeBase {\n    constructor(type) {\n        Object.defineProperty(this, identity.NODE_TYPE, { value: type });\n    }\n    /** Create a copy of this node.  */\n    clone() {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** A plain JavaScript representation of this node. */\n    toJS(doc, { mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        if (!identity.isDocument(doc))\n            throw new TypeError('A document argument is required');\n        const ctx = {\n            anchors: new Map(),\n            doc,\n            keep: true,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS.toJS(this, '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver.applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n}\n\nexports.NodeBase = NodeBase;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM;IACF,YAAY,IAAI,CAAE;QACd,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,SAAS,EAAE;YAAE,OAAO;QAAK;IAClE;IACA,iCAAiC,GACjC,QAAQ;QACJ,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC,IAAI,GAAG,OAAO,yBAAyB,CAAC,IAAI;QAC7F,IAAI,IAAI,CAAC,KAAK,EACV,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;QACjC,OAAO;IACX;IACA,oDAAoD,GACpD,KAAK,GAAG,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE;QAC3D,IAAI,CAAC,SAAS,UAAU,CAAC,MACrB,MAAM,IAAI,UAAU;QACxB,MAAM,MAAM;YACR,SAAS,IAAI;YACb;YACA,MAAM;YACN,UAAU,aAAa;YACvB,cAAc;YACd,eAAe,OAAO,kBAAkB,WAAW,gBAAgB;QACvE;QACA,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;QAChC,IAAI,OAAO,aAAa,YACpB,KAAK,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,IAAI,OAAO,CAAC,MAAM,GAC3C,SAAS,KAAK;QACtB,OAAO,OAAO,YAAY,aACpB,aAAa,YAAY,CAAC,SAAS;YAAE,IAAI;QAAI,GAAG,IAAI,OACpD;IACV;AACJ;AAEA,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/Alias.js"], "sourcesContent": ["'use strict';\n\nvar anchors = require('../doc/anchors.js');\nvar visit = require('../visit.js');\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\nvar toJS = require('./toJS.js');\n\nclass Alias extends Node.NodeBase {\n    constructor(source) {\n        super(identity.ALIAS);\n        this.source = source;\n        Object.defineProperty(this, 'tag', {\n            set() {\n                throw new Error('Alias nodes cannot have tags');\n            }\n        });\n    }\n    /**\n     * Resolve the value of this alias within `doc`, finding the last\n     * instance of the `source` anchor before this node.\n     */\n    resolve(doc, ctx) {\n        let nodes;\n        if (ctx?.aliasResolveCache) {\n            nodes = ctx.aliasResolveCache;\n        }\n        else {\n            nodes = [];\n            visit.visit(doc, {\n                Node: (_key, node) => {\n                    if (identity.isAlias(node) || identity.hasAnchor(node))\n                        nodes.push(node);\n                }\n            });\n            if (ctx)\n                ctx.aliasResolveCache = nodes;\n        }\n        let found = undefined;\n        for (const node of nodes) {\n            if (node === this)\n                break;\n            if (node.anchor === this.source)\n                found = node;\n        }\n        return found;\n    }\n    toJSON(_arg, ctx) {\n        if (!ctx)\n            return { source: this.source };\n        const { anchors, doc, maxAliasCount } = ctx;\n        const source = this.resolve(doc, ctx);\n        if (!source) {\n            const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n            throw new ReferenceError(msg);\n        }\n        let data = anchors.get(source);\n        if (!data) {\n            // Resolve anchors for Node.prototype.toJS()\n            toJS.toJS(source, null, ctx);\n            data = anchors.get(source);\n        }\n        /* istanbul ignore if */\n        if (!data || data.res === undefined) {\n            const msg = 'This should not happen: Alias anchor was not resolved?';\n            throw new ReferenceError(msg);\n        }\n        if (maxAliasCount >= 0) {\n            data.count += 1;\n            if (data.aliasCount === 0)\n                data.aliasCount = getAliasCount(doc, source, anchors);\n            if (data.count * data.aliasCount > maxAliasCount) {\n                const msg = 'Excessive alias count indicates a resource exhaustion attack';\n                throw new ReferenceError(msg);\n            }\n        }\n        return data.res;\n    }\n    toString(ctx, _onComment, _onChompKeep) {\n        const src = `*${this.source}`;\n        if (ctx) {\n            anchors.anchorIsValid(this.source);\n            if (ctx.options.verifyAliasOrder && !ctx.anchors.has(this.source)) {\n                const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n                throw new Error(msg);\n            }\n            if (ctx.implicitKey)\n                return `${src} `;\n        }\n        return src;\n    }\n}\nfunction getAliasCount(doc, node, anchors) {\n    if (identity.isAlias(node)) {\n        const source = node.resolve(doc);\n        const anchor = anchors && source && anchors.get(source);\n        return anchor ? anchor.count * anchor.aliasCount : 0;\n    }\n    else if (identity.isCollection(node)) {\n        let count = 0;\n        for (const item of node.items) {\n            const c = getAliasCount(doc, item, anchors);\n            if (c > count)\n                count = c;\n        }\n        return count;\n    }\n    else if (identity.isPair(node)) {\n        const kc = getAliasCount(doc, node.key, anchors);\n        const vc = getAliasCount(doc, node.value, anchors);\n        return Math.max(kc, vc);\n    }\n    return 1;\n}\n\nexports.Alias = Alias;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,cAAc,KAAK,QAAQ;IAC7B,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO;YAC/B;gBACI,MAAM,IAAI,MAAM;YACpB;QACJ;IACJ;IACA;;;KAGC,GACD,QAAQ,GAAG,EAAE,GAAG,EAAE;QACd,IAAI;QACJ,IAAI,KAAK,mBAAmB;YACxB,QAAQ,IAAI,iBAAiB;QACjC,OACK;YACD,QAAQ,EAAE;YACV,MAAM,KAAK,CAAC,KAAK;gBACb,MAAM,CAAC,MAAM;oBACT,IAAI,SAAS,OAAO,CAAC,SAAS,SAAS,SAAS,CAAC,OAC7C,MAAM,IAAI,CAAC;gBACnB;YACJ;YACA,IAAI,KACA,IAAI,iBAAiB,GAAG;QAChC;QACA,IAAI,QAAQ;QACZ,KAAK,MAAM,QAAQ,MAAO;YACtB,IAAI,SAAS,IAAI,EACb;YACJ,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,EAC3B,QAAQ;QAChB;QACA,OAAO;IACX;IACA,OAAO,IAAI,EAAE,GAAG,EAAE;QACd,IAAI,CAAC,KACD,OAAO;YAAE,QAAQ,IAAI,CAAC,MAAM;QAAC;QACjC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,GAAG;QACxC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK;QACjC,IAAI,CAAC,QAAQ;YACT,MAAM,MAAM,CAAC,4DAA4D,EAAE,IAAI,CAAC,MAAM,EAAE;YACxF,MAAM,IAAI,eAAe;QAC7B;QACA,IAAI,OAAO,QAAQ,GAAG,CAAC;QACvB,IAAI,CAAC,MAAM;YACP,4CAA4C;YAC5C,KAAK,IAAI,CAAC,QAAQ,MAAM;YACxB,OAAO,QAAQ,GAAG,CAAC;QACvB;QACA,sBAAsB,GACtB,IAAI,CAAC,QAAQ,KAAK,GAAG,KAAK,WAAW;YACjC,MAAM,MAAM;YACZ,MAAM,IAAI,eAAe;QAC7B;QACA,IAAI,iBAAiB,GAAG;YACpB,KAAK,KAAK,IAAI;YACd,IAAI,KAAK,UAAU,KAAK,GACpB,KAAK,UAAU,GAAG,cAAc,KAAK,QAAQ;YACjD,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,GAAG,eAAe;gBAC9C,MAAM,MAAM;gBACZ,MAAM,IAAI,eAAe;YAC7B;QACJ;QACA,OAAO,KAAK,GAAG;IACnB;IACA,SAAS,GAAG,EAAE,UAAU,EAAE,YAAY,EAAE;QACpC,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;QAC7B,IAAI,KAAK;YACL,QAAQ,aAAa,CAAC,IAAI,CAAC,MAAM;YACjC,IAAI,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;gBAC/D,MAAM,MAAM,CAAC,4DAA4D,EAAE,IAAI,CAAC,MAAM,EAAE;gBACxF,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,IAAI,WAAW,EACf,OAAO,GAAG,IAAI,CAAC,CAAC;QACxB;QACA,OAAO;IACX;AACJ;AACA,SAAS,cAAc,GAAG,EAAE,IAAI,EAAE,OAAO;IACrC,IAAI,SAAS,OAAO,CAAC,OAAO;QACxB,MAAM,SAAS,KAAK,OAAO,CAAC;QAC5B,MAAM,SAAS,WAAW,UAAU,QAAQ,GAAG,CAAC;QAChD,OAAO,SAAS,OAAO,KAAK,GAAG,OAAO,UAAU,GAAG;IACvD,OACK,IAAI,SAAS,YAAY,CAAC,OAAO;QAClC,IAAI,QAAQ;QACZ,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;YAC3B,MAAM,IAAI,cAAc,KAAK,MAAM;YACnC,IAAI,IAAI,OACJ,QAAQ;QAChB;QACA,OAAO;IACX,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;QAC5B,MAAM,KAAK,cAAc,KAAK,KAAK,GAAG,EAAE;QACxC,MAAM,KAAK,cAAc,KAAK,KAAK,KAAK,EAAE;QAC1C,OAAO,KAAK,GAAG,CAAC,IAAI;IACxB;IACA,OAAO;AACX;AAEA,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/Scalar.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\nvar toJS = require('./toJS.js');\n\nconst isScalarValue = (value) => !value || (typeof value !== 'function' && typeof value !== 'object');\nclass Scalar extends Node.NodeBase {\n    constructor(value) {\n        super(identity.SCALAR);\n        this.value = value;\n    }\n    toJSON(arg, ctx) {\n        return ctx?.keep ? this.value : toJS.toJS(this.value, arg, ctx);\n    }\n    toString() {\n        return String(this.value);\n    }\n}\nScalar.BLOCK_FOLDED = 'BLOCK_FOLDED';\nScalar.BLOCK_LITERAL = 'BLOCK_LITERAL';\nScalar.PLAIN = 'PLAIN';\nScalar.QUOTE_DOUBLE = 'QUOTE_DOUBLE';\nScalar.QUOTE_SINGLE = 'QUOTE_SINGLE';\n\nexports.Scalar = Scalar;\nexports.isScalarValue = isScalarValue;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,gBAAgB,CAAC,QAAU,CAAC,SAAU,OAAO,UAAU,cAAc,OAAO,UAAU;AAC5F,MAAM,eAAe,KAAK,QAAQ;IAC9B,YAAY,KAAK,CAAE;QACf,KAAK,CAAC,SAAS,MAAM;QACrB,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,OAAO,GAAG,EAAE,GAAG,EAAE;QACb,OAAO,KAAK,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK;IAC/D;IACA,WAAW;QACP,OAAO,OAAO,IAAI,CAAC,KAAK;IAC5B;AACJ;AACA,OAAO,YAAY,GAAG;AACtB,OAAO,aAAa,GAAG;AACvB,OAAO,KAAK,GAAG;AACf,OAAO,YAAY,GAAG;AACtB,OAAO,YAAY,GAAG;AAEtB,QAAQ,MAAM,GAAG;AACjB,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/doc/createNode.js"], "sourcesContent": ["'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\n\nconst defaultTagPrefix = 'tag:yaml.org,2002:';\nfunction findTagObject(value, tagName, tags) {\n    if (tagName) {\n        const match = tags.filter(t => t.tag === tagName);\n        const tagObj = match.find(t => !t.format) ?? match[0];\n        if (!tagObj)\n            throw new Error(`Tag ${tagName} not found`);\n        return tagObj;\n    }\n    return tags.find(t => t.identify?.(value) && !t.format);\n}\nfunction createNode(value, tagName, ctx) {\n    if (identity.isDocument(value))\n        value = value.contents;\n    if (identity.isNode(value))\n        return value;\n    if (identity.isPair(value)) {\n        const map = ctx.schema[identity.MAP].createNode?.(ctx.schema, null, ctx);\n        map.items.push(value);\n        return map;\n    }\n    if (value instanceof String ||\n        value instanceof Number ||\n        value instanceof Boolean ||\n        (typeof BigInt !== 'undefined' && value instanceof BigInt) // not supported everywhere\n    ) {\n        // https://tc39.es/ecma262/#sec-serializejsonproperty\n        value = value.valueOf();\n    }\n    const { aliasDuplicateObjects, onAnchor, onTagObj, schema, sourceObjects } = ctx;\n    // Detect duplicate references to the same object & use Alias nodes for all\n    // after first. The `ref` wrapper allows for circular references to resolve.\n    let ref = undefined;\n    if (aliasDuplicateObjects && value && typeof value === 'object') {\n        ref = sourceObjects.get(value);\n        if (ref) {\n            ref.anchor ?? (ref.anchor = onAnchor(value));\n            return new Alias.Alias(ref.anchor);\n        }\n        else {\n            ref = { anchor: null, node: null };\n            sourceObjects.set(value, ref);\n        }\n    }\n    if (tagName?.startsWith('!!'))\n        tagName = defaultTagPrefix + tagName.slice(2);\n    let tagObj = findTagObject(value, tagName, schema.tags);\n    if (!tagObj) {\n        if (value && typeof value.toJSON === 'function') {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n            value = value.toJSON();\n        }\n        if (!value || typeof value !== 'object') {\n            const node = new Scalar.Scalar(value);\n            if (ref)\n                ref.node = node;\n            return node;\n        }\n        tagObj =\n            value instanceof Map\n                ? schema[identity.MAP]\n                : Symbol.iterator in Object(value)\n                    ? schema[identity.SEQ]\n                    : schema[identity.MAP];\n    }\n    if (onTagObj) {\n        onTagObj(tagObj);\n        delete ctx.onTagObj;\n    }\n    const node = tagObj?.createNode\n        ? tagObj.createNode(ctx.schema, value, ctx)\n        : typeof tagObj?.nodeClass?.from === 'function'\n            ? tagObj.nodeClass.from(ctx.schema, value, ctx)\n            : new Scalar.Scalar(value);\n    if (tagName)\n        node.tag = tagName;\n    else if (!tagObj.default)\n        node.tag = tagObj.tag;\n    if (ref)\n        ref.node = node;\n    return node;\n}\n\nexports.createNode = createNode;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,mBAAmB;AACzB,SAAS,cAAc,KAAK,EAAE,OAAO,EAAE,IAAI;IACvC,IAAI,SAAS;QACT,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;QACzC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,KAAK,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC,QACD,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,UAAU,CAAC;QAC9C,OAAO;IACX;IACA,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,UAAU,CAAC,EAAE,MAAM;AAC1D;AACA,SAAS,WAAW,KAAK,EAAE,OAAO,EAAE,GAAG;IACnC,IAAI,SAAS,UAAU,CAAC,QACpB,QAAQ,MAAM,QAAQ;IAC1B,IAAI,SAAS,MAAM,CAAC,QAChB,OAAO;IACX,IAAI,SAAS,MAAM,CAAC,QAAQ;QACxB,MAAM,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,UAAU,GAAG,IAAI,MAAM,EAAE,MAAM;QACpE,IAAI,KAAK,CAAC,IAAI,CAAC;QACf,OAAO;IACX;IACA,IAAI,iBAAiB,UACjB,iBAAiB,UACjB,iBAAiB,WAChB,OAAO,WAAW,eAAe,iBAAiB,OAAQ,2BAA2B;MACxF;QACE,qDAAqD;QACrD,QAAQ,MAAM,OAAO;IACzB;IACA,MAAM,EAAE,qBAAqB,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG;IAC7E,2EAA2E;IAC3E,4EAA4E;IAC5E,IAAI,MAAM;IACV,IAAI,yBAAyB,SAAS,OAAO,UAAU,UAAU;QAC7D,MAAM,cAAc,GAAG,CAAC;QACxB,IAAI,KAAK;YACL,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,SAAS,MAAM;YAC3C,OAAO,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM;QACrC,OACK;YACD,MAAM;gBAAE,QAAQ;gBAAM,MAAM;YAAK;YACjC,cAAc,GAAG,CAAC,OAAO;QAC7B;IACJ;IACA,IAAI,SAAS,WAAW,OACpB,UAAU,mBAAmB,QAAQ,KAAK,CAAC;IAC/C,IAAI,SAAS,cAAc,OAAO,SAAS,OAAO,IAAI;IACtD,IAAI,CAAC,QAAQ;QACT,IAAI,SAAS,OAAO,MAAM,MAAM,KAAK,YAAY;YAC7C,6DAA6D;YAC7D,QAAQ,MAAM,MAAM;QACxB;QACA,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;YACrC,MAAM,OAAO,IAAI,OAAO,MAAM,CAAC;YAC/B,IAAI,KACA,IAAI,IAAI,GAAG;YACf,OAAO;QACX;QACA,SACI,iBAAiB,MACX,MAAM,CAAC,SAAS,GAAG,CAAC,GACpB,OAAO,QAAQ,IAAI,OAAO,SACtB,MAAM,CAAC,SAAS,GAAG,CAAC,GACpB,MAAM,CAAC,SAAS,GAAG,CAAC;IACtC;IACA,IAAI,UAAU;QACV,SAAS;QACT,OAAO,IAAI,QAAQ;IACvB;IACA,MAAM,OAAO,QAAQ,aACf,OAAO,UAAU,CAAC,IAAI,MAAM,EAAE,OAAO,OACrC,OAAO,QAAQ,WAAW,SAAS,aAC/B,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,OAAO,OACzC,IAAI,OAAO,MAAM,CAAC;IAC5B,IAAI,SACA,KAAK,GAAG,GAAG;SACV,IAAI,CAAC,OAAO,OAAO,EACpB,KAAK,GAAG,GAAG,OAAO,GAAG;IACzB,IAAI,KACA,IAAI,IAAI,GAAG;IACf,OAAO;AACX;AAEA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/Collection.js"], "sourcesContent": ["'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\n\nfunction collectionFromPath(schema, path, value) {\n    let v = value;\n    for (let i = path.length - 1; i >= 0; --i) {\n        const k = path[i];\n        if (typeof k === 'number' && Number.isInteger(k) && k >= 0) {\n            const a = [];\n            a[k] = v;\n            v = a;\n        }\n        else {\n            v = new Map([[k, v]]);\n        }\n    }\n    return createNode.createNode(v, undefined, {\n        aliasDuplicateObjects: false,\n        keepUndefined: false,\n        onAnchor: () => {\n            throw new Error('This should not happen, please report a bug.');\n        },\n        schema,\n        sourceObjects: new Map()\n    });\n}\n// Type guard is intentionally a little wrong so as to be more useful,\n// as it does not cover untypable empty non-string iterables (e.g. []).\nconst isEmptyPath = (path) => path == null ||\n    (typeof path === 'object' && !!path[Symbol.iterator]().next().done);\nclass Collection extends Node.NodeBase {\n    constructor(type, schema) {\n        super(type);\n        Object.defineProperty(this, 'schema', {\n            value: schema,\n            configurable: true,\n            enumerable: false,\n            writable: true\n        });\n    }\n    /**\n     * Create a copy of this collection.\n     *\n     * @param schema - If defined, overwrites the original's schema\n     */\n    clone(schema) {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (schema)\n            copy.schema = schema;\n        copy.items = copy.items.map(it => identity.isNode(it) || identity.isPair(it) ? it.clone(schema) : it);\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /**\n     * Adds a value to the collection. For `!!map` and `!!omap` the value must\n     * be a Pair instance or a `{ key, value }` object, which may not have a key\n     * that already exists in the map.\n     */\n    addIn(path, value) {\n        if (isEmptyPath(path))\n            this.add(value);\n        else {\n            const [key, ...rest] = path;\n            const node = this.get(key, true);\n            if (identity.isCollection(node))\n                node.addIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n    /**\n     * Removes a value from the collection.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.delete(key);\n        const node = this.get(key, true);\n        if (identity.isCollection(node))\n            return node.deleteIn(rest);\n        else\n            throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        const [key, ...rest] = path;\n        const node = this.get(key, true);\n        if (rest.length === 0)\n            return !keepScalar && identity.isScalar(node) ? node.value : node;\n        else\n            return identity.isCollection(node) ? node.getIn(rest, keepScalar) : undefined;\n    }\n    hasAllNullValues(allowScalar) {\n        return this.items.every(node => {\n            if (!identity.isPair(node))\n                return false;\n            const n = node.value;\n            return (n == null ||\n                (allowScalar &&\n                    identity.isScalar(n) &&\n                    n.value == null &&\n                    !n.commentBefore &&\n                    !n.comment &&\n                    !n.tag));\n        });\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     */\n    hasIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.has(key);\n        const node = this.get(key, true);\n        return identity.isCollection(node) ? node.hasIn(rest) : false;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        const [key, ...rest] = path;\n        if (rest.length === 0) {\n            this.set(key, value);\n        }\n        else {\n            const node = this.get(key, true);\n            if (identity.isCollection(node))\n                node.setIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n}\n\nexports.Collection = Collection;\nexports.collectionFromPath = collectionFromPath;\nexports.isEmptyPath = isEmptyPath;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,mBAAmB,MAAM,EAAE,IAAI,EAAE,KAAK;IAC3C,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;QACvC,MAAM,IAAI,IAAI,CAAC,EAAE;QACjB,IAAI,OAAO,MAAM,YAAY,OAAO,SAAS,CAAC,MAAM,KAAK,GAAG;YACxD,MAAM,IAAI,EAAE;YACZ,CAAC,CAAC,EAAE,GAAG;YACP,IAAI;QACR,OACK;YACD,IAAI,IAAI,IAAI;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QACxB;IACJ;IACA,OAAO,WAAW,UAAU,CAAC,GAAG,WAAW;QACvC,uBAAuB;QACvB,eAAe;QACf,UAAU;YACN,MAAM,IAAI,MAAM;QACpB;QACA;QACA,eAAe,IAAI;IACvB;AACJ;AACA,sEAAsE;AACtE,uEAAuE;AACvE,MAAM,cAAc,CAAC,OAAS,QAAQ,QACjC,OAAO,SAAS,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI;AACtE,MAAM,mBAAmB,KAAK,QAAQ;IAClC,YAAY,IAAI,EAAE,MAAM,CAAE;QACtB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,OAAO;YACP,cAAc;YACd,YAAY;YACZ,UAAU;QACd;IACJ;IACA;;;;KAIC,GACD,MAAM,MAAM,EAAE;QACV,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC,IAAI,GAAG,OAAO,yBAAyB,CAAC,IAAI;QAC7F,IAAI,QACA,KAAK,MAAM,GAAG;QAClB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,KAAM,SAAS,MAAM,CAAC,OAAO,SAAS,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU;QAClG,IAAI,IAAI,CAAC,KAAK,EACV,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;QACjC,OAAO;IACX;IACA;;;;KAIC,GACD,MAAM,IAAI,EAAE,KAAK,EAAE;QACf,IAAI,YAAY,OACZ,IAAI,CAAC,GAAG,CAAC;aACR;YACD,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG;YACvB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;YAC3B,IAAI,SAAS,YAAY,CAAC,OACtB,KAAK,KAAK,CAAC,MAAM;iBAChB,IAAI,SAAS,aAAa,IAAI,CAAC,MAAM,EACtC,IAAI,CAAC,GAAG,CAAC,KAAK,mBAAmB,IAAI,CAAC,MAAM,EAAE,MAAM;iBAEpD,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,kBAAkB,EAAE,MAAM;QACrF;IACJ;IACA;;;KAGC,GACD,SAAS,IAAI,EAAE;QACX,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG;QACvB,IAAI,KAAK,MAAM,KAAK,GAChB,OAAO,IAAI,CAAC,MAAM,CAAC;QACvB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;QAC3B,IAAI,SAAS,YAAY,CAAC,OACtB,OAAO,KAAK,QAAQ,CAAC;aAErB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,kBAAkB,EAAE,MAAM;IACrF;IACA;;;;KAIC,GACD,MAAM,IAAI,EAAE,UAAU,EAAE;QACpB,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG;QACvB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;QAC3B,IAAI,KAAK,MAAM,KAAK,GAChB,OAAO,CAAC,cAAc,SAAS,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG;aAE7D,OAAO,SAAS,YAAY,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,cAAc;IAC5E;IACA,iBAAiB,WAAW,EAAE;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACpB,IAAI,CAAC,SAAS,MAAM,CAAC,OACjB,OAAO;YACX,MAAM,IAAI,KAAK,KAAK;YACpB,OAAQ,KAAK,QACR,eACG,SAAS,QAAQ,CAAC,MAClB,EAAE,KAAK,IAAI,QACX,CAAC,EAAE,aAAa,IAChB,CAAC,EAAE,OAAO,IACV,CAAC,EAAE,GAAG;QAClB;IACJ;IACA;;KAEC,GACD,MAAM,IAAI,EAAE;QACR,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG;QACvB,IAAI,KAAK,MAAM,KAAK,GAChB,OAAO,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;QAC3B,OAAO,SAAS,YAAY,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;IAC5D;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE,KAAK,EAAE;QACf,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG;QACvB,IAAI,KAAK,MAAM,KAAK,GAAG;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK;QAClB,OACK;YACD,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;YAC3B,IAAI,SAAS,YAAY,CAAC,OACtB,KAAK,KAAK,CAAC,MAAM;iBAChB,IAAI,SAAS,aAAa,IAAI,CAAC,MAAM,EACtC,IAAI,CAAC,GAAG,CAAC,KAAK,mBAAmB,IAAI,CAAC,MAAM,EAAE,MAAM;iBAEpD,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,kBAAkB,EAAE,MAAM;QACrF;IACJ;AACJ;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyComment.js"], "sourcesContent": ["'use strict';\n\n/**\n * Stringifies a comment.\n *\n * Empty comment lines are left empty,\n * lines consisting of a single space are replaced by `#`,\n * and all other lines are prefixed with a `#`.\n */\nconst stringifyComment = (str) => str.replace(/^(?!$)(?: $)?/gm, '#');\nfunction indentComment(comment, indent) {\n    if (/^\\n+$/.test(comment))\n        return comment.substring(1);\n    return indent ? comment.replace(/^(?! *$)/gm, indent) : comment;\n}\nconst lineComment = (str, indent, comment) => str.endsWith('\\n')\n    ? indentComment(comment, indent)\n    : comment.includes('\\n')\n        ? '\\n' + indentComment(comment, indent)\n        : (str.endsWith(' ') ? '' : ' ') + comment;\n\nexports.indentComment = indentComment;\nexports.lineComment = lineComment;\nexports.stringifyComment = stringifyComment;\n"], "names": [], "mappings": "AAEA;;;;;;CAMC,GACD,MAAM,mBAAmB,CAAC,MAAQ,IAAI,OAAO,CAAC,mBAAmB;AACjE,SAAS,cAAc,OAAO,EAAE,MAAM;IAClC,IAAI,QAAQ,IAAI,CAAC,UACb,OAAO,QAAQ,SAAS,CAAC;IAC7B,OAAO,SAAS,QAAQ,OAAO,CAAC,cAAc,UAAU;AAC5D;AACA,MAAM,cAAc,CAAC,KAAK,QAAQ,UAAY,IAAI,QAAQ,CAAC,QACrD,cAAc,SAAS,UACvB,QAAQ,QAAQ,CAAC,QACb,OAAO,cAAc,SAAS,UAC9B,CAAC,IAAI,QAAQ,CAAC,OAAO,KAAK,GAAG,IAAI;AAE3C,QAAQ,aAAa,GAAG;AACxB,QAAQ,WAAW,GAAG;AACtB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/foldFlowLines.js"], "sourcesContent": ["'use strict';\n\nconst FOLD_FLOW = 'flow';\nconst FOLD_BLOCK = 'block';\nconst FOLD_QUOTED = 'quoted';\n/**\n * Tries to keep input at up to `lineWidth` characters, splitting only on spaces\n * not followed by newlines or spaces unless `mode` is `'quoted'`. Lines are\n * terminated with `\\n` and started with `indent`.\n */\nfunction foldFlowLines(text, indent, mode = 'flow', { indentAtStart, lineWidth = 80, minContentWidth = 20, onFold, onOverflow } = {}) {\n    if (!lineWidth || lineWidth < 0)\n        return text;\n    if (lineWidth < minContentWidth)\n        minContentWidth = 0;\n    const endStep = Math.max(1 + minContentWidth, 1 + lineWidth - indent.length);\n    if (text.length <= endStep)\n        return text;\n    const folds = [];\n    const escapedFolds = {};\n    let end = lineWidth - indent.length;\n    if (typeof indentAtStart === 'number') {\n        if (indentAtStart > lineWidth - Math.max(2, minContentWidth))\n            folds.push(0);\n        else\n            end = lineWidth - indentAtStart;\n    }\n    let split = undefined;\n    let prev = undefined;\n    let overflow = false;\n    let i = -1;\n    let escStart = -1;\n    let escEnd = -1;\n    if (mode === FOLD_BLOCK) {\n        i = consumeMoreIndentedLines(text, i, indent.length);\n        if (i !== -1)\n            end = i + endStep;\n    }\n    for (let ch; (ch = text[(i += 1)]);) {\n        if (mode === FOLD_QUOTED && ch === '\\\\') {\n            escStart = i;\n            switch (text[i + 1]) {\n                case 'x':\n                    i += 3;\n                    break;\n                case 'u':\n                    i += 5;\n                    break;\n                case 'U':\n                    i += 9;\n                    break;\n                default:\n                    i += 1;\n            }\n            escEnd = i;\n        }\n        if (ch === '\\n') {\n            if (mode === FOLD_BLOCK)\n                i = consumeMoreIndentedLines(text, i, indent.length);\n            end = i + indent.length + endStep;\n            split = undefined;\n        }\n        else {\n            if (ch === ' ' &&\n                prev &&\n                prev !== ' ' &&\n                prev !== '\\n' &&\n                prev !== '\\t') {\n                // space surrounded by non-space can be replaced with newline + indent\n                const next = text[i + 1];\n                if (next && next !== ' ' && next !== '\\n' && next !== '\\t')\n                    split = i;\n            }\n            if (i >= end) {\n                if (split) {\n                    folds.push(split);\n                    end = split + endStep;\n                    split = undefined;\n                }\n                else if (mode === FOLD_QUOTED) {\n                    // white-space collected at end may stretch past lineWidth\n                    while (prev === ' ' || prev === '\\t') {\n                        prev = ch;\n                        ch = text[(i += 1)];\n                        overflow = true;\n                    }\n                    // Account for newline escape, but don't break preceding escape\n                    const j = i > escEnd + 1 ? i - 2 : escStart - 1;\n                    // Bail out if lineWidth & minContentWidth are shorter than an escape string\n                    if (escapedFolds[j])\n                        return text;\n                    folds.push(j);\n                    escapedFolds[j] = true;\n                    end = j + endStep;\n                    split = undefined;\n                }\n                else {\n                    overflow = true;\n                }\n            }\n        }\n        prev = ch;\n    }\n    if (overflow && onOverflow)\n        onOverflow();\n    if (folds.length === 0)\n        return text;\n    if (onFold)\n        onFold();\n    let res = text.slice(0, folds[0]);\n    for (let i = 0; i < folds.length; ++i) {\n        const fold = folds[i];\n        const end = folds[i + 1] || text.length;\n        if (fold === 0)\n            res = `\\n${indent}${text.slice(0, end)}`;\n        else {\n            if (mode === FOLD_QUOTED && escapedFolds[fold])\n                res += `${text[fold]}\\\\`;\n            res += `\\n${indent}${text.slice(fold + 1, end)}`;\n        }\n    }\n    return res;\n}\n/**\n * Presumes `i + 1` is at the start of a line\n * @returns index of last newline in more-indented block\n */\nfunction consumeMoreIndentedLines(text, i, indent) {\n    let end = i;\n    let start = i + 1;\n    let ch = text[start];\n    while (ch === ' ' || ch === '\\t') {\n        if (i < start + indent) {\n            ch = text[++i];\n        }\n        else {\n            do {\n                ch = text[++i];\n            } while (ch && ch !== '\\n');\n            end = i;\n            start = i + 1;\n            ch = text[start];\n        }\n    }\n    return end;\n}\n\nexports.FOLD_BLOCK = FOLD_BLOCK;\nexports.FOLD_FLOW = FOLD_FLOW;\nexports.FOLD_QUOTED = FOLD_QUOTED;\nexports.foldFlowLines = foldFlowLines;\n"], "names": [], "mappings": "AAEA,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB;;;;CAIC,GACD,SAAS,cAAc,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,EAAE,kBAAkB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IAChI,IAAI,CAAC,aAAa,YAAY,GAC1B,OAAO;IACX,IAAI,YAAY,iBACZ,kBAAkB;IACtB,MAAM,UAAU,KAAK,GAAG,CAAC,IAAI,iBAAiB,IAAI,YAAY,OAAO,MAAM;IAC3E,IAAI,KAAK,MAAM,IAAI,SACf,OAAO;IACX,MAAM,QAAQ,EAAE;IAChB,MAAM,eAAe,CAAC;IACtB,IAAI,MAAM,YAAY,OAAO,MAAM;IACnC,IAAI,OAAO,kBAAkB,UAAU;QACnC,IAAI,gBAAgB,YAAY,KAAK,GAAG,CAAC,GAAG,kBACxC,MAAM,IAAI,CAAC;aAEX,MAAM,YAAY;IAC1B;IACA,IAAI,QAAQ;IACZ,IAAI,OAAO;IACX,IAAI,WAAW;IACf,IAAI,IAAI,CAAC;IACT,IAAI,WAAW,CAAC;IAChB,IAAI,SAAS,CAAC;IACd,IAAI,SAAS,YAAY;QACrB,IAAI,yBAAyB,MAAM,GAAG,OAAO,MAAM;QACnD,IAAI,MAAM,CAAC,GACP,MAAM,IAAI;IAClB;IACA,IAAK,IAAI,IAAK,KAAK,IAAI,CAAE,KAAK,EAAG,EAAI;QACjC,IAAI,SAAS,eAAe,OAAO,MAAM;YACrC,WAAW;YACX,OAAQ,IAAI,CAAC,IAAI,EAAE;gBACf,KAAK;oBACD,KAAK;oBACL;gBACJ,KAAK;oBACD,KAAK;oBACL;gBACJ,KAAK;oBACD,KAAK;oBACL;gBACJ;oBACI,KAAK;YACb;YACA,SAAS;QACb;QACA,IAAI,OAAO,MAAM;YACb,IAAI,SAAS,YACT,IAAI,yBAAyB,MAAM,GAAG,OAAO,MAAM;YACvD,MAAM,IAAI,OAAO,MAAM,GAAG;YAC1B,QAAQ;QACZ,OACK;YACD,IAAI,OAAO,OACP,QACA,SAAS,OACT,SAAS,QACT,SAAS,MAAM;gBACf,sEAAsE;gBACtE,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE;gBACxB,IAAI,QAAQ,SAAS,OAAO,SAAS,QAAQ,SAAS,MAClD,QAAQ;YAChB;YACA,IAAI,KAAK,KAAK;gBACV,IAAI,OAAO;oBACP,MAAM,IAAI,CAAC;oBACX,MAAM,QAAQ;oBACd,QAAQ;gBACZ,OACK,IAAI,SAAS,aAAa;oBAC3B,0DAA0D;oBAC1D,MAAO,SAAS,OAAO,SAAS,KAAM;wBAClC,OAAO;wBACP,KAAK,IAAI,CAAE,KAAK,EAAG;wBACnB,WAAW;oBACf;oBACA,+DAA+D;oBAC/D,MAAM,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,WAAW;oBAC9C,4EAA4E;oBAC5E,IAAI,YAAY,CAAC,EAAE,EACf,OAAO;oBACX,MAAM,IAAI,CAAC;oBACX,YAAY,CAAC,EAAE,GAAG;oBAClB,MAAM,IAAI;oBACV,QAAQ;gBACZ,OACK;oBACD,WAAW;gBACf;YACJ;QACJ;QACA,OAAO;IACX;IACA,IAAI,YAAY,YACZ;IACJ,IAAI,MAAM,MAAM,KAAK,GACjB,OAAO;IACX,IAAI,QACA;IACJ,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,KAAK,MAAM;QACvC,IAAI,SAAS,GACT,MAAM,CAAC,EAAE,EAAE,SAAS,KAAK,KAAK,CAAC,GAAG,MAAM;aACvC;YACD,IAAI,SAAS,eAAe,YAAY,CAAC,KAAK,EAC1C,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,EAAE,EAAE,SAAS,KAAK,KAAK,CAAC,OAAO,GAAG,MAAM;QACpD;IACJ;IACA,OAAO;AACX;AACA;;;CAGC,GACD,SAAS,yBAAyB,IAAI,EAAE,CAAC,EAAE,MAAM;IAC7C,IAAI,MAAM;IACV,IAAI,QAAQ,IAAI;IAChB,IAAI,KAAK,IAAI,CAAC,MAAM;IACpB,MAAO,OAAO,OAAO,OAAO,KAAM;QAC9B,IAAI,IAAI,QAAQ,QAAQ;YACpB,KAAK,IAAI,CAAC,EAAE,EAAE;QAClB,OACK;YACD,GAAG;gBACC,KAAK,IAAI,CAAC,EAAE,EAAE;YAClB,QAAS,MAAM,OAAO,KAAM;YAC5B,MAAM;YACN,QAAQ,IAAI;YACZ,KAAK,IAAI,CAAC,MAAM;QACpB;IACJ;IACA,OAAO;AACX;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG;AACpB,QAAQ,WAAW,GAAG;AACtB,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyString.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\nvar foldFlowLines = require('./foldFlowLines.js');\n\nconst getFoldOptions = (ctx, isBlock) => ({\n    indentAtStart: isBlock ? ctx.indent.length : ctx.indentAtStart,\n    lineWidth: ctx.options.lineWidth,\n    minContentWidth: ctx.options.minContentWidth\n});\n// Also checks for lines starting with %, as parsing the output as YAML 1.1 will\n// presume that's starting a new document.\nconst containsDocumentMarker = (str) => /^(%|---|\\.\\.\\.)/m.test(str);\nfunction lineLengthOverLimit(str, lineWidth, indentLength) {\n    if (!lineWidth || lineWidth < 0)\n        return false;\n    const limit = lineWidth - indentLength;\n    const strLen = str.length;\n    if (strLen <= limit)\n        return false;\n    for (let i = 0, start = 0; i < strLen; ++i) {\n        if (str[i] === '\\n') {\n            if (i - start > limit)\n                return true;\n            start = i + 1;\n            if (strLen - start <= limit)\n                return false;\n        }\n    }\n    return true;\n}\nfunction doubleQuotedString(value, ctx) {\n    const json = JSON.stringify(value);\n    if (ctx.options.doubleQuotedAsJSON)\n        return json;\n    const { implicitKey } = ctx;\n    const minMultiLineLength = ctx.options.doubleQuotedMinMultiLineLength;\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    let str = '';\n    let start = 0;\n    for (let i = 0, ch = json[i]; ch; ch = json[++i]) {\n        if (ch === ' ' && json[i + 1] === '\\\\' && json[i + 2] === 'n') {\n            // space before newline needs to be escaped to not be folded\n            str += json.slice(start, i) + '\\\\ ';\n            i += 1;\n            start = i;\n            ch = '\\\\';\n        }\n        if (ch === '\\\\')\n            switch (json[i + 1]) {\n                case 'u':\n                    {\n                        str += json.slice(start, i);\n                        const code = json.substr(i + 2, 4);\n                        switch (code) {\n                            case '0000':\n                                str += '\\\\0';\n                                break;\n                            case '0007':\n                                str += '\\\\a';\n                                break;\n                            case '000b':\n                                str += '\\\\v';\n                                break;\n                            case '001b':\n                                str += '\\\\e';\n                                break;\n                            case '0085':\n                                str += '\\\\N';\n                                break;\n                            case '00a0':\n                                str += '\\\\_';\n                                break;\n                            case '2028':\n                                str += '\\\\L';\n                                break;\n                            case '2029':\n                                str += '\\\\P';\n                                break;\n                            default:\n                                if (code.substr(0, 2) === '00')\n                                    str += '\\\\x' + code.substr(2);\n                                else\n                                    str += json.substr(i, 6);\n                        }\n                        i += 5;\n                        start = i + 1;\n                    }\n                    break;\n                case 'n':\n                    if (implicitKey ||\n                        json[i + 2] === '\"' ||\n                        json.length < minMultiLineLength) {\n                        i += 1;\n                    }\n                    else {\n                        // folding will eat first newline\n                        str += json.slice(start, i) + '\\n\\n';\n                        while (json[i + 2] === '\\\\' &&\n                            json[i + 3] === 'n' &&\n                            json[i + 4] !== '\"') {\n                            str += '\\n';\n                            i += 2;\n                        }\n                        str += indent;\n                        // space after newline needs to be escaped to not be folded\n                        if (json[i + 2] === ' ')\n                            str += '\\\\';\n                        i += 1;\n                        start = i + 1;\n                    }\n                    break;\n                default:\n                    i += 1;\n            }\n    }\n    str = start ? str + json.slice(start) : json;\n    return implicitKey\n        ? str\n        : foldFlowLines.foldFlowLines(str, indent, foldFlowLines.FOLD_QUOTED, getFoldOptions(ctx, false));\n}\nfunction singleQuotedString(value, ctx) {\n    if (ctx.options.singleQuote === false ||\n        (ctx.implicitKey && value.includes('\\n')) ||\n        /[ \\t]\\n|\\n[ \\t]/.test(value) // single quoted string can't have leading or trailing whitespace around newline\n    )\n        return doubleQuotedString(value, ctx);\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    const res = \"'\" + value.replace(/'/g, \"''\").replace(/\\n+/g, `$&\\n${indent}`) + \"'\";\n    return ctx.implicitKey\n        ? res\n        : foldFlowLines.foldFlowLines(res, indent, foldFlowLines.FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction quotedString(value, ctx) {\n    const { singleQuote } = ctx.options;\n    let qs;\n    if (singleQuote === false)\n        qs = doubleQuotedString;\n    else {\n        const hasDouble = value.includes('\"');\n        const hasSingle = value.includes(\"'\");\n        if (hasDouble && !hasSingle)\n            qs = singleQuotedString;\n        else if (hasSingle && !hasDouble)\n            qs = doubleQuotedString;\n        else\n            qs = singleQuote ? singleQuotedString : doubleQuotedString;\n    }\n    return qs(value, ctx);\n}\n// The negative lookbehind avoids a polynomial search,\n// but isn't supported yet on Safari: https://caniuse.com/js-regexp-lookbehind\nlet blockEndNewlines;\ntry {\n    blockEndNewlines = new RegExp('(^|(?<!\\n))\\n+(?!\\n|$)', 'g');\n}\ncatch {\n    blockEndNewlines = /\\n+(?!\\n|$)/g;\n}\nfunction blockString({ comment, type, value }, ctx, onComment, onChompKeep) {\n    const { blockQuote, commentString, lineWidth } = ctx.options;\n    // 1. Block can't end in whitespace unless the last line is non-empty.\n    // 2. Strings consisting of only whitespace are best rendered explicitly.\n    if (!blockQuote || /\\n[\\t ]+$/.test(value) || /^\\s*$/.test(value)) {\n        return quotedString(value, ctx);\n    }\n    const indent = ctx.indent ||\n        (ctx.forceBlockIndent || containsDocumentMarker(value) ? '  ' : '');\n    const literal = blockQuote === 'literal'\n        ? true\n        : blockQuote === 'folded' || type === Scalar.Scalar.BLOCK_FOLDED\n            ? false\n            : type === Scalar.Scalar.BLOCK_LITERAL\n                ? true\n                : !lineLengthOverLimit(value, lineWidth, indent.length);\n    if (!value)\n        return literal ? '|\\n' : '>\\n';\n    // determine chomping from whitespace at value end\n    let chomp;\n    let endStart;\n    for (endStart = value.length; endStart > 0; --endStart) {\n        const ch = value[endStart - 1];\n        if (ch !== '\\n' && ch !== '\\t' && ch !== ' ')\n            break;\n    }\n    let end = value.substring(endStart);\n    const endNlPos = end.indexOf('\\n');\n    if (endNlPos === -1) {\n        chomp = '-'; // strip\n    }\n    else if (value === end || endNlPos !== end.length - 1) {\n        chomp = '+'; // keep\n        if (onChompKeep)\n            onChompKeep();\n    }\n    else {\n        chomp = ''; // clip\n    }\n    if (end) {\n        value = value.slice(0, -end.length);\n        if (end[end.length - 1] === '\\n')\n            end = end.slice(0, -1);\n        end = end.replace(blockEndNewlines, `$&${indent}`);\n    }\n    // determine indent indicator from whitespace at value start\n    let startWithSpace = false;\n    let startEnd;\n    let startNlPos = -1;\n    for (startEnd = 0; startEnd < value.length; ++startEnd) {\n        const ch = value[startEnd];\n        if (ch === ' ')\n            startWithSpace = true;\n        else if (ch === '\\n')\n            startNlPos = startEnd;\n        else\n            break;\n    }\n    let start = value.substring(0, startNlPos < startEnd ? startNlPos + 1 : startEnd);\n    if (start) {\n        value = value.substring(start.length);\n        start = start.replace(/\\n+/g, `$&${indent}`);\n    }\n    const indentSize = indent ? '2' : '1'; // root is at -1\n    // Leading | or > is added later\n    let header = (startWithSpace ? indentSize : '') + chomp;\n    if (comment) {\n        header += ' ' + commentString(comment.replace(/ ?[\\r\\n]+/g, ' '));\n        if (onComment)\n            onComment();\n    }\n    if (!literal) {\n        const foldedValue = value\n            .replace(/\\n+/g, '\\n$&')\n            .replace(/(?:^|\\n)([\\t ].*)(?:([\\n\\t ]*)\\n(?![\\n\\t ]))?/g, '$1$2') // more-indented lines aren't folded\n            //                ^ more-ind. ^ empty     ^ capture next empty lines only at end of indent\n            .replace(/\\n+/g, `$&${indent}`);\n        let literalFallback = false;\n        const foldOptions = getFoldOptions(ctx, true);\n        if (blockQuote !== 'folded' && type !== Scalar.Scalar.BLOCK_FOLDED) {\n            foldOptions.onOverflow = () => {\n                literalFallback = true;\n            };\n        }\n        const body = foldFlowLines.foldFlowLines(`${start}${foldedValue}${end}`, indent, foldFlowLines.FOLD_BLOCK, foldOptions);\n        if (!literalFallback)\n            return `>${header}\\n${indent}${body}`;\n    }\n    value = value.replace(/\\n+/g, `$&${indent}`);\n    return `|${header}\\n${indent}${start}${value}${end}`;\n}\nfunction plainString(item, ctx, onComment, onChompKeep) {\n    const { type, value } = item;\n    const { actualString, implicitKey, indent, indentStep, inFlow } = ctx;\n    if ((implicitKey && value.includes('\\n')) ||\n        (inFlow && /[[\\]{},]/.test(value))) {\n        return quotedString(value, ctx);\n    }\n    if (/^[\\n\\t ,[\\]{}#&*!|>'\"%@`]|^[?-]$|^[?-][ \\t]|[\\n:][ \\t]|[ \\t]\\n|[\\n\\t ]#|[\\n\\t :]$/.test(value)) {\n        // not allowed:\n        // - '-' or '?'\n        // - start with an indicator character (except [?:-]) or /[?-] /\n        // - '\\n ', ': ' or ' \\n' anywhere\n        // - '#' not preceded by a non-space char\n        // - end with ' ' or ':'\n        return implicitKey || inFlow || !value.includes('\\n')\n            ? quotedString(value, ctx)\n            : blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (!implicitKey &&\n        !inFlow &&\n        type !== Scalar.Scalar.PLAIN &&\n        value.includes('\\n')) {\n        // Where allowed & type not set explicitly, prefer block style for multiline strings\n        return blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (containsDocumentMarker(value)) {\n        if (indent === '') {\n            ctx.forceBlockIndent = true;\n            return blockString(item, ctx, onComment, onChompKeep);\n        }\n        else if (implicitKey && indent === indentStep) {\n            return quotedString(value, ctx);\n        }\n    }\n    const str = value.replace(/\\n+/g, `$&\\n${indent}`);\n    // Verify that output will be parsed as a string, as e.g. plain numbers and\n    // booleans get parsed with those types in v1.2 (e.g. '42', 'true' & '0.9e-3'),\n    // and others in v1.1.\n    if (actualString) {\n        const test = (tag) => tag.default && tag.tag !== 'tag:yaml.org,2002:str' && tag.test?.test(str);\n        const { compat, tags } = ctx.doc.schema;\n        if (tags.some(test) || compat?.some(test))\n            return quotedString(value, ctx);\n    }\n    return implicitKey\n        ? str\n        : foldFlowLines.foldFlowLines(str, indent, foldFlowLines.FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction stringifyString(item, ctx, onComment, onChompKeep) {\n    const { implicitKey, inFlow } = ctx;\n    const ss = typeof item.value === 'string'\n        ? item\n        : Object.assign({}, item, { value: String(item.value) });\n    let { type } = item;\n    if (type !== Scalar.Scalar.QUOTE_DOUBLE) {\n        // force double quotes on control characters & unpaired surrogates\n        if (/[\\x00-\\x08\\x0b-\\x1f\\x7f-\\x9f\\u{D800}-\\u{DFFF}]/u.test(ss.value))\n            type = Scalar.Scalar.QUOTE_DOUBLE;\n    }\n    const _stringify = (_type) => {\n        switch (_type) {\n            case Scalar.Scalar.BLOCK_FOLDED:\n            case Scalar.Scalar.BLOCK_LITERAL:\n                return implicitKey || inFlow\n                    ? quotedString(ss.value, ctx) // blocks are not valid inside flow containers\n                    : blockString(ss, ctx, onComment, onChompKeep);\n            case Scalar.Scalar.QUOTE_DOUBLE:\n                return doubleQuotedString(ss.value, ctx);\n            case Scalar.Scalar.QUOTE_SINGLE:\n                return singleQuotedString(ss.value, ctx);\n            case Scalar.Scalar.PLAIN:\n                return plainString(ss, ctx, onComment, onChompKeep);\n            default:\n                return null;\n        }\n    };\n    let res = _stringify(type);\n    if (res === null) {\n        const { defaultKeyType, defaultStringType } = ctx.options;\n        const t = (implicitKey && defaultKeyType) || defaultStringType;\n        res = _stringify(t);\n        if (res === null)\n            throw new Error(`Unsupported default string type ${t}`);\n    }\n    return res;\n}\n\nexports.stringifyString = stringifyString;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,iBAAiB,CAAC,KAAK,UAAY,CAAC;QACtC,eAAe,UAAU,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,aAAa;QAC9D,WAAW,IAAI,OAAO,CAAC,SAAS;QAChC,iBAAiB,IAAI,OAAO,CAAC,eAAe;IAChD,CAAC;AACD,gFAAgF;AAChF,0CAA0C;AAC1C,MAAM,yBAAyB,CAAC,MAAQ,mBAAmB,IAAI,CAAC;AAChE,SAAS,oBAAoB,GAAG,EAAE,SAAS,EAAE,YAAY;IACrD,IAAI,CAAC,aAAa,YAAY,GAC1B,OAAO;IACX,MAAM,QAAQ,YAAY;IAC1B,MAAM,SAAS,IAAI,MAAM;IACzB,IAAI,UAAU,OACV,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,QAAQ,EAAE,EAAG;QACxC,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM;YACjB,IAAI,IAAI,QAAQ,OACZ,OAAO;YACX,QAAQ,IAAI;YACZ,IAAI,SAAS,SAAS,OAClB,OAAO;QACf;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,KAAK,EAAE,GAAG;IAClC,MAAM,OAAO,KAAK,SAAS,CAAC;IAC5B,IAAI,IAAI,OAAO,CAAC,kBAAkB,EAC9B,OAAO;IACX,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,qBAAqB,IAAI,OAAO,CAAC,8BAA8B;IACrE,MAAM,SAAS,IAAI,MAAM,IAAI,CAAC,uBAAuB,SAAS,OAAO,EAAE;IACvE,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,CAAE;QAC9C,IAAI,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK;YAC3D,4DAA4D;YAC5D,OAAO,KAAK,KAAK,CAAC,OAAO,KAAK;YAC9B,KAAK;YACL,QAAQ;YACR,KAAK;QACT;QACA,IAAI,OAAO,MACP,OAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK;gBACD;oBACI,OAAO,KAAK,KAAK,CAAC,OAAO;oBACzB,MAAM,OAAO,KAAK,MAAM,CAAC,IAAI,GAAG;oBAChC,OAAQ;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ,KAAK;4BACD,OAAO;4BACP;wBACJ;4BACI,IAAI,KAAK,MAAM,CAAC,GAAG,OAAO,MACtB,OAAO,QAAQ,KAAK,MAAM,CAAC;iCAE3B,OAAO,KAAK,MAAM,CAAC,GAAG;oBAClC;oBACA,KAAK;oBACL,QAAQ,IAAI;gBAChB;gBACA;YACJ,KAAK;gBACD,IAAI,eACA,IAAI,CAAC,IAAI,EAAE,KAAK,OAChB,KAAK,MAAM,GAAG,oBAAoB;oBAClC,KAAK;gBACT,OACK;oBACD,iCAAiC;oBACjC,OAAO,KAAK,KAAK,CAAC,OAAO,KAAK;oBAC9B,MAAO,IAAI,CAAC,IAAI,EAAE,KAAK,QACnB,IAAI,CAAC,IAAI,EAAE,KAAK,OAChB,IAAI,CAAC,IAAI,EAAE,KAAK,IAAK;wBACrB,OAAO;wBACP,KAAK;oBACT;oBACA,OAAO;oBACP,2DAA2D;oBAC3D,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,KAChB,OAAO;oBACX,KAAK;oBACL,QAAQ,IAAI;gBAChB;gBACA;YACJ;gBACI,KAAK;QACb;IACR;IACA,MAAM,QAAQ,MAAM,KAAK,KAAK,CAAC,SAAS;IACxC,OAAO,cACD,MACA,cAAc,aAAa,CAAC,KAAK,QAAQ,cAAc,WAAW,EAAE,eAAe,KAAK;AAClG;AACA,SAAS,mBAAmB,KAAK,EAAE,GAAG;IAClC,IAAI,IAAI,OAAO,CAAC,WAAW,KAAK,SAC3B,IAAI,WAAW,IAAI,MAAM,QAAQ,CAAC,SACnC,kBAAkB,IAAI,CAAC,OAAO,gFAAgF;MAE9G,OAAO,mBAAmB,OAAO;IACrC,MAAM,SAAS,IAAI,MAAM,IAAI,CAAC,uBAAuB,SAAS,OAAO,EAAE;IACvE,MAAM,MAAM,MAAM,MAAM,OAAO,CAAC,MAAM,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,IAAI;IAC/E,OAAO,IAAI,WAAW,GAChB,MACA,cAAc,aAAa,CAAC,KAAK,QAAQ,cAAc,SAAS,EAAE,eAAe,KAAK;AAChG;AACA,SAAS,aAAa,KAAK,EAAE,GAAG;IAC5B,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,OAAO;IACnC,IAAI;IACJ,IAAI,gBAAgB,OAChB,KAAK;SACJ;QACD,MAAM,YAAY,MAAM,QAAQ,CAAC;QACjC,MAAM,YAAY,MAAM,QAAQ,CAAC;QACjC,IAAI,aAAa,CAAC,WACd,KAAK;aACJ,IAAI,aAAa,CAAC,WACnB,KAAK;aAEL,KAAK,cAAc,qBAAqB;IAChD;IACA,OAAO,GAAG,OAAO;AACrB;AACA,sDAAsD;AACtD,8EAA8E;AAC9E,IAAI;AACJ,IAAI;IACA,mBAAmB,IAAI,OAAO,0BAA0B;AAC5D,EACA,OAAM;IACF,mBAAmB;AACvB;AACA,SAAS,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;IACtE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,IAAI,OAAO;IAC5D,sEAAsE;IACtE,yEAAyE;IACzE,IAAI,CAAC,cAAc,YAAY,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,QAAQ;QAC/D,OAAO,aAAa,OAAO;IAC/B;IACA,MAAM,SAAS,IAAI,MAAM,IACrB,CAAC,IAAI,gBAAgB,IAAI,uBAAuB,SAAS,OAAO,EAAE;IACtE,MAAM,UAAU,eAAe,YACzB,OACA,eAAe,YAAY,SAAS,OAAO,MAAM,CAAC,YAAY,GAC1D,QACA,SAAS,OAAO,MAAM,CAAC,aAAa,GAChC,OACA,CAAC,oBAAoB,OAAO,WAAW,OAAO,MAAM;IAClE,IAAI,CAAC,OACD,OAAO,UAAU,QAAQ;IAC7B,kDAAkD;IAClD,IAAI;IACJ,IAAI;IACJ,IAAK,WAAW,MAAM,MAAM,EAAE,WAAW,GAAG,EAAE,SAAU;QACpD,MAAM,KAAK,KAAK,CAAC,WAAW,EAAE;QAC9B,IAAI,OAAO,QAAQ,OAAO,QAAQ,OAAO,KACrC;IACR;IACA,IAAI,MAAM,MAAM,SAAS,CAAC;IAC1B,MAAM,WAAW,IAAI,OAAO,CAAC;IAC7B,IAAI,aAAa,CAAC,GAAG;QACjB,QAAQ,KAAK,QAAQ;IACzB,OACK,IAAI,UAAU,OAAO,aAAa,IAAI,MAAM,GAAG,GAAG;QACnD,QAAQ,KAAK,OAAO;QACpB,IAAI,aACA;IACR,OACK;QACD,QAAQ,IAAI,OAAO;IACvB;IACA,IAAI,KAAK;QACL,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM;QAClC,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,MACxB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC;QACxB,MAAM,IAAI,OAAO,CAAC,kBAAkB,CAAC,EAAE,EAAE,QAAQ;IACrD;IACA,4DAA4D;IAC5D,IAAI,iBAAiB;IACrB,IAAI;IACJ,IAAI,aAAa,CAAC;IAClB,IAAK,WAAW,GAAG,WAAW,MAAM,MAAM,EAAE,EAAE,SAAU;QACpD,MAAM,KAAK,KAAK,CAAC,SAAS;QAC1B,IAAI,OAAO,KACP,iBAAiB;aAChB,IAAI,OAAO,MACZ,aAAa;aAEb;IACR;IACA,IAAI,QAAQ,MAAM,SAAS,CAAC,GAAG,aAAa,WAAW,aAAa,IAAI;IACxE,IAAI,OAAO;QACP,QAAQ,MAAM,SAAS,CAAC,MAAM,MAAM;QACpC,QAAQ,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ;IAC/C;IACA,MAAM,aAAa,SAAS,MAAM,KAAK,gBAAgB;IACvD,gCAAgC;IAChC,IAAI,SAAS,CAAC,iBAAiB,aAAa,EAAE,IAAI;IAClD,IAAI,SAAS;QACT,UAAU,MAAM,cAAc,QAAQ,OAAO,CAAC,cAAc;QAC5D,IAAI,WACA;IACR;IACA,IAAI,CAAC,SAAS;QACV,MAAM,cAAc,MACf,OAAO,CAAC,QAAQ,QAChB,OAAO,CAAC,kDAAkD,QAAQ,oCAAoC;QACvG,0FAA0F;SACzF,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ;QAClC,IAAI,kBAAkB;QACtB,MAAM,cAAc,eAAe,KAAK;QACxC,IAAI,eAAe,YAAY,SAAS,OAAO,MAAM,CAAC,YAAY,EAAE;YAChE,YAAY,UAAU,GAAG;gBACrB,kBAAkB;YACtB;QACJ;QACA,MAAM,OAAO,cAAc,aAAa,CAAC,GAAG,QAAQ,cAAc,KAAK,EAAE,QAAQ,cAAc,UAAU,EAAE;QAC3G,IAAI,CAAC,iBACD,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,SAAS,MAAM;IAC7C;IACA,QAAQ,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ;IAC3C,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,SAAS,QAAQ,QAAQ,KAAK;AACxD;AACA,SAAS,YAAY,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IACxB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAClE,IAAI,AAAC,eAAe,MAAM,QAAQ,CAAC,SAC9B,UAAU,WAAW,IAAI,CAAC,QAAS;QACpC,OAAO,aAAa,OAAO;IAC/B;IACA,IAAI,oFAAoF,IAAI,CAAC,QAAQ;QACjG,eAAe;QACf,eAAe;QACf,gEAAgE;QAChE,kCAAkC;QAClC,yCAAyC;QACzC,wBAAwB;QACxB,OAAO,eAAe,UAAU,CAAC,MAAM,QAAQ,CAAC,QAC1C,aAAa,OAAO,OACpB,YAAY,MAAM,KAAK,WAAW;IAC5C;IACA,IAAI,CAAC,eACD,CAAC,UACD,SAAS,OAAO,MAAM,CAAC,KAAK,IAC5B,MAAM,QAAQ,CAAC,OAAO;QACtB,oFAAoF;QACpF,OAAO,YAAY,MAAM,KAAK,WAAW;IAC7C;IACA,IAAI,uBAAuB,QAAQ;QAC/B,IAAI,WAAW,IAAI;YACf,IAAI,gBAAgB,GAAG;YACvB,OAAO,YAAY,MAAM,KAAK,WAAW;QAC7C,OACK,IAAI,eAAe,WAAW,YAAY;YAC3C,OAAO,aAAa,OAAO;QAC/B;IACJ;IACA,MAAM,MAAM,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ;IACjD,2EAA2E;IAC3E,+EAA+E;IAC/E,sBAAsB;IACtB,IAAI,cAAc;QACd,MAAM,OAAO,CAAC,MAAQ,IAAI,OAAO,IAAI,IAAI,GAAG,KAAK,2BAA2B,IAAI,IAAI,EAAE,KAAK;QAC3F,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,MAAM;QACvC,IAAI,KAAK,IAAI,CAAC,SAAS,QAAQ,KAAK,OAChC,OAAO,aAAa,OAAO;IACnC;IACA,OAAO,cACD,MACA,cAAc,aAAa,CAAC,KAAK,QAAQ,cAAc,SAAS,EAAE,eAAe,KAAK;AAChG;AACA,SAAS,gBAAgB,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;IACtD,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG;IAChC,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,WAC3B,OACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE,OAAO,OAAO,KAAK,KAAK;IAAE;IAC1D,IAAI,EAAE,IAAI,EAAE,GAAG;IACf,IAAI,SAAS,OAAO,MAAM,CAAC,YAAY,EAAE;QACrC,kEAAkE;QAClE,IAAI,kDAAkD,IAAI,CAAC,GAAG,KAAK,GAC/D,OAAO,OAAO,MAAM,CAAC,YAAY;IACzC;IACA,MAAM,aAAa,CAAC;QAChB,OAAQ;YACJ,KAAK,OAAO,MAAM,CAAC,YAAY;YAC/B,KAAK,OAAO,MAAM,CAAC,aAAa;gBAC5B,OAAO,eAAe,SAChB,aAAa,GAAG,KAAK,EAAE,KAAK,8CAA8C;mBAC1E,YAAY,IAAI,KAAK,WAAW;YAC1C,KAAK,OAAO,MAAM,CAAC,YAAY;gBAC3B,OAAO,mBAAmB,GAAG,KAAK,EAAE;YACxC,KAAK,OAAO,MAAM,CAAC,YAAY;gBAC3B,OAAO,mBAAmB,GAAG,KAAK,EAAE;YACxC,KAAK,OAAO,MAAM,CAAC,KAAK;gBACpB,OAAO,YAAY,IAAI,KAAK,WAAW;YAC3C;gBACI,OAAO;QACf;IACJ;IACA,IAAI,MAAM,WAAW;IACrB,IAAI,QAAQ,MAAM;QACd,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,IAAI,OAAO;QACzD,MAAM,IAAI,AAAC,eAAe,kBAAmB;QAC7C,MAAM,WAAW;QACjB,IAAI,QAAQ,MACR,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,GAAG;IAC9D;IACA,OAAO;AACX;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringify.js"], "sourcesContent": ["'use strict';\n\nvar anchors = require('../doc/anchors.js');\nvar identity = require('../nodes/identity.js');\nvar stringifyComment = require('./stringifyComment.js');\nvar stringifyString = require('./stringifyString.js');\n\nfunction createStringifyContext(doc, options) {\n    const opt = Object.assign({\n        blockQuote: true,\n        commentString: stringifyComment.stringifyComment,\n        defaultKeyType: null,\n        defaultStringType: 'PLAIN',\n        directives: null,\n        doubleQuotedAsJSON: false,\n        doubleQuotedMinMultiLineLength: 40,\n        falseStr: 'false',\n        flowCollectionPadding: true,\n        indentSeq: true,\n        lineWidth: 80,\n        minContentWidth: 20,\n        nullStr: 'null',\n        simpleKeys: false,\n        singleQuote: null,\n        trueStr: 'true',\n        verifyAliasOrder: true\n    }, doc.schema.toStringOptions, options);\n    let inFlow;\n    switch (opt.collectionStyle) {\n        case 'block':\n            inFlow = false;\n            break;\n        case 'flow':\n            inFlow = true;\n            break;\n        default:\n            inFlow = null;\n    }\n    return {\n        anchors: new Set(),\n        doc,\n        flowCollectionPadding: opt.flowCollectionPadding ? ' ' : '',\n        indent: '',\n        indentStep: typeof opt.indent === 'number' ? ' '.repeat(opt.indent) : '  ',\n        inFlow,\n        options: opt\n    };\n}\nfunction getTagObject(tags, item) {\n    if (item.tag) {\n        const match = tags.filter(t => t.tag === item.tag);\n        if (match.length > 0)\n            return match.find(t => t.format === item.format) ?? match[0];\n    }\n    let tagObj = undefined;\n    let obj;\n    if (identity.isScalar(item)) {\n        obj = item.value;\n        let match = tags.filter(t => t.identify?.(obj));\n        if (match.length > 1) {\n            const testMatch = match.filter(t => t.test);\n            if (testMatch.length > 0)\n                match = testMatch;\n        }\n        tagObj =\n            match.find(t => t.format === item.format) ?? match.find(t => !t.format);\n    }\n    else {\n        obj = item;\n        tagObj = tags.find(t => t.nodeClass && obj instanceof t.nodeClass);\n    }\n    if (!tagObj) {\n        const name = obj?.constructor?.name ?? (obj === null ? 'null' : typeof obj);\n        throw new Error(`Tag not resolved for ${name} value`);\n    }\n    return tagObj;\n}\n// needs to be called before value stringifier to allow for circular anchor refs\nfunction stringifyProps(node, tagObj, { anchors: anchors$1, doc }) {\n    if (!doc.directives)\n        return '';\n    const props = [];\n    const anchor = (identity.isScalar(node) || identity.isCollection(node)) && node.anchor;\n    if (anchor && anchors.anchorIsValid(anchor)) {\n        anchors$1.add(anchor);\n        props.push(`&${anchor}`);\n    }\n    const tag = node.tag ?? (tagObj.default ? null : tagObj.tag);\n    if (tag)\n        props.push(doc.directives.tagString(tag));\n    return props.join(' ');\n}\nfunction stringify(item, ctx, onComment, onChompKeep) {\n    if (identity.isPair(item))\n        return item.toString(ctx, onComment, onChompKeep);\n    if (identity.isAlias(item)) {\n        if (ctx.doc.directives)\n            return item.toString(ctx);\n        if (ctx.resolvedAliases?.has(item)) {\n            throw new TypeError(`Cannot stringify circular structure without alias nodes`);\n        }\n        else {\n            if (ctx.resolvedAliases)\n                ctx.resolvedAliases.add(item);\n            else\n                ctx.resolvedAliases = new Set([item]);\n            item = item.resolve(ctx.doc);\n        }\n    }\n    let tagObj = undefined;\n    const node = identity.isNode(item)\n        ? item\n        : ctx.doc.createNode(item, { onTagObj: o => (tagObj = o) });\n    tagObj ?? (tagObj = getTagObject(ctx.doc.schema.tags, node));\n    const props = stringifyProps(node, tagObj, ctx);\n    if (props.length > 0)\n        ctx.indentAtStart = (ctx.indentAtStart ?? 0) + props.length + 1;\n    const str = typeof tagObj.stringify === 'function'\n        ? tagObj.stringify(node, ctx, onComment, onChompKeep)\n        : identity.isScalar(node)\n            ? stringifyString.stringifyString(node, ctx, onComment, onChompKeep)\n            : node.toString(ctx, onComment, onChompKeep);\n    if (!props)\n        return str;\n    return identity.isScalar(node) || str[0] === '{' || str[0] === '['\n        ? `${props} ${str}`\n        : `${props}\\n${ctx.indent}${str}`;\n}\n\nexports.createStringifyContext = createStringifyContext;\nexports.stringify = stringify;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAuB,GAAG,EAAE,OAAO;IACxC,MAAM,MAAM,OAAO,MAAM,CAAC;QACtB,YAAY;QACZ,eAAe,iBAAiB,gBAAgB;QAChD,gBAAgB;QAChB,mBAAmB;QACnB,YAAY;QACZ,oBAAoB;QACpB,gCAAgC;QAChC,UAAU;QACV,uBAAuB;QACvB,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,aAAa;QACb,SAAS;QACT,kBAAkB;IACtB,GAAG,IAAI,MAAM,CAAC,eAAe,EAAE;IAC/B,IAAI;IACJ,OAAQ,IAAI,eAAe;QACvB,KAAK;YACD,SAAS;YACT;QACJ,KAAK;YACD,SAAS;YACT;QACJ;YACI,SAAS;IACjB;IACA,OAAO;QACH,SAAS,IAAI;QACb;QACA,uBAAuB,IAAI,qBAAqB,GAAG,MAAM;QACzD,QAAQ;QACR,YAAY,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,IAAI,MAAM,IAAI;QACtE;QACA,SAAS;IACb;AACJ;AACA,SAAS,aAAa,IAAI,EAAE,IAAI;IAC5B,IAAI,KAAK,GAAG,EAAE;QACV,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,KAAK,GAAG;QACjD,IAAI,MAAM,MAAM,GAAG,GACf,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,EAAE;IACpE;IACA,IAAI,SAAS;IACb,IAAI;IACJ,IAAI,SAAS,QAAQ,CAAC,OAAO;QACzB,MAAM,KAAK,KAAK;QAChB,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG;QAC1C,IAAI,MAAM,MAAM,GAAG,GAAG;YAClB,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;YAC1C,IAAI,UAAU,MAAM,GAAG,GACnB,QAAQ;QAChB;QACA,SACI,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM;IAC9E,OACK;QACD,MAAM;QACN,SAAS,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,IAAI,eAAe,EAAE,SAAS;IACrE;IACA,IAAI,CAAC,QAAQ;QACT,MAAM,OAAO,KAAK,aAAa,QAAQ,CAAC,QAAQ,OAAO,SAAS,OAAO,GAAG;QAC1E,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,MAAM,CAAC;IACxD;IACA,OAAO;AACX;AACA,gFAAgF;AAChF,SAAS,eAAe,IAAI,EAAE,MAAM,EAAE,EAAE,SAAS,SAAS,EAAE,GAAG,EAAE;IAC7D,IAAI,CAAC,IAAI,UAAU,EACf,OAAO;IACX,MAAM,QAAQ,EAAE;IAChB,MAAM,SAAS,CAAC,SAAS,QAAQ,CAAC,SAAS,SAAS,YAAY,CAAC,KAAK,KAAK,KAAK,MAAM;IACtF,IAAI,UAAU,QAAQ,aAAa,CAAC,SAAS;QACzC,UAAU,GAAG,CAAC;QACd,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ;IAC3B;IACA,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,OAAO,GAAG,OAAO,OAAO,GAAG;IAC3D,IAAI,KACA,MAAM,IAAI,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC;IACxC,OAAO,MAAM,IAAI,CAAC;AACtB;AACA,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;IAChD,IAAI,SAAS,MAAM,CAAC,OAChB,OAAO,KAAK,QAAQ,CAAC,KAAK,WAAW;IACzC,IAAI,SAAS,OAAO,CAAC,OAAO;QACxB,IAAI,IAAI,GAAG,CAAC,UAAU,EAClB,OAAO,KAAK,QAAQ,CAAC;QACzB,IAAI,IAAI,eAAe,EAAE,IAAI,OAAO;YAChC,MAAM,IAAI,UAAU,CAAC,uDAAuD,CAAC;QACjF,OACK;YACD,IAAI,IAAI,eAAe,EACnB,IAAI,eAAe,CAAC,GAAG,CAAC;iBAExB,IAAI,eAAe,GAAG,IAAI,IAAI;gBAAC;aAAK;YACxC,OAAO,KAAK,OAAO,CAAC,IAAI,GAAG;QAC/B;IACJ;IACA,IAAI,SAAS;IACb,MAAM,OAAO,SAAS,MAAM,CAAC,QACvB,OACA,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM;QAAE,UAAU,CAAA,IAAM,SAAS;IAAG;IAC7D,UAAU,CAAC,SAAS,aAAa,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK;IAC3D,MAAM,QAAQ,eAAe,MAAM,QAAQ;IAC3C,IAAI,MAAM,MAAM,GAAG,GACf,IAAI,aAAa,GAAG,CAAC,IAAI,aAAa,IAAI,CAAC,IAAI,MAAM,MAAM,GAAG;IAClE,MAAM,MAAM,OAAO,OAAO,SAAS,KAAK,aAClC,OAAO,SAAS,CAAC,MAAM,KAAK,WAAW,eACvC,SAAS,QAAQ,CAAC,QACd,gBAAgB,eAAe,CAAC,MAAM,KAAK,WAAW,eACtD,KAAK,QAAQ,CAAC,KAAK,WAAW;IACxC,IAAI,CAAC,OACD,OAAO;IACX,OAAO,SAAS,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,MACzD,GAAG,MAAM,CAAC,EAAE,KAAK,GACjB,GAAG,MAAM,EAAE,EAAE,IAAI,MAAM,GAAG,KAAK;AACzC;AAEA,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyPair.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyPair({ key, value }, ctx, onComment, onChompKeep) {\n    const { allNullValues, doc, indent, indentStep, options: { commentString, indentSeq, simpleKeys } } = ctx;\n    let keyComment = (identity.isNode(key) && key.comment) || null;\n    if (simpleKeys) {\n        if (keyComment) {\n            throw new Error('With simple keys, key nodes cannot have comments');\n        }\n        if (identity.isCollection(key) || (!identity.isNode(key) && typeof key === 'object')) {\n            const msg = 'With simple keys, collection cannot be used as a key value';\n            throw new Error(msg);\n        }\n    }\n    let explicitKey = !simpleKeys &&\n        (!key ||\n            (keyComment && value == null && !ctx.inFlow) ||\n            identity.isCollection(key) ||\n            (identity.isScalar(key)\n                ? key.type === Scalar.Scalar.BLOCK_FOLDED || key.type === Scalar.Scalar.BLOCK_LITERAL\n                : typeof key === 'object'));\n    ctx = Object.assign({}, ctx, {\n        allNullValues: false,\n        implicitKey: !explicitKey && (simpleKeys || !allNullValues),\n        indent: indent + indentStep\n    });\n    let keyCommentDone = false;\n    let chompKeep = false;\n    let str = stringify.stringify(key, ctx, () => (keyCommentDone = true), () => (chompKeep = true));\n    if (!explicitKey && !ctx.inFlow && str.length > 1024) {\n        if (simpleKeys)\n            throw new Error('With simple keys, single line scalar must not span more than 1024 characters');\n        explicitKey = true;\n    }\n    if (ctx.inFlow) {\n        if (allNullValues || value == null) {\n            if (keyCommentDone && onComment)\n                onComment();\n            return str === '' ? '?' : explicitKey ? `? ${str}` : str;\n        }\n    }\n    else if ((allNullValues && !simpleKeys) || (value == null && explicitKey)) {\n        str = `? ${str}`;\n        if (keyComment && !keyCommentDone) {\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n        }\n        else if (chompKeep && onChompKeep)\n            onChompKeep();\n        return str;\n    }\n    if (keyCommentDone)\n        keyComment = null;\n    if (explicitKey) {\n        if (keyComment)\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n        str = `? ${str}\\n${indent}:`;\n    }\n    else {\n        str = `${str}:`;\n        if (keyComment)\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n    }\n    let vsb, vcb, valueComment;\n    if (identity.isNode(value)) {\n        vsb = !!value.spaceBefore;\n        vcb = value.commentBefore;\n        valueComment = value.comment;\n    }\n    else {\n        vsb = false;\n        vcb = null;\n        valueComment = null;\n        if (value && typeof value === 'object')\n            value = doc.createNode(value);\n    }\n    ctx.implicitKey = false;\n    if (!explicitKey && !keyComment && identity.isScalar(value))\n        ctx.indentAtStart = str.length + 1;\n    chompKeep = false;\n    if (!indentSeq &&\n        indentStep.length >= 2 &&\n        !ctx.inFlow &&\n        !explicitKey &&\n        identity.isSeq(value) &&\n        !value.flow &&\n        !value.tag &&\n        !value.anchor) {\n        // If indentSeq === false, consider '- ' as part of indentation where possible\n        ctx.indent = ctx.indent.substring(2);\n    }\n    let valueCommentDone = false;\n    const valueStr = stringify.stringify(value, ctx, () => (valueCommentDone = true), () => (chompKeep = true));\n    let ws = ' ';\n    if (keyComment || vsb || vcb) {\n        ws = vsb ? '\\n' : '';\n        if (vcb) {\n            const cs = commentString(vcb);\n            ws += `\\n${stringifyComment.indentComment(cs, ctx.indent)}`;\n        }\n        if (valueStr === '' && !ctx.inFlow) {\n            if (ws === '\\n')\n                ws = '\\n\\n';\n        }\n        else {\n            ws += `\\n${ctx.indent}`;\n        }\n    }\n    else if (!explicitKey && identity.isCollection(value)) {\n        const vs0 = valueStr[0];\n        const nl0 = valueStr.indexOf('\\n');\n        const hasNewline = nl0 !== -1;\n        const flow = ctx.inFlow ?? value.flow ?? value.items.length === 0;\n        if (hasNewline || !flow) {\n            let hasPropsLine = false;\n            if (hasNewline && (vs0 === '&' || vs0 === '!')) {\n                let sp0 = valueStr.indexOf(' ');\n                if (vs0 === '&' &&\n                    sp0 !== -1 &&\n                    sp0 < nl0 &&\n                    valueStr[sp0 + 1] === '!') {\n                    sp0 = valueStr.indexOf(' ', sp0 + 1);\n                }\n                if (sp0 === -1 || nl0 < sp0)\n                    hasPropsLine = true;\n            }\n            if (!hasPropsLine)\n                ws = `\\n${ctx.indent}`;\n        }\n    }\n    else if (valueStr === '' || valueStr[0] === '\\n') {\n        ws = '';\n    }\n    str += ws + valueStr;\n    if (ctx.inFlow) {\n        if (valueCommentDone && onComment)\n            onComment();\n    }\n    else if (valueComment && !valueCommentDone) {\n        str += stringifyComment.lineComment(str, ctx.indent, commentString(valueComment));\n    }\n    else if (chompKeep && onChompKeep) {\n        onChompKeep();\n    }\n    return str;\n}\n\nexports.stringifyPair = stringifyPair;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;IAC9D,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,GAAG;IACtG,IAAI,aAAa,AAAC,SAAS,MAAM,CAAC,QAAQ,IAAI,OAAO,IAAK;IAC1D,IAAI,YAAY;QACZ,IAAI,YAAY;YACZ,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,SAAS,YAAY,CAAC,QAAS,CAAC,SAAS,MAAM,CAAC,QAAQ,OAAO,QAAQ,UAAW;YAClF,MAAM,MAAM;YACZ,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,IAAI,cAAc,CAAC,cACf,CAAC,CAAC,OACG,cAAc,SAAS,QAAQ,CAAC,IAAI,MAAM,IAC3C,SAAS,YAAY,CAAC,QACtB,CAAC,SAAS,QAAQ,CAAC,OACb,IAAI,IAAI,KAAK,OAAO,MAAM,CAAC,YAAY,IAAI,IAAI,IAAI,KAAK,OAAO,MAAM,CAAC,aAAa,GACnF,OAAO,QAAQ,QAAQ,CAAC;IACtC,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QACzB,eAAe;QACf,aAAa,CAAC,eAAe,CAAC,cAAc,CAAC,aAAa;QAC1D,QAAQ,SAAS;IACrB;IACA,IAAI,iBAAiB;IACrB,IAAI,YAAY;IAChB,IAAI,MAAM,UAAU,SAAS,CAAC,KAAK,KAAK,IAAO,iBAAiB,MAAO,IAAO,YAAY;IAC1F,IAAI,CAAC,eAAe,CAAC,IAAI,MAAM,IAAI,IAAI,MAAM,GAAG,MAAM;QAClD,IAAI,YACA,MAAM,IAAI,MAAM;QACpB,cAAc;IAClB;IACA,IAAI,IAAI,MAAM,EAAE;QACZ,IAAI,iBAAiB,SAAS,MAAM;YAChC,IAAI,kBAAkB,WAClB;YACJ,OAAO,QAAQ,KAAK,MAAM,cAAc,CAAC,EAAE,EAAE,KAAK,GAAG;QACzD;IACJ,OACK,IAAI,AAAC,iBAAiB,CAAC,cAAgB,SAAS,QAAQ,aAAc;QACvE,MAAM,CAAC,EAAE,EAAE,KAAK;QAChB,IAAI,cAAc,CAAC,gBAAgB;YAC/B,OAAO,iBAAiB,WAAW,CAAC,KAAK,IAAI,MAAM,EAAE,cAAc;QACvE,OACK,IAAI,aAAa,aAClB;QACJ,OAAO;IACX;IACA,IAAI,gBACA,aAAa;IACjB,IAAI,aAAa;QACb,IAAI,YACA,OAAO,iBAAiB,WAAW,CAAC,KAAK,IAAI,MAAM,EAAE,cAAc;QACvE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IAChC,OACK;QACD,MAAM,GAAG,IAAI,CAAC,CAAC;QACf,IAAI,YACA,OAAO,iBAAiB,WAAW,CAAC,KAAK,IAAI,MAAM,EAAE,cAAc;IAC3E;IACA,IAAI,KAAK,KAAK;IACd,IAAI,SAAS,MAAM,CAAC,QAAQ;QACxB,MAAM,CAAC,CAAC,MAAM,WAAW;QACzB,MAAM,MAAM,aAAa;QACzB,eAAe,MAAM,OAAO;IAChC,OACK;QACD,MAAM;QACN,MAAM;QACN,eAAe;QACf,IAAI,SAAS,OAAO,UAAU,UAC1B,QAAQ,IAAI,UAAU,CAAC;IAC/B;IACA,IAAI,WAAW,GAAG;IAClB,IAAI,CAAC,eAAe,CAAC,cAAc,SAAS,QAAQ,CAAC,QACjD,IAAI,aAAa,GAAG,IAAI,MAAM,GAAG;IACrC,YAAY;IACZ,IAAI,CAAC,aACD,WAAW,MAAM,IAAI,KACrB,CAAC,IAAI,MAAM,IACX,CAAC,eACD,SAAS,KAAK,CAAC,UACf,CAAC,MAAM,IAAI,IACX,CAAC,MAAM,GAAG,IACV,CAAC,MAAM,MAAM,EAAE;QACf,8EAA8E;QAC9E,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC;IACtC;IACA,IAAI,mBAAmB;IACvB,MAAM,WAAW,UAAU,SAAS,CAAC,OAAO,KAAK,IAAO,mBAAmB,MAAO,IAAO,YAAY;IACrG,IAAI,KAAK;IACT,IAAI,cAAc,OAAO,KAAK;QAC1B,KAAK,MAAM,OAAO;QAClB,IAAI,KAAK;YACL,MAAM,KAAK,cAAc;YACzB,MAAM,CAAC,EAAE,EAAE,iBAAiB,aAAa,CAAC,IAAI,IAAI,MAAM,GAAG;QAC/D;QACA,IAAI,aAAa,MAAM,CAAC,IAAI,MAAM,EAAE;YAChC,IAAI,OAAO,MACP,KAAK;QACb,OACK;YACD,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,EAAE;QAC3B;IACJ,OACK,IAAI,CAAC,eAAe,SAAS,YAAY,CAAC,QAAQ;QACnD,MAAM,MAAM,QAAQ,CAAC,EAAE;QACvB,MAAM,MAAM,SAAS,OAAO,CAAC;QAC7B,MAAM,aAAa,QAAQ,CAAC;QAC5B,MAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK;QAChE,IAAI,cAAc,CAAC,MAAM;YACrB,IAAI,eAAe;YACnB,IAAI,cAAc,CAAC,QAAQ,OAAO,QAAQ,GAAG,GAAG;gBAC5C,IAAI,MAAM,SAAS,OAAO,CAAC;gBAC3B,IAAI,QAAQ,OACR,QAAQ,CAAC,KACT,MAAM,OACN,QAAQ,CAAC,MAAM,EAAE,KAAK,KAAK;oBAC3B,MAAM,SAAS,OAAO,CAAC,KAAK,MAAM;gBACtC;gBACA,IAAI,QAAQ,CAAC,KAAK,MAAM,KACpB,eAAe;YACvB;YACA,IAAI,CAAC,cACD,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,EAAE;QAC9B;IACJ,OACK,IAAI,aAAa,MAAM,QAAQ,CAAC,EAAE,KAAK,MAAM;QAC9C,KAAK;IACT;IACA,OAAO,KAAK;IACZ,IAAI,IAAI,MAAM,EAAE;QACZ,IAAI,oBAAoB,WACpB;IACR,OACK,IAAI,gBAAgB,CAAC,kBAAkB;QACxC,OAAO,iBAAiB,WAAW,CAAC,KAAK,IAAI,MAAM,EAAE,cAAc;IACvE,OACK,IAAI,aAAa,aAAa;QAC/B;IACJ;IACA,OAAO;AACX;AAEA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/log.js"], "sourcesContent": ["'use strict';\n\nvar node_process = require('process');\n\nfunction debug(logLevel, ...messages) {\n    if (logLevel === 'debug')\n        console.log(...messages);\n}\nfunction warn(logLevel, warning) {\n    if (logLevel === 'debug' || logLevel === 'warn') {\n        if (typeof node_process.emitWarning === 'function')\n            node_process.emitWarning(warning);\n        else\n            console.warn(warning);\n    }\n}\n\nexports.debug = debug;\nexports.warn = warn;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,SAAS,MAAM,QAAQ,EAAE,GAAG,QAAQ;IAChC,IAAI,aAAa,SACb,QAAQ,GAAG,IAAI;AACvB;AACA,SAAS,KAAK,QAAQ,EAAE,OAAO;IAC3B,IAAI,aAAa,WAAW,aAAa,QAAQ;QAC7C,IAAI,OAAO,aAAa,WAAW,KAAK,YACpC,aAAa,WAAW,CAAC;aAEzB,QAAQ,IAAI,CAAC;IACrB;AACJ;AAEA,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/merge.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Scalar = require('../../nodes/Scalar.js');\n\n// If the value associated with a merge key is a single mapping node, each of\n// its key/value pairs is inserted into the current mapping, unless the key\n// already exists in it. If the value associated with the merge key is a\n// sequence, then this sequence is expected to contain mapping nodes and each\n// of these nodes is merged in turn according to its order in the sequence.\n// Keys in mapping nodes earlier in the sequence override keys specified in\n// later mapping nodes. -- http://yaml.org/type/merge.html\nconst MERGE_KEY = '<<';\nconst merge = {\n    identify: value => value === MERGE_KEY ||\n        (typeof value === 'symbol' && value.description === MERGE_KEY),\n    default: 'key',\n    tag: 'tag:yaml.org,2002:merge',\n    test: /^<<$/,\n    resolve: () => Object.assign(new Scalar.Scalar(Symbol(MERGE_KEY)), {\n        addToJSMap: addMergeToJSMap\n    }),\n    stringify: () => MERGE_KEY\n};\nconst isMergeKey = (ctx, key) => (merge.identify(key) ||\n    (identity.isScalar(key) &&\n        (!key.type || key.type === Scalar.Scalar.PLAIN) &&\n        merge.identify(key.value))) &&\n    ctx?.doc.schema.tags.some(tag => tag.tag === merge.tag && tag.default);\nfunction addMergeToJSMap(ctx, map, value) {\n    value = ctx && identity.isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (identity.isSeq(value))\n        for (const it of value.items)\n            mergeValue(ctx, map, it);\n    else if (Array.isArray(value))\n        for (const it of value)\n            mergeValue(ctx, map, it);\n    else\n        mergeValue(ctx, map, value);\n}\nfunction mergeValue(ctx, map, value) {\n    const source = ctx && identity.isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (!identity.isMap(source))\n        throw new Error('Merge sources must be maps or map aliases');\n    const srcMap = source.toJSON(null, ctx, Map);\n    for (const [key, value] of srcMap) {\n        if (map instanceof Map) {\n            if (!map.has(key))\n                map.set(key, value);\n        }\n        else if (map instanceof Set) {\n            map.add(key);\n        }\n        else if (!Object.prototype.hasOwnProperty.call(map, key)) {\n            Object.defineProperty(map, key, {\n                value,\n                writable: true,\n                enumerable: true,\n                configurable: true\n            });\n        }\n    }\n    return map;\n}\n\nexports.addMergeToJSMap = addMergeToJSMap;\nexports.isMergeKey = isMergeKey;\nexports.merge = merge;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,6EAA6E;AAC7E,2EAA2E;AAC3E,wEAAwE;AACxE,6EAA6E;AAC7E,2EAA2E;AAC3E,2EAA2E;AAC3E,0DAA0D;AAC1D,MAAM,YAAY;AAClB,MAAM,QAAQ;IACV,UAAU,CAAA,QAAS,UAAU,aACxB,OAAO,UAAU,YAAY,MAAM,WAAW,KAAK;IACxD,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,IAAM,OAAO,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,OAAO,aAAa;YAC/D,YAAY;QAChB;IACA,WAAW,IAAM;AACrB;AACA,MAAM,aAAa,CAAC,KAAK,MAAQ,CAAC,MAAM,QAAQ,CAAC,QAC5C,SAAS,QAAQ,CAAC,QACf,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO,MAAM,CAAC,KAAK,KAC9C,MAAM,QAAQ,CAAC,IAAI,KAAK,CAAE,KAC9B,KAAK,IAAI,OAAO,KAAK,KAAK,CAAA,MAAO,IAAI,GAAG,KAAK,MAAM,GAAG,IAAI,IAAI,OAAO;AACzE,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACpC,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,IAAI,GAAG,IAAI;IAClE,IAAI,SAAS,KAAK,CAAC,QACf,KAAK,MAAM,MAAM,MAAM,KAAK,CACxB,WAAW,KAAK,KAAK;SACxB,IAAI,MAAM,OAAO,CAAC,QACnB,KAAK,MAAM,MAAM,MACb,WAAW,KAAK,KAAK;SAEzB,WAAW,KAAK,KAAK;AAC7B;AACA,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,KAAK;IAC/B,MAAM,SAAS,OAAO,SAAS,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,IAAI,GAAG,IAAI;IACzE,IAAI,CAAC,SAAS,KAAK,CAAC,SAChB,MAAM,IAAI,MAAM;IACpB,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,KAAK;IACxC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAQ;QAC/B,IAAI,eAAe,KAAK;YACpB,IAAI,CAAC,IAAI,GAAG,CAAC,MACT,IAAI,GAAG,CAAC,KAAK;QACrB,OACK,IAAI,eAAe,KAAK;YACzB,IAAI,GAAG,CAAC;QACZ,OACK,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACtD,OAAO,cAAc,CAAC,KAAK,KAAK;gBAC5B;gBACA,UAAU;gBACV,YAAY;gBACZ,cAAc;YAClB;QACJ;IACJ;IACA,OAAO;AACX;AAEA,QAAQ,eAAe,GAAG;AAC1B,QAAQ,UAAU,GAAG;AACrB,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/addPairToJSMap.js"], "sourcesContent": ["'use strict';\n\nvar log = require('../log.js');\nvar merge = require('../schema/yaml-1.1/merge.js');\nvar stringify = require('../stringify/stringify.js');\nvar identity = require('./identity.js');\nvar toJS = require('./toJS.js');\n\nfunction addPairToJSMap(ctx, map, { key, value }) {\n    if (identity.isNode(key) && key.addToJSMap)\n        key.addToJSMap(ctx, map, value);\n    // TODO: Should drop this special case for bare << handling\n    else if (merge.isMergeKey(ctx, key))\n        merge.addMergeToJSMap(ctx, map, value);\n    else {\n        const jsKey = toJS.toJS(key, '', ctx);\n        if (map instanceof Map) {\n            map.set(jsKey, toJS.toJS(value, jsKey, ctx));\n        }\n        else if (map instanceof Set) {\n            map.add(jsKey);\n        }\n        else {\n            const stringKey = stringifyKey(key, jsKey, ctx);\n            const jsValue = toJS.toJS(value, stringKey, ctx);\n            if (stringKey in map)\n                Object.defineProperty(map, stringKey, {\n                    value: jsValue,\n                    writable: true,\n                    enumerable: true,\n                    configurable: true\n                });\n            else\n                map[stringKey] = jsValue;\n        }\n    }\n    return map;\n}\nfunction stringifyKey(key, jsKey, ctx) {\n    if (jsKey === null)\n        return '';\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (typeof jsKey !== 'object')\n        return String(jsKey);\n    if (identity.isNode(key) && ctx?.doc) {\n        const strCtx = stringify.createStringifyContext(ctx.doc, {});\n        strCtx.anchors = new Set();\n        for (const node of ctx.anchors.keys())\n            strCtx.anchors.add(node.anchor);\n        strCtx.inFlow = true;\n        strCtx.inStringifyKey = true;\n        const strKey = key.toString(strCtx);\n        if (!ctx.mapKeyWarned) {\n            let jsonStr = JSON.stringify(strKey);\n            if (jsonStr.length > 40)\n                jsonStr = jsonStr.substring(0, 36) + '...\"';\n            log.warn(ctx.doc.options.logLevel, `Keys with collection values will be stringified due to JS Object restrictions: ${jsonStr}. Set mapAsMap: true to use object keys.`);\n            ctx.mapKeyWarned = true;\n        }\n        return strKey;\n    }\n    return JSON.stringify(jsKey);\n}\n\nexports.addPairToJSMap = addPairToJSMap;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;IAC5C,IAAI,SAAS,MAAM,CAAC,QAAQ,IAAI,UAAU,EACtC,IAAI,UAAU,CAAC,KAAK,KAAK;SAExB,IAAI,MAAM,UAAU,CAAC,KAAK,MAC3B,MAAM,eAAe,CAAC,KAAK,KAAK;SAC/B;QACD,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,IAAI;QACjC,IAAI,eAAe,KAAK;YACpB,IAAI,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,OAAO;QAC3C,OACK,IAAI,eAAe,KAAK;YACzB,IAAI,GAAG,CAAC;QACZ,OACK;YACD,MAAM,YAAY,aAAa,KAAK,OAAO;YAC3C,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,WAAW;YAC5C,IAAI,aAAa,KACb,OAAO,cAAc,CAAC,KAAK,WAAW;gBAClC,OAAO;gBACP,UAAU;gBACV,YAAY;gBACZ,cAAc;YAClB;iBAEA,GAAG,CAAC,UAAU,GAAG;QACzB;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,GAAG;IACjC,IAAI,UAAU,MACV,OAAO;IACX,gEAAgE;IAChE,IAAI,OAAO,UAAU,UACjB,OAAO,OAAO;IAClB,IAAI,SAAS,MAAM,CAAC,QAAQ,KAAK,KAAK;QAClC,MAAM,SAAS,UAAU,sBAAsB,CAAC,IAAI,GAAG,EAAE,CAAC;QAC1D,OAAO,OAAO,GAAG,IAAI;QACrB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,IAAI,GAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM;QAClC,OAAO,MAAM,GAAG;QAChB,OAAO,cAAc,GAAG;QACxB,MAAM,SAAS,IAAI,QAAQ,CAAC;QAC5B,IAAI,CAAC,IAAI,YAAY,EAAE;YACnB,IAAI,UAAU,KAAK,SAAS,CAAC;YAC7B,IAAI,QAAQ,MAAM,GAAG,IACjB,UAAU,QAAQ,SAAS,CAAC,GAAG,MAAM;YACzC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,+EAA+E,EAAE,QAAQ,wCAAwC,CAAC;YACtK,IAAI,YAAY,GAAG;QACvB;QACA,OAAO;IACX;IACA,OAAO,KAAK,SAAS,CAAC;AAC1B;AAEA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/Pair.js"], "sourcesContent": ["'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar stringifyPair = require('../stringify/stringifyPair.js');\nvar addPairToJSMap = require('./addPairToJSMap.js');\nvar identity = require('./identity.js');\n\nfunction createPair(key, value, ctx) {\n    const k = createNode.createNode(key, undefined, ctx);\n    const v = createNode.createNode(value, undefined, ctx);\n    return new Pair(k, v);\n}\nclass Pair {\n    constructor(key, value = null) {\n        Object.defineProperty(this, identity.NODE_TYPE, { value: identity.PAIR });\n        this.key = key;\n        this.value = value;\n    }\n    clone(schema) {\n        let { key, value } = this;\n        if (identity.isNode(key))\n            key = key.clone(schema);\n        if (identity.isNode(value))\n            value = value.clone(schema);\n        return new Pair(key, value);\n    }\n    toJSON(_, ctx) {\n        const pair = ctx?.mapAsMap ? new Map() : {};\n        return addPairToJSMap.addPairToJSMap(ctx, pair, this);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        return ctx?.doc\n            ? stringifyPair.stringifyPair(this, ctx, onComment, onChompKeep)\n            : JSON.stringify(this);\n    }\n}\n\nexports.Pair = Pair;\nexports.createPair = createPair;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,WAAW,GAAG,EAAE,KAAK,EAAE,GAAG;IAC/B,MAAM,IAAI,WAAW,UAAU,CAAC,KAAK,WAAW;IAChD,MAAM,IAAI,WAAW,UAAU,CAAC,OAAO,WAAW;IAClD,OAAO,IAAI,KAAK,GAAG;AACvB;AACA,MAAM;IACF,YAAY,GAAG,EAAE,QAAQ,IAAI,CAAE;QAC3B,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,SAAS,EAAE;YAAE,OAAO,SAAS,IAAI;QAAC;QACvE,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,MAAM,MAAM,EAAE;QACV,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI;QACzB,IAAI,SAAS,MAAM,CAAC,MAChB,MAAM,IAAI,KAAK,CAAC;QACpB,IAAI,SAAS,MAAM,CAAC,QAChB,QAAQ,MAAM,KAAK,CAAC;QACxB,OAAO,IAAI,KAAK,KAAK;IACzB;IACA,OAAO,CAAC,EAAE,GAAG,EAAE;QACX,MAAM,OAAO,KAAK,WAAW,IAAI,QAAQ,CAAC;QAC1C,OAAO,eAAe,cAAc,CAAC,KAAK,MAAM,IAAI;IACxD;IACA,SAAS,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;QAClC,OAAO,KAAK,MACN,cAAc,aAAa,CAAC,IAAI,EAAE,KAAK,WAAW,eAClD,KAAK,SAAS,CAAC,IAAI;IAC7B;AACJ;AAEA,QAAQ,IAAI,GAAG;AACf,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyCollection.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyCollection(collection, ctx, options) {\n    const flow = ctx.inFlow ?? collection.flow;\n    const stringify = flow ? stringifyFlowCollection : stringifyBlockCollection;\n    return stringify(collection, ctx, options);\n}\nfunction stringifyBlockCollection({ comment, items }, ctx, { blockItemPrefix, flowChars, itemIndent, onChompKeep, onComment }) {\n    const { indent, options: { commentString } } = ctx;\n    const itemCtx = Object.assign({}, ctx, { indent: itemIndent, type: null });\n    let chompKeep = false; // flag for the preceding node's status\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (identity.isNode(item)) {\n            if (!chompKeep && item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, chompKeep);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (identity.isPair(item)) {\n            const ik = identity.isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (!chompKeep && ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, chompKeep);\n            }\n        }\n        chompKeep = false;\n        let str = stringify.stringify(item, itemCtx, () => (comment = null), () => (chompKeep = true));\n        if (comment)\n            str += stringifyComment.lineComment(str, itemIndent, commentString(comment));\n        if (chompKeep && comment)\n            chompKeep = false;\n        lines.push(blockItemPrefix + str);\n    }\n    let str;\n    if (lines.length === 0) {\n        str = flowChars.start + flowChars.end;\n    }\n    else {\n        str = lines[0];\n        for (let i = 1; i < lines.length; ++i) {\n            const line = lines[i];\n            str += line ? `\\n${indent}${line}` : '\\n';\n        }\n    }\n    if (comment) {\n        str += '\\n' + stringifyComment.indentComment(commentString(comment), indent);\n        if (onComment)\n            onComment();\n    }\n    else if (chompKeep && onChompKeep)\n        onChompKeep();\n    return str;\n}\nfunction stringifyFlowCollection({ items }, ctx, { flowChars, itemIndent }) {\n    const { indent, indentStep, flowCollectionPadding: fcPadding, options: { commentString } } = ctx;\n    itemIndent += indentStep;\n    const itemCtx = Object.assign({}, ctx, {\n        indent: itemIndent,\n        inFlow: true,\n        type: null\n    });\n    let reqNewline = false;\n    let linesAtValue = 0;\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (identity.isNode(item)) {\n            if (item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, false);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (identity.isPair(item)) {\n            const ik = identity.isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, false);\n                if (ik.comment)\n                    reqNewline = true;\n            }\n            const iv = identity.isNode(item.value) ? item.value : null;\n            if (iv) {\n                if (iv.comment)\n                    comment = iv.comment;\n                if (iv.commentBefore)\n                    reqNewline = true;\n            }\n            else if (item.value == null && ik?.comment) {\n                comment = ik.comment;\n            }\n        }\n        if (comment)\n            reqNewline = true;\n        let str = stringify.stringify(item, itemCtx, () => (comment = null));\n        if (i < items.length - 1)\n            str += ',';\n        if (comment)\n            str += stringifyComment.lineComment(str, itemIndent, commentString(comment));\n        if (!reqNewline && (lines.length > linesAtValue || str.includes('\\n')))\n            reqNewline = true;\n        lines.push(str);\n        linesAtValue = lines.length;\n    }\n    const { start, end } = flowChars;\n    if (lines.length === 0) {\n        return start + end;\n    }\n    else {\n        if (!reqNewline) {\n            const len = lines.reduce((sum, line) => sum + line.length + 2, 2);\n            reqNewline = ctx.options.lineWidth > 0 && len > ctx.options.lineWidth;\n        }\n        if (reqNewline) {\n            let str = start;\n            for (const line of lines)\n                str += line ? `\\n${indentStep}${indent}${line}` : '\\n';\n            return `${str}\\n${indent}${end}`;\n        }\n        else {\n            return `${start}${fcPadding}${lines.join(' ')}${fcPadding}${end}`;\n        }\n    }\n}\nfunction addCommentBefore({ indent, options: { commentString } }, lines, comment, chompKeep) {\n    if (comment && chompKeep)\n        comment = comment.replace(/^\\n+/, '');\n    if (comment) {\n        const ic = stringifyComment.indentComment(commentString(comment), indent);\n        lines.push(ic.trimStart()); // Avoid double indent on first line\n    }\n}\n\nexports.stringifyCollection = stringifyCollection;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,oBAAoB,UAAU,EAAE,GAAG,EAAE,OAAO;IACjD,MAAM,OAAO,IAAI,MAAM,IAAI,WAAW,IAAI;IAC1C,MAAM,YAAY,OAAO,0BAA0B;IACnD,OAAO,UAAU,YAAY,KAAK;AACtC;AACA,SAAS,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE;IACzH,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,EAAE,GAAG;IAC/C,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE,QAAQ;QAAY,MAAM;IAAK;IACxE,IAAI,YAAY,OAAO,uCAAuC;IAC9D,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,IAAI,UAAU;QACd,IAAI,SAAS,MAAM,CAAC,OAAO;YACvB,IAAI,CAAC,aAAa,KAAK,WAAW,EAC9B,MAAM,IAAI,CAAC;YACf,iBAAiB,KAAK,OAAO,KAAK,aAAa,EAAE;YACjD,IAAI,KAAK,OAAO,EACZ,UAAU,KAAK,OAAO;QAC9B,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;YAC5B,MAAM,KAAK,SAAS,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;YAClD,IAAI,IAAI;gBACJ,IAAI,CAAC,aAAa,GAAG,WAAW,EAC5B,MAAM,IAAI,CAAC;gBACf,iBAAiB,KAAK,OAAO,GAAG,aAAa,EAAE;YACnD;QACJ;QACA,YAAY;QACZ,IAAI,MAAM,UAAU,SAAS,CAAC,MAAM,SAAS,IAAO,UAAU,MAAO,IAAO,YAAY;QACxF,IAAI,SACA,OAAO,iBAAiB,WAAW,CAAC,KAAK,YAAY,cAAc;QACvE,IAAI,aAAa,SACb,YAAY;QAChB,MAAM,IAAI,CAAC,kBAAkB;IACjC;IACA,IAAI;IACJ,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,UAAU,KAAK,GAAG,UAAU,GAAG;IACzC,OACK;QACD,MAAM,KAAK,CAAC,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC,EAAE,EAAE,SAAS,MAAM,GAAG;QACzC;IACJ;IACA,IAAI,SAAS;QACT,OAAO,OAAO,iBAAiB,aAAa,CAAC,cAAc,UAAU;QACrE,IAAI,WACA;IACR,OACK,IAAI,aAAa,aAClB;IACJ,OAAO;AACX;AACA,SAAS,wBAAwB,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE;IACtE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,uBAAuB,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,EAAE,GAAG;IAC7F,cAAc;IACd,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QACnC,QAAQ;QACR,QAAQ;QACR,MAAM;IACV;IACA,IAAI,aAAa;IACjB,IAAI,eAAe;IACnB,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,IAAI,UAAU;QACd,IAAI,SAAS,MAAM,CAAC,OAAO;YACvB,IAAI,KAAK,WAAW,EAChB,MAAM,IAAI,CAAC;YACf,iBAAiB,KAAK,OAAO,KAAK,aAAa,EAAE;YACjD,IAAI,KAAK,OAAO,EACZ,UAAU,KAAK,OAAO;QAC9B,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;YAC5B,MAAM,KAAK,SAAS,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;YAClD,IAAI,IAAI;gBACJ,IAAI,GAAG,WAAW,EACd,MAAM,IAAI,CAAC;gBACf,iBAAiB,KAAK,OAAO,GAAG,aAAa,EAAE;gBAC/C,IAAI,GAAG,OAAO,EACV,aAAa;YACrB;YACA,MAAM,KAAK,SAAS,MAAM,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;YACtD,IAAI,IAAI;gBACJ,IAAI,GAAG,OAAO,EACV,UAAU,GAAG,OAAO;gBACxB,IAAI,GAAG,aAAa,EAChB,aAAa;YACrB,OACK,IAAI,KAAK,KAAK,IAAI,QAAQ,IAAI,SAAS;gBACxC,UAAU,GAAG,OAAO;YACxB;QACJ;QACA,IAAI,SACA,aAAa;QACjB,IAAI,MAAM,UAAU,SAAS,CAAC,MAAM,SAAS,IAAO,UAAU;QAC9D,IAAI,IAAI,MAAM,MAAM,GAAG,GACnB,OAAO;QACX,IAAI,SACA,OAAO,iBAAiB,WAAW,CAAC,KAAK,YAAY,cAAc;QACvE,IAAI,CAAC,cAAc,CAAC,MAAM,MAAM,GAAG,gBAAgB,IAAI,QAAQ,CAAC,KAAK,GACjE,aAAa;QACjB,MAAM,IAAI,CAAC;QACX,eAAe,MAAM,MAAM;IAC/B;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO,QAAQ;IACnB,OACK;QACD,IAAI,CAAC,YAAY;YACb,MAAM,MAAM,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,GAAG,GAAG;YAC/D,aAAa,IAAI,OAAO,CAAC,SAAS,GAAG,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS;QACzE;QACA,IAAI,YAAY;YACZ,IAAI,MAAM;YACV,KAAK,MAAM,QAAQ,MACf,OAAO,OAAO,CAAC,EAAE,EAAE,aAAa,SAAS,MAAM,GAAG;YACtD,OAAO,GAAG,IAAI,EAAE,EAAE,SAAS,KAAK;QACpC,OACK;YACD,OAAO,GAAG,QAAQ,YAAY,MAAM,IAAI,CAAC,OAAO,YAAY,KAAK;QACrE;IACJ;AACJ;AACA,SAAS,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;IACvF,IAAI,WAAW,WACX,UAAU,QAAQ,OAAO,CAAC,QAAQ;IACtC,IAAI,SAAS;QACT,MAAM,KAAK,iBAAiB,aAAa,CAAC,cAAc,UAAU;QAClE,MAAM,IAAI,CAAC,GAAG,SAAS,KAAK,oCAAoC;IACpE;AACJ;AAEA,QAAQ,mBAAmB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/YAMLMap.js"], "sourcesContent": ["'use strict';\n\nvar stringifyCollection = require('../stringify/stringifyCollection.js');\nvar addPairToJSMap = require('./addPairToJSMap.js');\nvar Collection = require('./Collection.js');\nvar identity = require('./identity.js');\nvar Pair = require('./Pair.js');\nvar Scalar = require('./Scalar.js');\n\nfunction findPair(items, key) {\n    const k = identity.isScalar(key) ? key.value : key;\n    for (const it of items) {\n        if (identity.isPair(it)) {\n            if (it.key === key || it.key === k)\n                return it;\n            if (identity.isScalar(it.key) && it.key.value === k)\n                return it;\n        }\n    }\n    return undefined;\n}\nclass YAMLMap extends Collection.Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:map';\n    }\n    constructor(schema) {\n        super(identity.MAP, schema);\n        this.items = [];\n    }\n    /**\n     * A generic collection parsing method that can be extended\n     * to other node classes that inherit from YAMLMap\n     */\n    static from(schema, obj, ctx) {\n        const { keepUndefined, replacer } = ctx;\n        const map = new this(schema);\n        const add = (key, value) => {\n            if (typeof replacer === 'function')\n                value = replacer.call(obj, key, value);\n            else if (Array.isArray(replacer) && !replacer.includes(key))\n                return;\n            if (value !== undefined || keepUndefined)\n                map.items.push(Pair.createPair(key, value, ctx));\n        };\n        if (obj instanceof Map) {\n            for (const [key, value] of obj)\n                add(key, value);\n        }\n        else if (obj && typeof obj === 'object') {\n            for (const key of Object.keys(obj))\n                add(key, obj[key]);\n        }\n        if (typeof schema.sortMapEntries === 'function') {\n            map.items.sort(schema.sortMapEntries);\n        }\n        return map;\n    }\n    /**\n     * Adds a value to the collection.\n     *\n     * @param overwrite - If not set `true`, using a key that is already in the\n     *   collection will throw. Otherwise, overwrites the previous value.\n     */\n    add(pair, overwrite) {\n        let _pair;\n        if (identity.isPair(pair))\n            _pair = pair;\n        else if (!pair || typeof pair !== 'object' || !('key' in pair)) {\n            // In TypeScript, this never happens.\n            _pair = new Pair.Pair(pair, pair?.value);\n        }\n        else\n            _pair = new Pair.Pair(pair.key, pair.value);\n        const prev = findPair(this.items, _pair.key);\n        const sortEntries = this.schema?.sortMapEntries;\n        if (prev) {\n            if (!overwrite)\n                throw new Error(`Key ${_pair.key} already set`);\n            // For scalars, keep the old node & its comments and anchors\n            if (identity.isScalar(prev.value) && Scalar.isScalarValue(_pair.value))\n                prev.value.value = _pair.value;\n            else\n                prev.value = _pair.value;\n        }\n        else if (sortEntries) {\n            const i = this.items.findIndex(item => sortEntries(_pair, item) < 0);\n            if (i === -1)\n                this.items.push(_pair);\n            else\n                this.items.splice(i, 0, _pair);\n        }\n        else {\n            this.items.push(_pair);\n        }\n    }\n    delete(key) {\n        const it = findPair(this.items, key);\n        if (!it)\n            return false;\n        const del = this.items.splice(this.items.indexOf(it), 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const it = findPair(this.items, key);\n        const node = it?.value;\n        return (!keepScalar && identity.isScalar(node) ? node.value : node) ?? undefined;\n    }\n    has(key) {\n        return !!findPair(this.items, key);\n    }\n    set(key, value) {\n        this.add(new Pair.Pair(key, value), true);\n    }\n    /**\n     * @param ctx - Conversion context, originally set in Document#toJS()\n     * @param {Class} Type - If set, forces the returned collection type\n     * @returns Instance of Type, Map, or Object\n     */\n    toJSON(_, ctx, Type) {\n        const map = Type ? new Type() : ctx?.mapAsMap ? new Map() : {};\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const item of this.items)\n            addPairToJSMap.addPairToJSMap(ctx, map, item);\n        return map;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        for (const item of this.items) {\n            if (!identity.isPair(item))\n                throw new Error(`Map items must all be pairs; found ${JSON.stringify(item)} instead`);\n        }\n        if (!ctx.allNullValues && this.hasAllNullValues(false))\n            ctx = Object.assign({}, ctx, { allNullValues: true });\n        return stringifyCollection.stringifyCollection(this, ctx, {\n            blockItemPrefix: '',\n            flowChars: { start: '{', end: '}' },\n            itemIndent: ctx.indent || '',\n            onChompKeep,\n            onComment\n        });\n    }\n}\n\nexports.YAMLMap = YAMLMap;\nexports.findPair = findPair;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,SAAS,KAAK,EAAE,GAAG;IACxB,MAAM,IAAI,SAAS,QAAQ,CAAC,OAAO,IAAI,KAAK,GAAG;IAC/C,KAAK,MAAM,MAAM,MAAO;QACpB,IAAI,SAAS,MAAM,CAAC,KAAK;YACrB,IAAI,GAAG,GAAG,KAAK,OAAO,GAAG,GAAG,KAAK,GAC7B,OAAO;YACX,IAAI,SAAS,QAAQ,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK,GAC9C,OAAO;QACf;IACJ;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,WAAW,UAAU;IACvC,WAAW,UAAU;QACjB,OAAO;IACX;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,SAAS,GAAG,EAAE;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;IACnB;IACA;;;KAGC,GACD,OAAO,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;QAC1B,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG;QACpC,MAAM,MAAM,IAAI,IAAI,CAAC;QACrB,MAAM,MAAM,CAAC,KAAK;YACd,IAAI,OAAO,aAAa,YACpB,QAAQ,SAAS,IAAI,CAAC,KAAK,KAAK;iBAC/B,IAAI,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,QAAQ,CAAC,MACnD;YACJ,IAAI,UAAU,aAAa,eACvB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;QACnD;QACA,IAAI,eAAe,KAAK;YACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IACvB,IAAI,KAAK;QACjB,OACK,IAAI,OAAO,OAAO,QAAQ,UAAU;YACrC,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,KAC1B,IAAI,KAAK,GAAG,CAAC,IAAI;QACzB;QACA,IAAI,OAAO,OAAO,cAAc,KAAK,YAAY;YAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,cAAc;QACxC;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,IAAI,IAAI,EAAE,SAAS,EAAE;QACjB,IAAI;QACJ,IAAI,SAAS,MAAM,CAAC,OAChB,QAAQ;aACP,IAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,CAAC,SAAS,IAAI,GAAG;YAC5D,qCAAqC;YACrC,QAAQ,IAAI,KAAK,IAAI,CAAC,MAAM,MAAM;QACtC,OAEI,QAAQ,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,KAAK;QAC9C,MAAM,OAAO,SAAS,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG;QAC3C,MAAM,cAAc,IAAI,CAAC,MAAM,EAAE;QACjC,IAAI,MAAM;YACN,IAAI,CAAC,WACD,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,YAAY,CAAC;YAClD,4DAA4D;YAC5D,IAAI,SAAS,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,GACjE,KAAK,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK;iBAE9B,KAAK,KAAK,GAAG,MAAM,KAAK;QAChC,OACK,IAAI,aAAa;YAClB,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,YAAY,OAAO,QAAQ;YAClE,IAAI,MAAM,CAAC,GACP,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;iBAEhB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACpB;IACJ;IACA,OAAO,GAAG,EAAE;QACR,MAAM,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE;QAChC,IAAI,CAAC,IACD,OAAO;QACX,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK;QACtD,OAAO,IAAI,MAAM,GAAG;IACxB;IACA,IAAI,GAAG,EAAE,UAAU,EAAE;QACjB,MAAM,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE;QAChC,MAAM,OAAO,IAAI;QACjB,OAAO,CAAC,CAAC,cAAc,SAAS,QAAQ,CAAC,QAAQ,KAAK,KAAK,GAAG,IAAI,KAAK;IAC3E;IACA,IAAI,GAAG,EAAE;QACL,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;IAClC;IACA,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,QAAQ;IACxC;IACA;;;;KAIC,GACD,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;QACjB,MAAM,MAAM,OAAO,IAAI,SAAS,KAAK,WAAW,IAAI,QAAQ,CAAC;QAC7D,IAAI,KAAK,UACL,IAAI,QAAQ,CAAC;QACjB,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,CACzB,eAAe,cAAc,CAAC,KAAK,KAAK;QAC5C,OAAO;IACX;IACA,SAAS,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;QAClC,IAAI,CAAC,KACD,OAAO,KAAK,SAAS,CAAC,IAAI;QAC9B,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAE;YAC3B,IAAI,CAAC,SAAS,MAAM,CAAC,OACjB,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,SAAS,CAAC,MAAM,QAAQ,CAAC;QAC5F;QACA,IAAI,CAAC,IAAI,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAC5C,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;YAAE,eAAe;QAAK;QACvD,OAAO,oBAAoB,mBAAmB,CAAC,IAAI,EAAE,KAAK;YACtD,iBAAiB;YACjB,WAAW;gBAAE,OAAO;gBAAK,KAAK;YAAI;YAClC,YAAY,IAAI,MAAM,IAAI;YAC1B;YACA;QACJ;IACJ;AACJ;AAEA,QAAQ,OAAO,GAAG;AAClB,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/common/map.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\n\nconst map = {\n    collection: 'map',\n    default: true,\n    nodeClass: YAMLMap.YAMLMap,\n    tag: 'tag:yaml.org,2002:map',\n    resolve(map, onError) {\n        if (!identity.isMap(map))\n            onError('Expected a mapping for this tag');\n        return map;\n    },\n    createNode: (schema, obj, ctx) => YAMLMap.YAMLMap.from(schema, obj, ctx)\n};\n\nexports.map = map;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,MAAM;IACR,YAAY;IACZ,SAAS;IACT,WAAW,QAAQ,OAAO;IAC1B,KAAK;IACL,SAAQ,GAAG,EAAE,OAAO;QAChB,IAAI,CAAC,SAAS,KAAK,CAAC,MAChB,QAAQ;QACZ,OAAO;IACX;IACA,YAAY,CAAC,QAAQ,KAAK,MAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,QAAQ,KAAK;AACxE;AAEA,QAAQ,GAAG,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/nodes/YAMLSeq.js"], "sourcesContent": ["'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar stringifyCollection = require('../stringify/stringifyCollection.js');\nvar Collection = require('./Collection.js');\nvar identity = require('./identity.js');\nvar Scalar = require('./Scalar.js');\nvar toJS = require('./toJS.js');\n\nclass YAMLSeq extends Collection.Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:seq';\n    }\n    constructor(schema) {\n        super(identity.SEQ, schema);\n        this.items = [];\n    }\n    add(value) {\n        this.items.push(value);\n    }\n    /**\n     * Removes a value from the collection.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     *\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return false;\n        const del = this.items.splice(idx, 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return undefined;\n        const it = this.items[idx];\n        return !keepScalar && identity.isScalar(it) ? it.value : it;\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     */\n    has(key) {\n        const idx = asItemIndex(key);\n        return typeof idx === 'number' && idx < this.items.length;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     *\n     * If `key` does not contain a representation of an integer, this will throw.\n     * It may be wrapped in a `Scalar`.\n     */\n    set(key, value) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            throw new Error(`Expected a valid index, not ${key}.`);\n        const prev = this.items[idx];\n        if (identity.isScalar(prev) && Scalar.isScalarValue(value))\n            prev.value = value;\n        else\n            this.items[idx] = value;\n    }\n    toJSON(_, ctx) {\n        const seq = [];\n        if (ctx?.onCreate)\n            ctx.onCreate(seq);\n        let i = 0;\n        for (const item of this.items)\n            seq.push(toJS.toJS(item, String(i++), ctx));\n        return seq;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        return stringifyCollection.stringifyCollection(this, ctx, {\n            blockItemPrefix: '- ',\n            flowChars: { start: '[', end: ']' },\n            itemIndent: (ctx.indent || '') + '  ',\n            onChompKeep,\n            onComment\n        });\n    }\n    static from(schema, obj, ctx) {\n        const { replacer } = ctx;\n        const seq = new this(schema);\n        if (obj && Symbol.iterator in Object(obj)) {\n            let i = 0;\n            for (let it of obj) {\n                if (typeof replacer === 'function') {\n                    const key = obj instanceof Set ? it : String(i++);\n                    it = replacer.call(obj, key, it);\n                }\n                seq.items.push(createNode.createNode(it, undefined, ctx));\n            }\n        }\n        return seq;\n    }\n}\nfunction asItemIndex(key) {\n    let idx = identity.isScalar(key) ? key.value : key;\n    if (idx && typeof idx === 'string')\n        idx = Number(idx);\n    return typeof idx === 'number' && Number.isInteger(idx) && idx >= 0\n        ? idx\n        : null;\n}\n\nexports.YAMLSeq = YAMLSeq;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,gBAAgB,WAAW,UAAU;IACvC,WAAW,UAAU;QACjB,OAAO;IACX;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,SAAS,GAAG,EAAE;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;IACnB;IACA,IAAI,KAAK,EAAE;QACP,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACpB;IACA;;;;;;;KAOC,GACD,OAAO,GAAG,EAAE;QACR,MAAM,MAAM,YAAY;QACxB,IAAI,OAAO,QAAQ,UACf,OAAO;QACX,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;QACnC,OAAO,IAAI,MAAM,GAAG;IACxB;IACA,IAAI,GAAG,EAAE,UAAU,EAAE;QACjB,MAAM,MAAM,YAAY;QACxB,IAAI,OAAO,QAAQ,UACf,OAAO;QACX,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;QAC1B,OAAO,CAAC,cAAc,SAAS,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG;IAC7D;IACA;;;;;KAKC,GACD,IAAI,GAAG,EAAE;QACL,MAAM,MAAM,YAAY;QACxB,OAAO,OAAO,QAAQ,YAAY,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;IAC7D;IACA;;;;;;KAMC,GACD,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,MAAM,MAAM,YAAY;QACxB,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;QACzD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;QAC5B,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO,aAAa,CAAC,QAChD,KAAK,KAAK,GAAG;aAEb,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IAC1B;IACA,OAAO,CAAC,EAAE,GAAG,EAAE;QACX,MAAM,MAAM,EAAE;QACd,IAAI,KAAK,UACL,IAAI,QAAQ,CAAC;QACjB,IAAI,IAAI;QACR,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,CACzB,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,OAAO,MAAM;QAC1C,OAAO;IACX;IACA,SAAS,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;QAClC,IAAI,CAAC,KACD,OAAO,KAAK,SAAS,CAAC,IAAI;QAC9B,OAAO,oBAAoB,mBAAmB,CAAC,IAAI,EAAE,KAAK;YACtD,iBAAiB;YACjB,WAAW;gBAAE,OAAO;gBAAK,KAAK;YAAI;YAClC,YAAY,CAAC,IAAI,MAAM,IAAI,EAAE,IAAI;YACjC;YACA;QACJ;IACJ;IACA,OAAO,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;QAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM,MAAM,IAAI,IAAI,CAAC;QACrB,IAAI,OAAO,OAAO,QAAQ,IAAI,OAAO,MAAM;YACvC,IAAI,IAAI;YACR,KAAK,IAAI,MAAM,IAAK;gBAChB,IAAI,OAAO,aAAa,YAAY;oBAChC,MAAM,MAAM,eAAe,MAAM,KAAK,OAAO;oBAC7C,KAAK,SAAS,IAAI,CAAC,KAAK,KAAK;gBACjC;gBACA,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,CAAC,IAAI,WAAW;YACxD;QACJ;QACA,OAAO;IACX;AACJ;AACA,SAAS,YAAY,GAAG;IACpB,IAAI,MAAM,SAAS,QAAQ,CAAC,OAAO,IAAI,KAAK,GAAG;IAC/C,IAAI,OAAO,OAAO,QAAQ,UACtB,MAAM,OAAO;IACjB,OAAO,OAAO,QAAQ,YAAY,OAAO,SAAS,CAAC,QAAQ,OAAO,IAC5D,MACA;AACV;AAEA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/common/seq.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\n\nconst seq = {\n    collection: 'seq',\n    default: true,\n    nodeClass: YAMLSeq.YAMLSeq,\n    tag: 'tag:yaml.org,2002:seq',\n    resolve(seq, onError) {\n        if (!identity.isSeq(seq))\n            onError('Expected a sequence for this tag');\n        return seq;\n    },\n    createNode: (schema, obj, ctx) => YAMLSeq.YAMLSeq.from(schema, obj, ctx)\n};\n\nexports.seq = seq;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,MAAM;IACR,YAAY;IACZ,SAAS;IACT,WAAW,QAAQ,OAAO;IAC1B,KAAK;IACL,SAAQ,GAAG,EAAE,OAAO;QAChB,IAAI,CAAC,SAAS,KAAK,CAAC,MAChB,QAAQ;QACZ,OAAO;IACX;IACA,YAAY,CAAC,QAAQ,KAAK,MAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,QAAQ,KAAK;AACxE;AAEA,QAAQ,GAAG,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/common/string.js"], "sourcesContent": ["'use strict';\n\nvar stringifyString = require('../../stringify/stringifyString.js');\n\nconst string = {\n    identify: value => typeof value === 'string',\n    default: true,\n    tag: 'tag:yaml.org,2002:str',\n    resolve: str => str,\n    stringify(item, ctx, onComment, onChompKeep) {\n        ctx = Object.assign({ actualString: true }, ctx);\n        return stringifyString.stringifyString(item, ctx, onComment, onChompKeep);\n    }\n};\n\nexports.string = string;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,SAAS;IACX,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,SAAS,CAAA,MAAO;IAChB,WAAU,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;QACvC,MAAM,OAAO,MAAM,CAAC;YAAE,cAAc;QAAK,GAAG;QAC5C,OAAO,gBAAgB,eAAe,CAAC,MAAM,KAAK,WAAW;IACjE;AACJ;AAEA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/common/null.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nconst nullTag = {\n    identify: value => value == null,\n    createNode: () => new Scalar.Scalar(null),\n    default: true,\n    tag: 'tag:yaml.org,2002:null',\n    test: /^(?:~|[Nn]ull|NULL)?$/,\n    resolve: () => new Scalar.Scalar(null),\n    stringify: ({ source }, ctx) => typeof source === 'string' && nullTag.test.test(source)\n        ? source\n        : ctx.options.nullStr\n};\n\nexports.nullTag = nullTag;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,UAAU;IACZ,UAAU,CAAA,QAAS,SAAS;IAC5B,YAAY,IAAM,IAAI,OAAO,MAAM,CAAC;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,IAAM,IAAI,OAAO,MAAM,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,EAAE,EAAE,MAAQ,OAAO,WAAW,YAAY,QAAQ,IAAI,CAAC,IAAI,CAAC,UAC1E,SACA,IAAI,OAAO,CAAC,OAAO;AAC7B;AAEA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/core/bool.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nconst boolTag = {\n    identify: value => typeof value === 'boolean',\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,\n    resolve: str => new Scalar.Scalar(str[0] === 't' || str[0] === 'T'),\n    stringify({ source, value }, ctx) {\n        if (source && boolTag.test.test(source)) {\n            const sv = source[0] === 't' || source[0] === 'T';\n            if (value === sv)\n                return source;\n        }\n        return value ? ctx.options.trueStr : ctx.options.falseStr;\n    }\n};\n\nexports.boolTag = boolTag;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,UAAU;IACZ,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,CAAA,MAAO,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK;IAC/D,WAAU,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG;QAC5B,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS;YACrC,MAAM,KAAK,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK;YAC9C,IAAI,UAAU,IACV,OAAO;QACf;QACA,OAAO,QAAQ,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ;IAC7D;AACJ;AAEA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyNumber.js"], "sourcesContent": ["'use strict';\n\nfunction stringifyNumber({ format, minFractionDigits, tag, value }) {\n    if (typeof value === 'bigint')\n        return String(value);\n    const num = typeof value === 'number' ? value : Number(value);\n    if (!isFinite(num))\n        return isNaN(num) ? '.nan' : num < 0 ? '-.inf' : '.inf';\n    let n = JSON.stringify(value);\n    if (!format &&\n        minFractionDigits &&\n        (!tag || tag === 'tag:yaml.org,2002:float') &&\n        /^\\d/.test(n)) {\n        let i = n.indexOf('.');\n        if (i < 0) {\n            i = n.length;\n            n += '.';\n        }\n        let d = minFractionDigits - (n.length - i - 1);\n        while (d-- > 0)\n            n += '0';\n    }\n    return n;\n}\n\nexports.stringifyNumber = stringifyNumber;\n"], "names": [], "mappings": "AAEA,SAAS,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,EAAE,KAAK,EAAE;IAC9D,IAAI,OAAO,UAAU,UACjB,OAAO,OAAO;IAClB,MAAM,MAAM,OAAO,UAAU,WAAW,QAAQ,OAAO;IACvD,IAAI,CAAC,SAAS,MACV,OAAO,MAAM,OAAO,SAAS,MAAM,IAAI,UAAU;IACrD,IAAI,IAAI,KAAK,SAAS,CAAC;IACvB,IAAI,CAAC,UACD,qBACA,CAAC,CAAC,OAAO,QAAQ,yBAAyB,KAC1C,MAAM,IAAI,CAAC,IAAI;QACf,IAAI,IAAI,EAAE,OAAO,CAAC;QAClB,IAAI,IAAI,GAAG;YACP,IAAI,EAAE,MAAM;YACZ,KAAK;QACT;QACA,IAAI,IAAI,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAC7C,MAAO,MAAM,EACT,KAAK;IACb;IACA,OAAO;AACX;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/core/float.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: str => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber.stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+(?:\\.[0-9]*)?)[eE][-+]?[0-9]+$/,\n    resolve: str => parseFloat(str),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber.stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+\\.[0-9]*)$/,\n    resolve(str) {\n        const node = new Scalar.Scalar(parseFloat(str));\n        const dot = str.indexOf('.');\n        if (dot !== -1 && str[str.length - 1] === '0')\n            node.minFractionDigits = str.length - dot - 1;\n        return node;\n    },\n    stringify: stringifyNumber.stringifyNumber\n};\n\nexports.float = float;\nexports.floatExp = floatExp;\nexports.floatNaN = floatNaN;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,WAAW;IACb,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,CAAA,MAAO,IAAI,KAAK,CAAC,CAAC,GAAG,WAAW,OAAO,QAC1C,MACA,GAAG,CAAC,EAAE,KAAK,MACP,OAAO,iBAAiB,GACxB,OAAO,iBAAiB;IAClC,WAAW,gBAAgB,eAAe;AAC9C;AACA,MAAM,WAAW;IACb,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAA,MAAO,WAAW;IAC3B,WAAU,IAAI;QACV,MAAM,MAAM,OAAO,KAAK,KAAK;QAC7B,OAAO,SAAS,OAAO,IAAI,aAAa,KAAK,gBAAgB,eAAe,CAAC;IACjF;AACJ;AACA,MAAM,QAAQ;IACV,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAQ,GAAG;QACP,MAAM,OAAO,IAAI,OAAO,MAAM,CAAC,WAAW;QAC1C,MAAM,MAAM,IAAI,OAAO,CAAC;QACxB,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,KACtC,KAAK,iBAAiB,GAAG,IAAI,MAAM,GAAG,MAAM;QAChD,OAAO;IACX;IACA,WAAW,gBAAgB,eAAe;AAC9C;AAEA,QAAQ,KAAK,GAAG;AAChB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/core/int.js"], "sourcesContent": ["'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nconst intResolve = (str, offset, radix, { intAsBigInt }) => (intAsBigInt ? BigInt(str) : parseInt(str.substring(offset), radix));\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value) && value >= 0)\n        return prefix + value.toString(radix);\n    return stringifyNumber.stringifyNumber(node);\n}\nconst intOct = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^0o[0-7]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 8, opt),\n    stringify: node => intStringify(node, 8, '0o')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber.stringifyNumber\n};\nconst intHex = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^0x[0-9a-fA-F]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexports.int = int;\nexports.intHex = intHex;\nexports.intOct = intOct;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,cAAc,CAAC,QAAU,OAAO,UAAU,YAAY,OAAO,SAAS,CAAC;AAC7E,MAAM,aAAa,CAAC,KAAK,QAAQ,OAAO,EAAE,WAAW,EAAE,GAAM,cAAc,OAAO,OAAO,SAAS,IAAI,SAAS,CAAC,SAAS;AACzH,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,MAAM;IACrC,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,IAAI,YAAY,UAAU,SAAS,GAC/B,OAAO,SAAS,MAAM,QAAQ,CAAC;IACnC,OAAO,gBAAgB,eAAe,CAAC;AAC3C;AACA,MAAM,SAAS;IACX,UAAU,CAAA,QAAS,YAAY,UAAU,SAAS;IAClD,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,GAAG;IACvD,WAAW,CAAA,OAAQ,aAAa,MAAM,GAAG;AAC7C;AACA,MAAM,MAAM;IACR,UAAU;IACV,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,IAAI;IACxD,WAAW,gBAAgB,eAAe;AAC9C;AACA,MAAM,SAAS;IACX,UAAU,CAAA,QAAS,YAAY,UAAU,SAAS;IAClD,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,IAAI;IACxD,WAAW,CAAA,OAAQ,aAAa,MAAM,IAAI;AAC9C;AAEA,QAAQ,GAAG,GAAG;AACd,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/core/schema.js"], "sourcesContent": ["'use strict';\n\nvar map = require('../common/map.js');\nvar _null = require('../common/null.js');\nvar seq = require('../common/seq.js');\nvar string = require('../common/string.js');\nvar bool = require('./bool.js');\nvar float = require('./float.js');\nvar int = require('./int.js');\n\nconst schema = [\n    map.map,\n    seq.seq,\n    string.string,\n    _null.nullTag,\n    bool.boolTag,\n    int.intOct,\n    int.int,\n    int.intHex,\n    float.floatNaN,\n    float.floatExp,\n    float.float\n];\n\nexports.schema = schema;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,SAAS;IACX,IAAI,GAAG;IACP,IAAI,GAAG;IACP,OAAO,MAAM;IACb,MAAM,OAAO;IACb,KAAK,OAAO;IACZ,IAAI,MAAM;IACV,IAAI,GAAG;IACP,IAAI,MAAM;IACV,MAAM,QAAQ;IACd,MAAM,QAAQ;IACd,MAAM,KAAK;CACd;AAED,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/json/schema.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar map = require('../common/map.js');\nvar seq = require('../common/seq.js');\n\nfunction intIdentify(value) {\n    return typeof value === 'bigint' || Number.isInteger(value);\n}\nconst stringifyJSON = ({ value }) => JSON.stringify(value);\nconst jsonScalars = [\n    {\n        identify: value => typeof value === 'string',\n        default: true,\n        tag: 'tag:yaml.org,2002:str',\n        resolve: str => str,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => value == null,\n        createNode: () => new Scalar.Scalar(null),\n        default: true,\n        tag: 'tag:yaml.org,2002:null',\n        test: /^null$/,\n        resolve: () => null,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => typeof value === 'boolean',\n        default: true,\n        tag: 'tag:yaml.org,2002:bool',\n        test: /^true$|^false$/,\n        resolve: str => str === 'true',\n        stringify: stringifyJSON\n    },\n    {\n        identify: intIdentify,\n        default: true,\n        tag: 'tag:yaml.org,2002:int',\n        test: /^-?(?:0|[1-9][0-9]*)$/,\n        resolve: (str, _onError, { intAsBigInt }) => intAsBigInt ? BigInt(str) : parseInt(str, 10),\n        stringify: ({ value }) => intIdentify(value) ? value.toString() : JSON.stringify(value)\n    },\n    {\n        identify: value => typeof value === 'number',\n        default: true,\n        tag: 'tag:yaml.org,2002:float',\n        test: /^-?(?:0|[1-9][0-9]*)(?:\\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,\n        resolve: str => parseFloat(str),\n        stringify: stringifyJSON\n    }\n];\nconst jsonError = {\n    default: true,\n    tag: '',\n    test: /^/,\n    resolve(str, onError) {\n        onError(`Unresolved plain scalar ${JSON.stringify(str)}`);\n        return str;\n    }\n};\nconst schema = [map.map, seq.seq].concat(jsonScalars, jsonError);\n\nexports.schema = schema;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,YAAY,KAAK;IACtB,OAAO,OAAO,UAAU,YAAY,OAAO,SAAS,CAAC;AACzD;AACA,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,GAAK,KAAK,SAAS,CAAC;AACpD,MAAM,cAAc;IAChB;QACI,UAAU,CAAA,QAAS,OAAO,UAAU;QACpC,SAAS;QACT,KAAK;QACL,SAAS,CAAA,MAAO;QAChB,WAAW;IACf;IACA;QACI,UAAU,CAAA,QAAS,SAAS;QAC5B,YAAY,IAAM,IAAI,OAAO,MAAM,CAAC;QACpC,SAAS;QACT,KAAK;QACL,MAAM;QACN,SAAS,IAAM;QACf,WAAW;IACf;IACA;QACI,UAAU,CAAA,QAAS,OAAO,UAAU;QACpC,SAAS;QACT,KAAK;QACL,MAAM;QACN,SAAS,CAAA,MAAO,QAAQ;QACxB,WAAW;IACf;IACA;QACI,UAAU;QACV,SAAS;QACT,KAAK;QACL,MAAM;QACN,SAAS,CAAC,KAAK,UAAU,EAAE,WAAW,EAAE,GAAK,cAAc,OAAO,OAAO,SAAS,KAAK;QACvF,WAAW,CAAC,EAAE,KAAK,EAAE,GAAK,YAAY,SAAS,MAAM,QAAQ,KAAK,KAAK,SAAS,CAAC;IACrF;IACA;QACI,UAAU,CAAA,QAAS,OAAO,UAAU;QACpC,SAAS;QACT,KAAK;QACL,MAAM;QACN,SAAS,CAAA,MAAO,WAAW;QAC3B,WAAW;IACf;CACH;AACD,MAAM,YAAY;IACd,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAQ,GAAG,EAAE,OAAO;QAChB,QAAQ,CAAC,wBAAwB,EAAE,KAAK,SAAS,CAAC,MAAM;QACxD,OAAO;IACX;AACJ;AACA,MAAM,SAAS;IAAC,IAAI,GAAG;IAAE,IAAI,GAAG;CAAC,CAAC,MAAM,CAAC,aAAa;AAEtD,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/binary.js"], "sourcesContent": ["'use strict';\n\nvar node_buffer = require('buffer');\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyString = require('../../stringify/stringifyString.js');\n\nconst binary = {\n    identify: value => value instanceof Uint8Array, // <PERSON><PERSON><PERSON> inherits from Uint8Array\n    default: false,\n    tag: 'tag:yaml.org,2002:binary',\n    /**\n     * Returns a Buffer in node and an Uint8Array in browsers\n     *\n     * To use the resulting buffer as an image, you'll want to do something like:\n     *\n     *   const blob = new Blob([buffer], { type: 'image/jpeg' })\n     *   document.querySelector('#photo').src = URL.createObjectURL(blob)\n     */\n    resolve(src, onError) {\n        if (typeof node_buffer.Buffer === 'function') {\n            return node_buffer.Buffer.from(src, 'base64');\n        }\n        else if (typeof atob === 'function') {\n            // On IE 11, atob() can't handle newlines\n            const str = atob(src.replace(/[\\n\\r]/g, ''));\n            const buffer = new Uint8Array(str.length);\n            for (let i = 0; i < str.length; ++i)\n                buffer[i] = str.charCodeAt(i);\n            return buffer;\n        }\n        else {\n            onError('This environment does not support reading binary tags; either Buffer or atob is required');\n            return src;\n        }\n    },\n    stringify({ comment, type, value }, ctx, onComment, onChompKeep) {\n        if (!value)\n            return '';\n        const buf = value; // checked earlier by binary.identify()\n        let str;\n        if (typeof node_buffer.Buffer === 'function') {\n            str =\n                buf instanceof node_buffer.Buffer\n                    ? buf.toString('base64')\n                    : node_buffer.Buffer.from(buf.buffer).toString('base64');\n        }\n        else if (typeof btoa === 'function') {\n            let s = '';\n            for (let i = 0; i < buf.length; ++i)\n                s += String.fromCharCode(buf[i]);\n            str = btoa(s);\n        }\n        else {\n            throw new Error('This environment does not support writing binary tags; either Buffer or btoa is required');\n        }\n        type ?? (type = Scalar.Scalar.BLOCK_LITERAL);\n        if (type !== Scalar.Scalar.QUOTE_DOUBLE) {\n            const lineWidth = Math.max(ctx.options.lineWidth - ctx.indent.length, ctx.options.minContentWidth);\n            const n = Math.ceil(str.length / lineWidth);\n            const lines = new Array(n);\n            for (let i = 0, o = 0; i < n; ++i, o += lineWidth) {\n                lines[i] = str.substr(o, lineWidth);\n            }\n            str = lines.join(type === Scalar.Scalar.BLOCK_LITERAL ? '\\n' : ' ');\n        }\n        return stringifyString.stringifyString({ comment, type, value: str }, ctx, onComment, onChompKeep);\n    }\n};\n\nexports.binary = binary;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,SAAS;IACX,UAAU,CAAA,QAAS,iBAAiB;IACpC,SAAS;IACT,KAAK;IACL;;;;;;;KAOC,GACD,SAAQ,GAAG,EAAE,OAAO;QAChB,IAAI,OAAO,YAAY,MAAM,KAAK,YAAY;YAC1C,OAAO,YAAY,MAAM,CAAC,IAAI,CAAC,KAAK;QACxC,OACK,IAAI,OAAO,SAAS,YAAY;YACjC,yCAAyC;YACzC,MAAM,MAAM,KAAK,IAAI,OAAO,CAAC,WAAW;YACxC,MAAM,SAAS,IAAI,WAAW,IAAI,MAAM;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAC9B,MAAM,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC;YAC/B,OAAO;QACX,OACK;YACD,QAAQ;YACR,OAAO;QACX;IACJ;IACA,WAAU,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW;QAC3D,IAAI,CAAC,OACD,OAAO;QACX,MAAM,MAAM,OAAO,uCAAuC;QAC1D,IAAI;QACJ,IAAI,OAAO,YAAY,MAAM,KAAK,YAAY;YAC1C,MACI,eAAe,YAAY,MAAM,GAC3B,IAAI,QAAQ,CAAC,YACb,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,QAAQ,CAAC;QAC3D,OACK,IAAI,OAAO,SAAS,YAAY;YACjC,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAC9B,KAAK,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE;YACnC,MAAM,KAAK;QACf,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,QAAQ,CAAC,OAAO,OAAO,MAAM,CAAC,aAAa;QAC3C,IAAI,SAAS,OAAO,MAAM,CAAC,YAAY,EAAE;YACrC,MAAM,YAAY,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,OAAO,CAAC,eAAe;YACjG,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,GAAG;YACjC,MAAM,QAAQ,IAAI,MAAM;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,UAAW;gBAC/C,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG;YAC7B;YACA,MAAM,MAAM,IAAI,CAAC,SAAS,OAAO,MAAM,CAAC,aAAa,GAAG,OAAO;QACnE;QACA,OAAO,gBAAgB,eAAe,CAAC;YAAE;YAAS;YAAM,OAAO;QAAI,GAAG,KAAK,WAAW;IAC1F;AACJ;AAEA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/pairs.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Pair = require('../../nodes/Pair.js');\nvar Scalar = require('../../nodes/Scalar.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\n\nfunction resolvePairs(seq, onError) {\n    if (identity.isSeq(seq)) {\n        for (let i = 0; i < seq.items.length; ++i) {\n            let item = seq.items[i];\n            if (identity.isPair(item))\n                continue;\n            else if (identity.isMap(item)) {\n                if (item.items.length > 1)\n                    onError('Each pair must have its own sequence indicator');\n                const pair = item.items[0] || new Pair.Pair(new Scalar.Scalar(null));\n                if (item.commentBefore)\n                    pair.key.commentBefore = pair.key.commentBefore\n                        ? `${item.commentBefore}\\n${pair.key.commentBefore}`\n                        : item.commentBefore;\n                if (item.comment) {\n                    const cn = pair.value ?? pair.key;\n                    cn.comment = cn.comment\n                        ? `${item.comment}\\n${cn.comment}`\n                        : item.comment;\n                }\n                item = pair;\n            }\n            seq.items[i] = identity.isPair(item) ? item : new Pair.Pair(item);\n        }\n    }\n    else\n        onError('Expected a sequence for this tag');\n    return seq;\n}\nfunction createPairs(schema, iterable, ctx) {\n    const { replacer } = ctx;\n    const pairs = new YAMLSeq.YAMLSeq(schema);\n    pairs.tag = 'tag:yaml.org,2002:pairs';\n    let i = 0;\n    if (iterable && Symbol.iterator in Object(iterable))\n        for (let it of iterable) {\n            if (typeof replacer === 'function')\n                it = replacer.call(iterable, String(i++), it);\n            let key, value;\n            if (Array.isArray(it)) {\n                if (it.length === 2) {\n                    key = it[0];\n                    value = it[1];\n                }\n                else\n                    throw new TypeError(`Expected [key, value] tuple: ${it}`);\n            }\n            else if (it && it instanceof Object) {\n                const keys = Object.keys(it);\n                if (keys.length === 1) {\n                    key = keys[0];\n                    value = it[key];\n                }\n                else {\n                    throw new TypeError(`Expected tuple with one key, not ${keys.length} keys`);\n                }\n            }\n            else {\n                key = it;\n            }\n            pairs.items.push(Pair.createPair(key, value, ctx));\n        }\n    return pairs;\n}\nconst pairs = {\n    collection: 'seq',\n    default: false,\n    tag: 'tag:yaml.org,2002:pairs',\n    resolve: resolvePairs,\n    createNode: createPairs\n};\n\nexports.createPairs = createPairs;\nexports.pairs = pairs;\nexports.resolvePairs = resolvePairs;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAa,GAAG,EAAE,OAAO;IAC9B,IAAI,SAAS,KAAK,CAAC,MAAM;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;YACvC,IAAI,OAAO,IAAI,KAAK,CAAC,EAAE;YACvB,IAAI,SAAS,MAAM,CAAC,OAChB;iBACC,IAAI,SAAS,KAAK,CAAC,OAAO;gBAC3B,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GACpB,QAAQ;gBACZ,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,MAAM,CAAC;gBAC9D,IAAI,KAAK,aAAa,EAClB,KAAK,GAAG,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,aAAa,GACzC,GAAG,KAAK,aAAa,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,aAAa,EAAE,GAClD,KAAK,aAAa;gBAC5B,IAAI,KAAK,OAAO,EAAE;oBACd,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG;oBACjC,GAAG,OAAO,GAAG,GAAG,OAAO,GACjB,GAAG,KAAK,OAAO,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,GAChC,KAAK,OAAO;gBACtB;gBACA,OAAO;YACX;YACA,IAAI,KAAK,CAAC,EAAE,GAAG,SAAS,MAAM,CAAC,QAAQ,OAAO,IAAI,KAAK,IAAI,CAAC;QAChE;IACJ,OAEI,QAAQ;IACZ,OAAO;AACX;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,GAAG;IACtC,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,MAAM,QAAQ,IAAI,QAAQ,OAAO,CAAC;IAClC,MAAM,GAAG,GAAG;IACZ,IAAI,IAAI;IACR,IAAI,YAAY,OAAO,QAAQ,IAAI,OAAO,WACtC,KAAK,IAAI,MAAM,SAAU;QACrB,IAAI,OAAO,aAAa,YACpB,KAAK,SAAS,IAAI,CAAC,UAAU,OAAO,MAAM;QAC9C,IAAI,KAAK;QACT,IAAI,MAAM,OAAO,CAAC,KAAK;YACnB,IAAI,GAAG,MAAM,KAAK,GAAG;gBACjB,MAAM,EAAE,CAAC,EAAE;gBACX,QAAQ,EAAE,CAAC,EAAE;YACjB,OAEI,MAAM,IAAI,UAAU,CAAC,6BAA6B,EAAE,IAAI;QAChE,OACK,IAAI,MAAM,cAAc,QAAQ;YACjC,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACnB,MAAM,IAAI,CAAC,EAAE;gBACb,QAAQ,EAAE,CAAC,IAAI;YACnB,OACK;gBACD,MAAM,IAAI,UAAU,CAAC,iCAAiC,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC;YAC9E;QACJ,OACK;YACD,MAAM;QACV;QACA,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;IACjD;IACJ,OAAO;AACX;AACA,MAAM,QAAQ;IACV,YAAY;IACZ,SAAS;IACT,KAAK;IACL,SAAS;IACT,YAAY;AAChB;AAEA,QAAQ,WAAW,GAAG;AACtB,QAAQ,KAAK,GAAG;AAChB,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/omap.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar toJS = require('../../nodes/toJS.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\nvar pairs = require('./pairs.js');\n\nclass YAMLOMap extends YAMLSeq.YAMLSeq {\n    constructor() {\n        super();\n        this.add = YAMLMap.YAMLMap.prototype.add.bind(this);\n        this.delete = YAMLMap.YAMLMap.prototype.delete.bind(this);\n        this.get = YAMLMap.YAMLMap.prototype.get.bind(this);\n        this.has = YAMLMap.YAMLMap.prototype.has.bind(this);\n        this.set = YAMLMap.YAMLMap.prototype.set.bind(this);\n        this.tag = YAMLOMap.tag;\n    }\n    /**\n     * If `ctx` is given, the return type is actually `Map<unknown, unknown>`,\n     * but TypeScript won't allow widening the signature of a child method.\n     */\n    toJSON(_, ctx) {\n        if (!ctx)\n            return super.toJSON(_);\n        const map = new Map();\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const pair of this.items) {\n            let key, value;\n            if (identity.isPair(pair)) {\n                key = toJS.toJS(pair.key, '', ctx);\n                value = toJS.toJS(pair.value, key, ctx);\n            }\n            else {\n                key = toJS.toJS(pair, '', ctx);\n            }\n            if (map.has(key))\n                throw new Error('Ordered maps must not include duplicate keys');\n            map.set(key, value);\n        }\n        return map;\n    }\n    static from(schema, iterable, ctx) {\n        const pairs$1 = pairs.createPairs(schema, iterable, ctx);\n        const omap = new this();\n        omap.items = pairs$1.items;\n        return omap;\n    }\n}\nYAMLOMap.tag = 'tag:yaml.org,2002:omap';\nconst omap = {\n    collection: 'seq',\n    identify: value => value instanceof Map,\n    nodeClass: YAMLOMap,\n    default: false,\n    tag: 'tag:yaml.org,2002:omap',\n    resolve(seq, onError) {\n        const pairs$1 = pairs.resolvePairs(seq, onError);\n        const seenKeys = [];\n        for (const { key } of pairs$1.items) {\n            if (identity.isScalar(key)) {\n                if (seenKeys.includes(key.value)) {\n                    onError(`Ordered maps must not include duplicate keys: ${key.value}`);\n                }\n                else {\n                    seenKeys.push(key.value);\n                }\n            }\n        }\n        return Object.assign(new YAMLOMap(), pairs$1);\n    },\n    createNode: (schema, iterable, ctx) => YAMLOMap.from(schema, iterable, ctx)\n};\n\nexports.YAMLOMap = YAMLOMap;\nexports.omap = omap;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,iBAAiB,QAAQ,OAAO;IAClC,aAAc;QACV,KAAK;QACL,IAAI,CAAC,GAAG,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACxD,IAAI,CAAC,GAAG,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,GAAG,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,GAAG,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;QAClD,IAAI,CAAC,GAAG,GAAG,SAAS,GAAG;IAC3B;IACA;;;KAGC,GACD,OAAO,CAAC,EAAE,GAAG,EAAE;QACX,IAAI,CAAC,KACD,OAAO,KAAK,CAAC,OAAO;QACxB,MAAM,MAAM,IAAI;QAChB,IAAI,KAAK,UACL,IAAI,QAAQ,CAAC;QACjB,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAE;YAC3B,IAAI,KAAK;YACT,IAAI,SAAS,MAAM,CAAC,OAAO;gBACvB,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI;gBAC9B,QAAQ,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK;YACvC,OACK;gBACD,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI;YAC9B;YACA,IAAI,IAAI,GAAG,CAAC,MACR,MAAM,IAAI,MAAM;YACpB,IAAI,GAAG,CAAC,KAAK;QACjB;QACA,OAAO;IACX;IACA,OAAO,KAAK,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE;QAC/B,MAAM,UAAU,MAAM,WAAW,CAAC,QAAQ,UAAU;QACpD,MAAM,OAAO,IAAI,IAAI;QACrB,KAAK,KAAK,GAAG,QAAQ,KAAK;QAC1B,OAAO;IACX;AACJ;AACA,SAAS,GAAG,GAAG;AACf,MAAM,OAAO;IACT,YAAY;IACZ,UAAU,CAAA,QAAS,iBAAiB;IACpC,WAAW;IACX,SAAS;IACT,KAAK;IACL,SAAQ,GAAG,EAAE,OAAO;QAChB,MAAM,UAAU,MAAM,YAAY,CAAC,KAAK;QACxC,MAAM,WAAW,EAAE;QACnB,KAAK,MAAM,EAAE,GAAG,EAAE,IAAI,QAAQ,KAAK,CAAE;YACjC,IAAI,SAAS,QAAQ,CAAC,MAAM;gBACxB,IAAI,SAAS,QAAQ,CAAC,IAAI,KAAK,GAAG;oBAC9B,QAAQ,CAAC,8CAA8C,EAAE,IAAI,KAAK,EAAE;gBACxE,OACK;oBACD,SAAS,IAAI,CAAC,IAAI,KAAK;gBAC3B;YACJ;QACJ;QACA,OAAO,OAAO,MAAM,CAAC,IAAI,YAAY;IACzC;IACA,YAAY,CAAC,QAAQ,UAAU,MAAQ,SAAS,IAAI,CAAC,QAAQ,UAAU;AAC3E;AAEA,QAAQ,QAAQ,GAAG;AACnB,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/bool.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nfunction boolStringify({ value, source }, ctx) {\n    const boolObj = value ? trueTag : falseTag;\n    if (source && boolObj.test.test(source))\n        return source;\n    return value ? ctx.options.trueStr : ctx.options.falseStr;\n}\nconst trueTag = {\n    identify: value => value === true,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,\n    resolve: () => new Scalar.Scalar(true),\n    stringify: boolStringify\n};\nconst falseTag = {\n    identify: value => value === false,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,\n    resolve: () => new Scalar.Scalar(false),\n    stringify: boolStringify\n};\n\nexports.falseTag = falseTag;\nexports.trueTag = trueTag;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,SAAS,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,GAAG;IACzC,MAAM,UAAU,QAAQ,UAAU;IAClC,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,SAC5B,OAAO;IACX,OAAO,QAAQ,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ;AAC7D;AACA,MAAM,UAAU;IACZ,UAAU,CAAA,QAAS,UAAU;IAC7B,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,IAAM,IAAI,OAAO,MAAM,CAAC;IACjC,WAAW;AACf;AACA,MAAM,WAAW;IACb,UAAU,CAAA,QAAS,UAAU;IAC7B,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,IAAM,IAAI,OAAO,MAAM,CAAC;IACjC,WAAW;AACf;AAEA,QAAQ,QAAQ,GAAG;AACnB,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/float.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: (str) => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber.stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:[0-9][0-9_]*)?(?:\\.[0-9_]*)?[eE][-+]?[0-9]+$/,\n    resolve: (str) => parseFloat(str.replace(/_/g, '')),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber.stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:[0-9][0-9_]*)?\\.[0-9_]*$/,\n    resolve(str) {\n        const node = new Scalar.Scalar(parseFloat(str.replace(/_/g, '')));\n        const dot = str.indexOf('.');\n        if (dot !== -1) {\n            const f = str.substring(dot + 1).replace(/_/g, '');\n            if (f[f.length - 1] === '0')\n                node.minFractionDigits = f.length;\n        }\n        return node;\n    },\n    stringify: stringifyNumber.stringifyNumber\n};\n\nexports.float = float;\nexports.floatExp = floatExp;\nexports.floatNaN = floatNaN;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,WAAW;IACb,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,CAAC,MAAQ,IAAI,KAAK,CAAC,CAAC,GAAG,WAAW,OAAO,QAC5C,MACA,GAAG,CAAC,EAAE,KAAK,MACP,OAAO,iBAAiB,GACxB,OAAO,iBAAiB;IAClC,WAAW,gBAAgB,eAAe;AAC9C;AACA,MAAM,WAAW;IACb,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,MAAQ,WAAW,IAAI,OAAO,CAAC,MAAM;IAC/C,WAAU,IAAI;QACV,MAAM,MAAM,OAAO,KAAK,KAAK;QAC7B,OAAO,SAAS,OAAO,IAAI,aAAa,KAAK,gBAAgB,eAAe,CAAC;IACjF;AACJ;AACA,MAAM,QAAQ;IACV,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAQ,GAAG;QACP,MAAM,OAAO,IAAI,OAAO,MAAM,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM;QAC5D,MAAM,MAAM,IAAI,OAAO,CAAC;QACxB,IAAI,QAAQ,CAAC,GAAG;YACZ,MAAM,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;YAC/C,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,KACpB,KAAK,iBAAiB,GAAG,EAAE,MAAM;QACzC;QACA,OAAO;IACX;IACA,WAAW,gBAAgB,eAAe;AAC9C;AAEA,QAAQ,KAAK,GAAG;AAChB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/int.js"], "sourcesContent": ["'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nfunction intResolve(str, offset, radix, { intAsBigInt }) {\n    const sign = str[0];\n    if (sign === '-' || sign === '+')\n        offset += 1;\n    str = str.substring(offset).replace(/_/g, '');\n    if (intAsBigInt) {\n        switch (radix) {\n            case 2:\n                str = `0b${str}`;\n                break;\n            case 8:\n                str = `0o${str}`;\n                break;\n            case 16:\n                str = `0x${str}`;\n                break;\n        }\n        const n = BigInt(str);\n        return sign === '-' ? BigInt(-1) * n : n;\n    }\n    const n = parseInt(str, radix);\n    return sign === '-' ? -1 * n : n;\n}\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value)) {\n        const str = value.toString(radix);\n        return value < 0 ? '-' + prefix + str.substr(1) : prefix + str;\n    }\n    return stringifyNumber.stringifyNumber(node);\n}\nconst intBin = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'BIN',\n    test: /^[-+]?0b[0-1_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 2, opt),\n    stringify: node => intStringify(node, 2, '0b')\n};\nconst intOct = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^[-+]?0[0-7_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 1, 8, opt),\n    stringify: node => intStringify(node, 8, '0')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9][0-9_]*$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber.stringifyNumber\n};\nconst intHex = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^[-+]?0x[0-9a-fA-F_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexports.int = int;\nexports.intBin = intBin;\nexports.intHex = intHex;\nexports.intOct = intOct;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,MAAM,cAAc,CAAC,QAAU,OAAO,UAAU,YAAY,OAAO,SAAS,CAAC;AAC7E,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE;IACnD,MAAM,OAAO,GAAG,CAAC,EAAE;IACnB,IAAI,SAAS,OAAO,SAAS,KACzB,UAAU;IACd,MAAM,IAAI,SAAS,CAAC,QAAQ,OAAO,CAAC,MAAM;IAC1C,IAAI,aAAa;QACb,OAAQ;YACJ,KAAK;gBACD,MAAM,CAAC,EAAE,EAAE,KAAK;gBAChB;YACJ,KAAK;gBACD,MAAM,CAAC,EAAE,EAAE,KAAK;gBAChB;YACJ,KAAK;gBACD,MAAM,CAAC,EAAE,EAAE,KAAK;gBAChB;QACR;QACA,MAAM,IAAI,OAAO;QACjB,OAAO,SAAS,MAAM,OAAO,CAAC,KAAK,IAAI;IAC3C;IACA,MAAM,IAAI,SAAS,KAAK;IACxB,OAAO,SAAS,MAAM,CAAC,IAAI,IAAI;AACnC;AACA,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,MAAM;IACrC,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,IAAI,YAAY,QAAQ;QACpB,MAAM,MAAM,MAAM,QAAQ,CAAC;QAC3B,OAAO,QAAQ,IAAI,MAAM,SAAS,IAAI,MAAM,CAAC,KAAK,SAAS;IAC/D;IACA,OAAO,gBAAgB,eAAe,CAAC;AAC3C;AACA,MAAM,SAAS;IACX,UAAU;IACV,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,GAAG;IACvD,WAAW,CAAA,OAAQ,aAAa,MAAM,GAAG;AAC7C;AACA,MAAM,SAAS;IACX,UAAU;IACV,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,GAAG;IACvD,WAAW,CAAA,OAAQ,aAAa,MAAM,GAAG;AAC7C;AACA,MAAM,MAAM;IACR,UAAU;IACV,SAAS;IACT,KAAK;IACL,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,IAAI;IACxD,WAAW,gBAAgB,eAAe;AAC9C;AACA,MAAM,SAAS;IACX,UAAU;IACV,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,MAAQ,WAAW,KAAK,GAAG,IAAI;IACxD,WAAW,CAAA,OAAQ,aAAa,MAAM,IAAI;AAC9C;AAEA,QAAQ,GAAG,GAAG;AACd,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/set.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Pair = require('../../nodes/Pair.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\n\nclass YAMLSet extends YAMLMap.YAMLMap {\n    constructor(schema) {\n        super(schema);\n        this.tag = YAMLSet.tag;\n    }\n    add(key) {\n        let pair;\n        if (identity.isPair(key))\n            pair = key;\n        else if (key &&\n            typeof key === 'object' &&\n            'key' in key &&\n            'value' in key &&\n            key.value === null)\n            pair = new Pair.Pair(key.key, null);\n        else\n            pair = new Pair.Pair(key, null);\n        const prev = YAMLMap.findPair(this.items, pair.key);\n        if (!prev)\n            this.items.push(pair);\n    }\n    /**\n     * If `keepPair` is `true`, returns the Pair matching `key`.\n     * Otherwise, returns the value of that Pair's key.\n     */\n    get(key, keepPair) {\n        const pair = YAMLMap.findPair(this.items, key);\n        return !keepPair && identity.isPair(pair)\n            ? identity.isScalar(pair.key)\n                ? pair.key.value\n                : pair.key\n            : pair;\n    }\n    set(key, value) {\n        if (typeof value !== 'boolean')\n            throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof value}`);\n        const prev = YAMLMap.findPair(this.items, key);\n        if (prev && !value) {\n            this.items.splice(this.items.indexOf(prev), 1);\n        }\n        else if (!prev && value) {\n            this.items.push(new Pair.Pair(key));\n        }\n    }\n    toJSON(_, ctx) {\n        return super.toJSON(_, ctx, Set);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        if (this.hasAllNullValues(true))\n            return super.toString(Object.assign({}, ctx, { allNullValues: true }), onComment, onChompKeep);\n        else\n            throw new Error('Set items must all have null values');\n    }\n    static from(schema, iterable, ctx) {\n        const { replacer } = ctx;\n        const set = new this(schema);\n        if (iterable && Symbol.iterator in Object(iterable))\n            for (let value of iterable) {\n                if (typeof replacer === 'function')\n                    value = replacer.call(iterable, value, value);\n                set.items.push(Pair.createPair(value, null, ctx));\n            }\n        return set;\n    }\n}\nYAMLSet.tag = 'tag:yaml.org,2002:set';\nconst set = {\n    collection: 'map',\n    identify: value => value instanceof Set,\n    nodeClass: YAMLSet,\n    default: false,\n    tag: 'tag:yaml.org,2002:set',\n    createNode: (schema, iterable, ctx) => YAMLSet.from(schema, iterable, ctx),\n    resolve(map, onError) {\n        if (identity.isMap(map)) {\n            if (map.hasAllNullValues(true))\n                return Object.assign(new YAMLSet(), map);\n            else\n                onError('Set items must all have null values');\n        }\n        else\n            onError('Expected a mapping for this tag');\n        return map;\n    }\n};\n\nexports.YAMLSet = YAMLSet;\nexports.set = set;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,gBAAgB,QAAQ,OAAO;IACjC,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC;QACN,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG;IAC1B;IACA,IAAI,GAAG,EAAE;QACL,IAAI;QACJ,IAAI,SAAS,MAAM,CAAC,MAChB,OAAO;aACN,IAAI,OACL,OAAO,QAAQ,YACf,SAAS,OACT,WAAW,OACX,IAAI,KAAK,KAAK,MACd,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;aAE9B,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK;QAC9B,MAAM,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG;QAClD,IAAI,CAAC,MACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACxB;IACA;;;KAGC,GACD,IAAI,GAAG,EAAE,QAAQ,EAAE;QACf,MAAM,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;QAC1C,OAAO,CAAC,YAAY,SAAS,MAAM,CAAC,QAC9B,SAAS,QAAQ,CAAC,KAAK,GAAG,IACtB,KAAK,GAAG,CAAC,KAAK,GACd,KAAK,GAAG,GACZ;IACV;IACA,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,IAAI,OAAO,UAAU,WACjB,MAAM,IAAI,MAAM,CAAC,8DAA8D,EAAE,OAAO,OAAO;QACnG,MAAM,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;QAC1C,IAAI,QAAQ,CAAC,OAAO;YAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAChD,OACK,IAAI,CAAC,QAAQ,OAAO;YACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QAClC;IACJ;IACA,OAAO,CAAC,EAAE,GAAG,EAAE;QACX,OAAO,KAAK,CAAC,OAAO,GAAG,KAAK;IAChC;IACA,SAAS,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;QAClC,IAAI,CAAC,KACD,OAAO,KAAK,SAAS,CAAC,IAAI;QAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,OACtB,OAAO,KAAK,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;YAAE,eAAe;QAAK,IAAI,WAAW;aAElF,MAAM,IAAI,MAAM;IACxB;IACA,OAAO,KAAK,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE;QAC/B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM,MAAM,IAAI,IAAI,CAAC;QACrB,IAAI,YAAY,OAAO,QAAQ,IAAI,OAAO,WACtC,KAAK,IAAI,SAAS,SAAU;YACxB,IAAI,OAAO,aAAa,YACpB,QAAQ,SAAS,IAAI,CAAC,UAAU,OAAO;YAC3C,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,OAAO,MAAM;QAChD;QACJ,OAAO;IACX;AACJ;AACA,QAAQ,GAAG,GAAG;AACd,MAAM,MAAM;IACR,YAAY;IACZ,UAAU,CAAA,QAAS,iBAAiB;IACpC,WAAW;IACX,SAAS;IACT,KAAK;IACL,YAAY,CAAC,QAAQ,UAAU,MAAQ,QAAQ,IAAI,CAAC,QAAQ,UAAU;IACtE,SAAQ,GAAG,EAAE,OAAO;QAChB,IAAI,SAAS,KAAK,CAAC,MAAM;YACrB,IAAI,IAAI,gBAAgB,CAAC,OACrB,OAAO,OAAO,MAAM,CAAC,IAAI,WAAW;iBAEpC,QAAQ;QAChB,OAEI,QAAQ;QACZ,OAAO;IACX;AACJ;AAEA,QAAQ,OAAO,GAAG;AAClB,QAAQ,GAAG,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/timestamp.js"], "sourcesContent": ["'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\n/** Internal types handle bigint as number, because TS can't figure it out. */\nfunction parseSexagesimal(str, asBigInt) {\n    const sign = str[0];\n    const parts = sign === '-' || sign === '+' ? str.substring(1) : str;\n    const num = (n) => asBigInt ? BigInt(n) : Number(n);\n    const res = parts\n        .replace(/_/g, '')\n        .split(':')\n        .reduce((res, p) => res * num(60) + num(p), num(0));\n    return (sign === '-' ? num(-1) * res : res);\n}\n/**\n * hhhh:mm:ss.sss\n *\n * Internal types handle bigint as number, because TS can't figure it out.\n */\nfunction stringifySexagesimal(node) {\n    let { value } = node;\n    let num = (n) => n;\n    if (typeof value === 'bigint')\n        num = n => BigInt(n);\n    else if (isNaN(value) || !isFinite(value))\n        return stringifyNumber.stringifyNumber(node);\n    let sign = '';\n    if (value < 0) {\n        sign = '-';\n        value *= num(-1);\n    }\n    const _60 = num(60);\n    const parts = [value % _60]; // seconds, including ms\n    if (value < 60) {\n        parts.unshift(0); // at least one : is required\n    }\n    else {\n        value = (value - parts[0]) / _60;\n        parts.unshift(value % _60); // minutes\n        if (value >= 60) {\n            value = (value - parts[0]) / _60;\n            parts.unshift(value); // hours\n        }\n    }\n    return (sign +\n        parts\n            .map(n => String(n).padStart(2, '0'))\n            .join(':')\n            .replace(/000000\\d*$/, '') // % 60 may introduce error\n    );\n}\nconst intTime = {\n    identify: value => typeof value === 'bigint' || Number.isInteger(value),\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,\n    resolve: (str, _onError, { intAsBigInt }) => parseSexagesimal(str, intAsBigInt),\n    stringify: stringifySexagesimal\n};\nconst floatTime = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*$/,\n    resolve: str => parseSexagesimal(str, false),\n    stringify: stringifySexagesimal\n};\nconst timestamp = {\n    identify: value => value instanceof Date,\n    default: true,\n    tag: 'tag:yaml.org,2002:timestamp',\n    // If the time zone is omitted, the timestamp is assumed to be specified in UTC. The time part\n    // may be omitted altogether, resulting in a date format. In such a case, the time part is\n    // assumed to be 00:00:00Z (start of day, UTC).\n    test: RegExp('^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})' + // YYYY-Mm-Dd\n        '(?:' + // time is optional\n        '(?:t|T|[ \\\\t]+)' + // t | T | whitespace\n        '([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\\\.[0-9]+)?)' + // Hh:Mm:Ss(.ss)?\n        '(?:[ \\\\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?' + // Z | +5 | -03:30\n        ')?$'),\n    resolve(str) {\n        const match = str.match(timestamp.test);\n        if (!match)\n            throw new Error('!!timestamp expects a date, starting with yyyy-mm-dd');\n        const [, year, month, day, hour, minute, second] = match.map(Number);\n        const millisec = match[7] ? Number((match[7] + '00').substr(1, 3)) : 0;\n        let date = Date.UTC(year, month - 1, day, hour || 0, minute || 0, second || 0, millisec);\n        const tz = match[8];\n        if (tz && tz !== 'Z') {\n            let d = parseSexagesimal(tz, false);\n            if (Math.abs(d) < 30)\n                d *= 60;\n            date -= 60000 * d;\n        }\n        return new Date(date);\n    },\n    stringify: ({ value }) => value?.toISOString().replace(/(T00:00:00)?\\.000Z$/, '') ?? ''\n};\n\nexports.floatTime = floatTime;\nexports.intTime = intTime;\nexports.timestamp = timestamp;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,4EAA4E,GAC5E,SAAS,iBAAiB,GAAG,EAAE,QAAQ;IACnC,MAAM,OAAO,GAAG,CAAC,EAAE;IACnB,MAAM,QAAQ,SAAS,OAAO,SAAS,MAAM,IAAI,SAAS,CAAC,KAAK;IAChE,MAAM,MAAM,CAAC,IAAM,WAAW,OAAO,KAAK,OAAO;IACjD,MAAM,MAAM,MACP,OAAO,CAAC,MAAM,IACd,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,IAAI,MAAM,IAAI,IAAI,IAAI;IACpD,OAAQ,SAAS,MAAM,IAAI,CAAC,KAAK,MAAM;AAC3C;AACA;;;;CAIC,GACD,SAAS,qBAAqB,IAAI;IAC9B,IAAI,EAAE,KAAK,EAAE,GAAG;IAChB,IAAI,MAAM,CAAC,IAAM;IACjB,IAAI,OAAO,UAAU,UACjB,MAAM,CAAA,IAAK,OAAO;SACjB,IAAI,MAAM,UAAU,CAAC,SAAS,QAC/B,OAAO,gBAAgB,eAAe,CAAC;IAC3C,IAAI,OAAO;IACX,IAAI,QAAQ,GAAG;QACX,OAAO;QACP,SAAS,IAAI,CAAC;IAClB;IACA,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ;QAAC,QAAQ;KAAI,EAAE,wBAAwB;IACrD,IAAI,QAAQ,IAAI;QACZ,MAAM,OAAO,CAAC,IAAI,6BAA6B;IACnD,OACK;QACD,QAAQ,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI;QAC7B,MAAM,OAAO,CAAC,QAAQ,MAAM,UAAU;QACtC,IAAI,SAAS,IAAI;YACb,QAAQ,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI;YAC7B,MAAM,OAAO,CAAC,QAAQ,QAAQ;QAClC;IACJ;IACA,OAAQ,OACJ,MACK,GAAG,CAAC,CAAA,IAAK,OAAO,GAAG,QAAQ,CAAC,GAAG,MAC/B,IAAI,CAAC,KACL,OAAO,CAAC,cAAc,IAAI,2BAA2B;;AAElE;AACA,MAAM,UAAU;IACZ,UAAU,CAAA,QAAS,OAAO,UAAU,YAAY,OAAO,SAAS,CAAC;IACjE,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAC,KAAK,UAAU,EAAE,WAAW,EAAE,GAAK,iBAAiB,KAAK;IACnE,WAAW;AACf;AACA,MAAM,YAAY;IACd,UAAU,CAAA,QAAS,OAAO,UAAU;IACpC,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,SAAS,CAAA,MAAO,iBAAiB,KAAK;IACtC,WAAW;AACf;AACA,MAAM,YAAY;IACd,UAAU,CAAA,QAAS,iBAAiB;IACpC,SAAS;IACT,KAAK;IACL,8FAA8F;IAC9F,0FAA0F;IAC1F,+CAA+C;IAC/C,MAAM,OAAO,0CAA0C,aAAa;IAChE,QAAQ,mBAAmB;IAC3B,oBAAoB,qBAAqB;IACzC,uDAAuD,iBAAiB;IACxE,kDAAkD,kBAAkB;IACpE;IACJ,SAAQ,GAAG;QACP,MAAM,QAAQ,IAAI,KAAK,CAAC,UAAU,IAAI;QACtC,IAAI,CAAC,OACD,MAAM,IAAI,MAAM;QACpB,MAAM,GAAG,MAAM,OAAO,KAAK,MAAM,QAAQ,OAAO,GAAG,MAAM,GAAG,CAAC;QAC7D,MAAM,WAAW,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM;QACrE,IAAI,OAAO,KAAK,GAAG,CAAC,MAAM,QAAQ,GAAG,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG;QAC/E,MAAM,KAAK,KAAK,CAAC,EAAE;QACnB,IAAI,MAAM,OAAO,KAAK;YAClB,IAAI,IAAI,iBAAiB,IAAI;YAC7B,IAAI,KAAK,GAAG,CAAC,KAAK,IACd,KAAK;YACT,QAAQ,QAAQ;QACpB;QACA,OAAO,IAAI,KAAK;IACpB;IACA,WAAW,CAAC,EAAE,KAAK,EAAE,GAAK,OAAO,cAAc,QAAQ,uBAAuB,OAAO;AACzF;AAEA,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG;AAClB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/yaml-1.1/schema.js"], "sourcesContent": ["'use strict';\n\nvar map = require('../common/map.js');\nvar _null = require('../common/null.js');\nvar seq = require('../common/seq.js');\nvar string = require('../common/string.js');\nvar binary = require('./binary.js');\nvar bool = require('./bool.js');\nvar float = require('./float.js');\nvar int = require('./int.js');\nvar merge = require('./merge.js');\nvar omap = require('./omap.js');\nvar pairs = require('./pairs.js');\nvar set = require('./set.js');\nvar timestamp = require('./timestamp.js');\n\nconst schema = [\n    map.map,\n    seq.seq,\n    string.string,\n    _null.nullTag,\n    bool.trueTag,\n    bool.falseTag,\n    int.intBin,\n    int.intOct,\n    int.int,\n    int.intHex,\n    float.floatNaN,\n    float.floatExp,\n    float.float,\n    binary.binary,\n    merge.merge,\n    omap.omap,\n    pairs.pairs,\n    set.set,\n    timestamp.intTime,\n    timestamp.floatTime,\n    timestamp.timestamp\n];\n\nexports.schema = schema;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,SAAS;IACX,IAAI,GAAG;IACP,IAAI,GAAG;IACP,OAAO,MAAM;IACb,MAAM,OAAO;IACb,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,IAAI,MAAM;IACV,IAAI,MAAM;IACV,IAAI,GAAG;IACP,IAAI,MAAM;IACV,MAAM,QAAQ;IACd,MAAM,QAAQ;IACd,MAAM,KAAK;IACX,OAAO,MAAM;IACb,MAAM,KAAK;IACX,KAAK,IAAI;IACT,MAAM,KAAK;IACX,IAAI,GAAG;IACP,UAAU,OAAO;IACjB,UAAU,SAAS;IACnB,UAAU,SAAS;CACtB;AAED,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/tags.js"], "sourcesContent": ["'use strict';\n\nvar map = require('./common/map.js');\nvar _null = require('./common/null.js');\nvar seq = require('./common/seq.js');\nvar string = require('./common/string.js');\nvar bool = require('./core/bool.js');\nvar float = require('./core/float.js');\nvar int = require('./core/int.js');\nvar schema = require('./core/schema.js');\nvar schema$1 = require('./json/schema.js');\nvar binary = require('./yaml-1.1/binary.js');\nvar merge = require('./yaml-1.1/merge.js');\nvar omap = require('./yaml-1.1/omap.js');\nvar pairs = require('./yaml-1.1/pairs.js');\nvar schema$2 = require('./yaml-1.1/schema.js');\nvar set = require('./yaml-1.1/set.js');\nvar timestamp = require('./yaml-1.1/timestamp.js');\n\nconst schemas = new Map([\n    ['core', schema.schema],\n    ['failsafe', [map.map, seq.seq, string.string]],\n    ['json', schema$1.schema],\n    ['yaml11', schema$2.schema],\n    ['yaml-1.1', schema$2.schema]\n]);\nconst tagsByName = {\n    binary: binary.binary,\n    bool: bool.boolTag,\n    float: float.float,\n    floatExp: float.floatExp,\n    floatNaN: float.floatNaN,\n    floatTime: timestamp.floatTime,\n    int: int.int,\n    intHex: int.intHex,\n    intOct: int.intOct,\n    intTime: timestamp.intTime,\n    map: map.map,\n    merge: merge.merge,\n    null: _null.nullTag,\n    omap: omap.omap,\n    pairs: pairs.pairs,\n    seq: seq.seq,\n    set: set.set,\n    timestamp: timestamp.timestamp\n};\nconst coreKnownTags = {\n    'tag:yaml.org,2002:binary': binary.binary,\n    'tag:yaml.org,2002:merge': merge.merge,\n    'tag:yaml.org,2002:omap': omap.omap,\n    'tag:yaml.org,2002:pairs': pairs.pairs,\n    'tag:yaml.org,2002:set': set.set,\n    'tag:yaml.org,2002:timestamp': timestamp.timestamp\n};\nfunction getTags(customTags, schemaName, addMergeTag) {\n    const schemaTags = schemas.get(schemaName);\n    if (schemaTags && !customTags) {\n        return addMergeTag && !schemaTags.includes(merge.merge)\n            ? schemaTags.concat(merge.merge)\n            : schemaTags.slice();\n    }\n    let tags = schemaTags;\n    if (!tags) {\n        if (Array.isArray(customTags))\n            tags = [];\n        else {\n            const keys = Array.from(schemas.keys())\n                .filter(key => key !== 'yaml11')\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown schema \"${schemaName}\"; use one of ${keys} or define customTags array`);\n        }\n    }\n    if (Array.isArray(customTags)) {\n        for (const tag of customTags)\n            tags = tags.concat(tag);\n    }\n    else if (typeof customTags === 'function') {\n        tags = customTags(tags.slice());\n    }\n    if (addMergeTag)\n        tags = tags.concat(merge.merge);\n    return tags.reduce((tags, tag) => {\n        const tagObj = typeof tag === 'string' ? tagsByName[tag] : tag;\n        if (!tagObj) {\n            const tagName = JSON.stringify(tag);\n            const keys = Object.keys(tagsByName)\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown custom tag ${tagName}; use one of ${keys}`);\n        }\n        if (!tags.includes(tagObj))\n            tags.push(tagObj);\n        return tags;\n    }, []);\n}\n\nexports.coreKnownTags = coreKnownTags;\nexports.getTags = getTags;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,UAAU,IAAI,IAAI;IACpB;QAAC;QAAQ,OAAO,MAAM;KAAC;IACvB;QAAC;QAAY;YAAC,IAAI,GAAG;YAAE,IAAI,GAAG;YAAE,OAAO,MAAM;SAAC;KAAC;IAC/C;QAAC;QAAQ,SAAS,MAAM;KAAC;IACzB;QAAC;QAAU,SAAS,MAAM;KAAC;IAC3B;QAAC;QAAY,SAAS,MAAM;KAAC;CAChC;AACD,MAAM,aAAa;IACf,QAAQ,OAAO,MAAM;IACrB,MAAM,KAAK,OAAO;IAClB,OAAO,MAAM,KAAK;IAClB,UAAU,MAAM,QAAQ;IACxB,UAAU,MAAM,QAAQ;IACxB,WAAW,UAAU,SAAS;IAC9B,KAAK,IAAI,GAAG;IACZ,QAAQ,IAAI,MAAM;IAClB,QAAQ,IAAI,MAAM;IAClB,SAAS,UAAU,OAAO;IAC1B,KAAK,IAAI,GAAG;IACZ,OAAO,MAAM,KAAK;IAClB,MAAM,MAAM,OAAO;IACnB,MAAM,KAAK,IAAI;IACf,OAAO,MAAM,KAAK;IAClB,KAAK,IAAI,GAAG;IACZ,KAAK,IAAI,GAAG;IACZ,WAAW,UAAU,SAAS;AAClC;AACA,MAAM,gBAAgB;IAClB,4BAA4B,OAAO,MAAM;IACzC,2BAA2B,MAAM,KAAK;IACtC,0BAA0B,KAAK,IAAI;IACnC,2BAA2B,MAAM,KAAK;IACtC,yBAAyB,IAAI,GAAG;IAChC,+BAA+B,UAAU,SAAS;AACtD;AACA,SAAS,QAAQ,UAAU,EAAE,UAAU,EAAE,WAAW;IAChD,MAAM,aAAa,QAAQ,GAAG,CAAC;IAC/B,IAAI,cAAc,CAAC,YAAY;QAC3B,OAAO,eAAe,CAAC,WAAW,QAAQ,CAAC,MAAM,KAAK,IAChD,WAAW,MAAM,CAAC,MAAM,KAAK,IAC7B,WAAW,KAAK;IAC1B;IACA,IAAI,OAAO;IACX,IAAI,CAAC,MAAM;QACP,IAAI,MAAM,OAAO,CAAC,aACd,OAAO,EAAE;aACR;YACD,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,IAAI,IAC/B,MAAM,CAAC,CAAA,MAAO,QAAQ,UACtB,GAAG,CAAC,CAAA,MAAO,KAAK,SAAS,CAAC,MAC1B,IAAI,CAAC;YACV,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,WAAW,cAAc,EAAE,KAAK,2BAA2B,CAAC;QACnG;IACJ;IACA,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,KAAK,MAAM,OAAO,WACd,OAAO,KAAK,MAAM,CAAC;IAC3B,OACK,IAAI,OAAO,eAAe,YAAY;QACvC,OAAO,WAAW,KAAK,KAAK;IAChC;IACA,IAAI,aACA,OAAO,KAAK,MAAM,CAAC,MAAM,KAAK;IAClC,OAAO,KAAK,MAAM,CAAC,CAAC,MAAM;QACtB,MAAM,SAAS,OAAO,QAAQ,WAAW,UAAU,CAAC,IAAI,GAAG;QAC3D,IAAI,CAAC,QAAQ;YACT,MAAM,UAAU,KAAK,SAAS,CAAC;YAC/B,MAAM,OAAO,OAAO,IAAI,CAAC,YACpB,GAAG,CAAC,CAAA,MAAO,KAAK,SAAS,CAAC,MAC1B,IAAI,CAAC;YACV,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,QAAQ,aAAa,EAAE,MAAM;QACvE;QACA,IAAI,CAAC,KAAK,QAAQ,CAAC,SACf,KAAK,IAAI,CAAC;QACd,OAAO;IACX,GAAG,EAAE;AACT;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/schema/Schema.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar map = require('./common/map.js');\nvar seq = require('./common/seq.js');\nvar string = require('./common/string.js');\nvar tags = require('./tags.js');\n\nconst sortMapEntriesByKey = (a, b) => a.key < b.key ? -1 : a.key > b.key ? 1 : 0;\nclass Schema {\n    constructor({ compat, customTags, merge, resolveKnownTags, schema, sortMapEntries, toStringDefaults }) {\n        this.compat = Array.isArray(compat)\n            ? tags.getTags(compat, 'compat')\n            : compat\n                ? tags.getTags(null, compat)\n                : null;\n        this.name = (typeof schema === 'string' && schema) || 'core';\n        this.knownTags = resolveKnownTags ? tags.coreKnownTags : {};\n        this.tags = tags.getTags(customTags, this.name, merge);\n        this.toStringOptions = toStringDefaults ?? null;\n        Object.defineProperty(this, identity.MAP, { value: map.map });\n        Object.defineProperty(this, identity.SCALAR, { value: string.string });\n        Object.defineProperty(this, identity.SEQ, { value: seq.seq });\n        // Used by createMap()\n        this.sortMapEntries =\n            typeof sortMapEntries === 'function'\n                ? sortMapEntries\n                : sortMapEntries === true\n                    ? sortMapEntriesByKey\n                    : null;\n    }\n    clone() {\n        const copy = Object.create(Schema.prototype, Object.getOwnPropertyDescriptors(this));\n        copy.tags = this.tags.slice();\n        return copy;\n    }\n}\n\nexports.Schema = Schema;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,sBAAsB,CAAC,GAAG,IAAM,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI;AAC/E,MAAM;IACF,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,CAAE;QACnG,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,UACtB,KAAK,OAAO,CAAC,QAAQ,YACrB,SACI,KAAK,OAAO,CAAC,MAAM,UACnB;QACV,IAAI,CAAC,IAAI,GAAG,AAAC,OAAO,WAAW,YAAY,UAAW;QACtD,IAAI,CAAC,SAAS,GAAG,mBAAmB,KAAK,aAAa,GAAG,CAAC;QAC1D,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO,CAAC,YAAY,IAAI,CAAC,IAAI,EAAE;QAChD,IAAI,CAAC,eAAe,GAAG,oBAAoB;QAC3C,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,GAAG,EAAE;YAAE,OAAO,IAAI,GAAG;QAAC;QAC3D,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,MAAM,EAAE;YAAE,OAAO,OAAO,MAAM;QAAC;QACpE,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,GAAG,EAAE;YAAE,OAAO,IAAI,GAAG;QAAC;QAC3D,sBAAsB;QACtB,IAAI,CAAC,cAAc,GACf,OAAO,mBAAmB,aACpB,iBACA,mBAAmB,OACf,sBACA;IAClB;IACA,QAAQ;QACJ,MAAM,OAAO,OAAO,MAAM,CAAC,OAAO,SAAS,EAAE,OAAO,yBAAyB,CAAC,IAAI;QAClF,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAC3B,OAAO;IACX;AACJ;AAEA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/stringify/stringifyDocument.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyDocument(doc, options) {\n    const lines = [];\n    let hasDirectives = options.directives === true;\n    if (options.directives !== false && doc.directives) {\n        const dir = doc.directives.toString(doc);\n        if (dir) {\n            lines.push(dir);\n            hasDirectives = true;\n        }\n        else if (doc.directives.docStart)\n            hasDirectives = true;\n    }\n    if (hasDirectives)\n        lines.push('---');\n    const ctx = stringify.createStringifyContext(doc, options);\n    const { commentString } = ctx.options;\n    if (doc.commentBefore) {\n        if (lines.length !== 1)\n            lines.unshift('');\n        const cs = commentString(doc.commentBefore);\n        lines.unshift(stringifyComment.indentComment(cs, ''));\n    }\n    let chompKeep = false;\n    let contentComment = null;\n    if (doc.contents) {\n        if (identity.isNode(doc.contents)) {\n            if (doc.contents.spaceBefore && hasDirectives)\n                lines.push('');\n            if (doc.contents.commentBefore) {\n                const cs = commentString(doc.contents.commentBefore);\n                lines.push(stringifyComment.indentComment(cs, ''));\n            }\n            // top-level block scalars need to be indented if followed by a comment\n            ctx.forceBlockIndent = !!doc.comment;\n            contentComment = doc.contents.comment;\n        }\n        const onChompKeep = contentComment ? undefined : () => (chompKeep = true);\n        let body = stringify.stringify(doc.contents, ctx, () => (contentComment = null), onChompKeep);\n        if (contentComment)\n            body += stringifyComment.lineComment(body, '', commentString(contentComment));\n        if ((body[0] === '|' || body[0] === '>') &&\n            lines[lines.length - 1] === '---') {\n            // Top-level block scalars with a preceding doc marker ought to use the\n            // same line for their header.\n            lines[lines.length - 1] = `--- ${body}`;\n        }\n        else\n            lines.push(body);\n    }\n    else {\n        lines.push(stringify.stringify(doc.contents, ctx));\n    }\n    if (doc.directives?.docEnd) {\n        if (doc.comment) {\n            const cs = commentString(doc.comment);\n            if (cs.includes('\\n')) {\n                lines.push('...');\n                lines.push(stringifyComment.indentComment(cs, ''));\n            }\n            else {\n                lines.push(`... ${cs}`);\n            }\n        }\n        else {\n            lines.push('...');\n        }\n    }\n    else {\n        let dc = doc.comment;\n        if (dc && chompKeep)\n            dc = dc.replace(/^\\n+/, '');\n        if (dc) {\n            if ((!chompKeep || contentComment) && lines[lines.length - 1] !== '')\n                lines.push('');\n            lines.push(stringifyComment.indentComment(commentString(dc), ''));\n        }\n    }\n    return lines.join('\\n') + '\\n';\n}\n\nexports.stringifyDocument = stringifyDocument;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,kBAAkB,GAAG,EAAE,OAAO;IACnC,MAAM,QAAQ,EAAE;IAChB,IAAI,gBAAgB,QAAQ,UAAU,KAAK;IAC3C,IAAI,QAAQ,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE;QAChD,MAAM,MAAM,IAAI,UAAU,CAAC,QAAQ,CAAC;QACpC,IAAI,KAAK;YACL,MAAM,IAAI,CAAC;YACX,gBAAgB;QACpB,OACK,IAAI,IAAI,UAAU,CAAC,QAAQ,EAC5B,gBAAgB;IACxB;IACA,IAAI,eACA,MAAM,IAAI,CAAC;IACf,MAAM,MAAM,UAAU,sBAAsB,CAAC,KAAK;IAClD,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,OAAO;IACrC,IAAI,IAAI,aAAa,EAAE;QACnB,IAAI,MAAM,MAAM,KAAK,GACjB,MAAM,OAAO,CAAC;QAClB,MAAM,KAAK,cAAc,IAAI,aAAa;QAC1C,MAAM,OAAO,CAAC,iBAAiB,aAAa,CAAC,IAAI;IACrD;IACA,IAAI,YAAY;IAChB,IAAI,iBAAiB;IACrB,IAAI,IAAI,QAAQ,EAAE;QACd,IAAI,SAAS,MAAM,CAAC,IAAI,QAAQ,GAAG;YAC/B,IAAI,IAAI,QAAQ,CAAC,WAAW,IAAI,eAC5B,MAAM,IAAI,CAAC;YACf,IAAI,IAAI,QAAQ,CAAC,aAAa,EAAE;gBAC5B,MAAM,KAAK,cAAc,IAAI,QAAQ,CAAC,aAAa;gBACnD,MAAM,IAAI,CAAC,iBAAiB,aAAa,CAAC,IAAI;YAClD;YACA,uEAAuE;YACvE,IAAI,gBAAgB,GAAG,CAAC,CAAC,IAAI,OAAO;YACpC,iBAAiB,IAAI,QAAQ,CAAC,OAAO;QACzC;QACA,MAAM,cAAc,iBAAiB,YAAY,IAAO,YAAY;QACpE,IAAI,OAAO,UAAU,SAAS,CAAC,IAAI,QAAQ,EAAE,KAAK,IAAO,iBAAiB,MAAO;QACjF,IAAI,gBACA,QAAQ,iBAAiB,WAAW,CAAC,MAAM,IAAI,cAAc;QACjE,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,KACnC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,OAAO;YACnC,uEAAuE;YACvE,8BAA8B;YAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM;QAC3C,OAEI,MAAM,IAAI,CAAC;IACnB,OACK;QACD,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,IAAI,QAAQ,EAAE;IACjD;IACA,IAAI,IAAI,UAAU,EAAE,QAAQ;QACxB,IAAI,IAAI,OAAO,EAAE;YACb,MAAM,KAAK,cAAc,IAAI,OAAO;YACpC,IAAI,GAAG,QAAQ,CAAC,OAAO;gBACnB,MAAM,IAAI,CAAC;gBACX,MAAM,IAAI,CAAC,iBAAiB,aAAa,CAAC,IAAI;YAClD,OACK;gBACD,MAAM,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI;YAC1B;QACJ,OACK;YACD,MAAM,IAAI,CAAC;QACf;IACJ,OACK;QACD,IAAI,KAAK,IAAI,OAAO;QACpB,IAAI,MAAM,WACN,KAAK,GAAG,OAAO,CAAC,QAAQ;QAC5B,IAAI,IAAI;YACJ,IAAI,CAAC,CAAC,aAAa,cAAc,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,IAC9D,MAAM,IAAI,CAAC;YACf,MAAM,IAAI,CAAC,iBAAiB,aAAa,CAAC,cAAc,KAAK;QACjE;IACJ;IACA,OAAO,MAAM,IAAI,CAAC,QAAQ;AAC9B;AAEA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/doc/Document.js"], "sourcesContent": ["'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar Collection = require('../nodes/Collection.js');\nvar identity = require('../nodes/identity.js');\nvar Pair = require('../nodes/Pair.js');\nvar toJS = require('../nodes/toJS.js');\nvar Schema = require('../schema/Schema.js');\nvar stringifyDocument = require('../stringify/stringifyDocument.js');\nvar anchors = require('./anchors.js');\nvar applyReviver = require('./applyReviver.js');\nvar createNode = require('./createNode.js');\nvar directives = require('./directives.js');\n\nclass Document {\n    constructor(value, replacer, options) {\n        /** A comment before this Document */\n        this.commentBefore = null;\n        /** A comment immediately after this Document */\n        this.comment = null;\n        /** Errors encountered during parsing. */\n        this.errors = [];\n        /** Warnings encountered during parsing. */\n        this.warnings = [];\n        Object.defineProperty(this, identity.NODE_TYPE, { value: identity.DOC });\n        let _replacer = null;\n        if (typeof replacer === 'function' || Array.isArray(replacer)) {\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const opt = Object.assign({\n            intAsBigInt: false,\n            keepSourceTokens: false,\n            logLevel: 'warn',\n            prettyErrors: true,\n            strict: true,\n            stringKeys: false,\n            uniqueKeys: true,\n            version: '1.2'\n        }, options);\n        this.options = opt;\n        let { version } = opt;\n        if (options?._directives) {\n            this.directives = options._directives.atDocument();\n            if (this.directives.yaml.explicit)\n                version = this.directives.yaml.version;\n        }\n        else\n            this.directives = new directives.Directives({ version });\n        this.setSchema(version, options);\n        // @ts-expect-error We can't really know that this matches Contents.\n        this.contents =\n            value === undefined ? null : this.createNode(value, _replacer, options);\n    }\n    /**\n     * Create a deep copy of this Document and its contents.\n     *\n     * Custom Node values that inherit from `Object` still refer to their original instances.\n     */\n    clone() {\n        const copy = Object.create(Document.prototype, {\n            [identity.NODE_TYPE]: { value: identity.DOC }\n        });\n        copy.commentBefore = this.commentBefore;\n        copy.comment = this.comment;\n        copy.errors = this.errors.slice();\n        copy.warnings = this.warnings.slice();\n        copy.options = Object.assign({}, this.options);\n        if (this.directives)\n            copy.directives = this.directives.clone();\n        copy.schema = this.schema.clone();\n        // @ts-expect-error We can't really know that this matches Contents.\n        copy.contents = identity.isNode(this.contents)\n            ? this.contents.clone(copy.schema)\n            : this.contents;\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** Adds a value to the document. */\n    add(value) {\n        if (assertCollection(this.contents))\n            this.contents.add(value);\n    }\n    /** Adds a value to the document. */\n    addIn(path, value) {\n        if (assertCollection(this.contents))\n            this.contents.addIn(path, value);\n    }\n    /**\n     * Create a new `Alias` node, ensuring that the target `node` has the required anchor.\n     *\n     * If `node` already has an anchor, `name` is ignored.\n     * Otherwise, the `node.anchor` value will be set to `name`,\n     * or if an anchor with that name is already present in the document,\n     * `name` will be used as a prefix for a new unique anchor.\n     * If `name` is undefined, the generated anchor will use 'a' as a prefix.\n     */\n    createAlias(node, name) {\n        if (!node.anchor) {\n            const prev = anchors.anchorNames(this);\n            node.anchor =\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                !name || prev.has(name) ? anchors.findNewAnchor(name || 'a', prev) : name;\n        }\n        return new Alias.Alias(node.anchor);\n    }\n    createNode(value, replacer, options) {\n        let _replacer = undefined;\n        if (typeof replacer === 'function') {\n            value = replacer.call({ '': value }, '', value);\n            _replacer = replacer;\n        }\n        else if (Array.isArray(replacer)) {\n            const keyToStr = (v) => typeof v === 'number' || v instanceof String || v instanceof Number;\n            const asStr = replacer.filter(keyToStr).map(String);\n            if (asStr.length > 0)\n                replacer = replacer.concat(asStr);\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const { aliasDuplicateObjects, anchorPrefix, flow, keepUndefined, onTagObj, tag } = options ?? {};\n        const { onAnchor, setAnchors, sourceObjects } = anchors.createNodeAnchors(this, \n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        anchorPrefix || 'a');\n        const ctx = {\n            aliasDuplicateObjects: aliasDuplicateObjects ?? true,\n            keepUndefined: keepUndefined ?? false,\n            onAnchor,\n            onTagObj,\n            replacer: _replacer,\n            schema: this.schema,\n            sourceObjects\n        };\n        const node = createNode.createNode(value, tag, ctx);\n        if (flow && identity.isCollection(node))\n            node.flow = true;\n        setAnchors();\n        return node;\n    }\n    /**\n     * Convert a key and a value into a `Pair` using the current schema,\n     * recursively wrapping all values as `Scalar` or `Collection` nodes.\n     */\n    createPair(key, value, options = {}) {\n        const k = this.createNode(key, null, options);\n        const v = this.createNode(value, null, options);\n        return new Pair.Pair(k, v);\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        return assertCollection(this.contents) ? this.contents.delete(key) : false;\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        if (Collection.isEmptyPath(path)) {\n            if (this.contents == null)\n                return false;\n            // @ts-expect-error Presumed impossible if Strict extends false\n            this.contents = null;\n            return true;\n        }\n        return assertCollection(this.contents)\n            ? this.contents.deleteIn(path)\n            : false;\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    get(key, keepScalar) {\n        return identity.isCollection(this.contents)\n            ? this.contents.get(key, keepScalar)\n            : undefined;\n    }\n    /**\n     * Returns item at `path`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        if (Collection.isEmptyPath(path))\n            return !keepScalar && identity.isScalar(this.contents)\n                ? this.contents.value\n                : this.contents;\n        return identity.isCollection(this.contents)\n            ? this.contents.getIn(path, keepScalar)\n            : undefined;\n    }\n    /**\n     * Checks if the document includes a value with the key `key`.\n     */\n    has(key) {\n        return identity.isCollection(this.contents) ? this.contents.has(key) : false;\n    }\n    /**\n     * Checks if the document includes a value at `path`.\n     */\n    hasIn(path) {\n        if (Collection.isEmptyPath(path))\n            return this.contents !== undefined;\n        return identity.isCollection(this.contents) ? this.contents.hasIn(path) : false;\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    set(key, value) {\n        if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = Collection.collectionFromPath(this.schema, [key], value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.set(key, value);\n        }\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        if (Collection.isEmptyPath(path)) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = value;\n        }\n        else if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = Collection.collectionFromPath(this.schema, Array.from(path), value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.setIn(path, value);\n        }\n    }\n    /**\n     * Change the YAML version and schema used by the document.\n     * A `null` version disables support for directives, explicit tags, anchors, and aliases.\n     * It also requires the `schema` option to be given as a `Schema` instance value.\n     *\n     * Overrides all previously set schema options.\n     */\n    setSchema(version, options = {}) {\n        if (typeof version === 'number')\n            version = String(version);\n        let opt;\n        switch (version) {\n            case '1.1':\n                if (this.directives)\n                    this.directives.yaml.version = '1.1';\n                else\n                    this.directives = new directives.Directives({ version: '1.1' });\n                opt = { resolveKnownTags: false, schema: 'yaml-1.1' };\n                break;\n            case '1.2':\n            case 'next':\n                if (this.directives)\n                    this.directives.yaml.version = version;\n                else\n                    this.directives = new directives.Directives({ version });\n                opt = { resolveKnownTags: true, schema: 'core' };\n                break;\n            case null:\n                if (this.directives)\n                    delete this.directives;\n                opt = null;\n                break;\n            default: {\n                const sv = JSON.stringify(version);\n                throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${sv}`);\n            }\n        }\n        // Not using `instanceof Schema` to allow for duck typing\n        if (options.schema instanceof Object)\n            this.schema = options.schema;\n        else if (opt)\n            this.schema = new Schema.Schema(Object.assign(opt, options));\n        else\n            throw new Error(`With a null YAML version, the { schema: Schema } option is required`);\n    }\n    // json & jsonArg are only used from toJSON()\n    toJS({ json, jsonArg, mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        const ctx = {\n            anchors: new Map(),\n            doc: this,\n            keep: !json,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS.toJS(this.contents, jsonArg ?? '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver.applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n    /**\n     * A JSON representation of the document `contents`.\n     *\n     * @param jsonArg Used by `JSON.stringify` to indicate the array index or\n     *   property name.\n     */\n    toJSON(jsonArg, onAnchor) {\n        return this.toJS({ json: true, jsonArg, mapAsMap: false, onAnchor });\n    }\n    /** A YAML representation of the document. */\n    toString(options = {}) {\n        if (this.errors.length > 0)\n            throw new Error('Document with errors cannot be stringified');\n        if ('indent' in options &&\n            (!Number.isInteger(options.indent) || Number(options.indent) <= 0)) {\n            const s = JSON.stringify(options.indent);\n            throw new Error(`\"indent\" option must be a positive integer, not ${s}`);\n        }\n        return stringifyDocument.stringifyDocument(this, options);\n    }\n}\nfunction assertCollection(contents) {\n    if (identity.isCollection(contents))\n        return true;\n    throw new Error('Expected a YAML collection as document contents');\n}\n\nexports.Document = Document;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM;IACF,YAAY,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAE;QAClC,mCAAmC,GACnC,IAAI,CAAC,aAAa,GAAG;QACrB,8CAA8C,GAC9C,IAAI,CAAC,OAAO,GAAG;QACf,uCAAuC,GACvC,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,yCAAyC,GACzC,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,SAAS,EAAE;YAAE,OAAO,SAAS,GAAG;QAAC;QACtE,IAAI,YAAY;QAChB,IAAI,OAAO,aAAa,cAAc,MAAM,OAAO,CAAC,WAAW;YAC3D,YAAY;QAChB,OACK,IAAI,YAAY,aAAa,UAAU;YACxC,UAAU;YACV,WAAW;QACf;QACA,MAAM,MAAM,OAAO,MAAM,CAAC;YACtB,aAAa;YACb,kBAAkB;YAClB,UAAU;YACV,cAAc;YACd,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,SAAS;QACb,GAAG;QACH,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,EAAE,OAAO,EAAE,GAAG;QAClB,IAAI,SAAS,aAAa;YACtB,IAAI,CAAC,UAAU,GAAG,QAAQ,WAAW,CAAC,UAAU;YAChD,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAC7B,UAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;QAC9C,OAEI,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,UAAU,CAAC;YAAE;QAAQ;QAC1D,IAAI,CAAC,SAAS,CAAC,SAAS;QACxB,oEAAoE;QACpE,IAAI,CAAC,QAAQ,GACT,UAAU,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,WAAW;IACvE;IACA;;;;KAIC,GACD,QAAQ;QACJ,MAAM,OAAO,OAAO,MAAM,CAAC,SAAS,SAAS,EAAE;YAC3C,CAAC,SAAS,SAAS,CAAC,EAAE;gBAAE,OAAO,SAAS,GAAG;YAAC;QAChD;QACA,KAAK,aAAa,GAAG,IAAI,CAAC,aAAa;QACvC,KAAK,OAAO,GAAG,IAAI,CAAC,OAAO;QAC3B,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QAC/B,KAAK,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnC,KAAK,OAAO,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO;QAC7C,IAAI,IAAI,CAAC,UAAU,EACf,KAAK,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;QAC3C,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QAC/B,oEAAoE;QACpE,KAAK,QAAQ,GAAG,SAAS,MAAM,CAAC,IAAI,CAAC,QAAQ,IACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,IAC/B,IAAI,CAAC,QAAQ;QACnB,IAAI,IAAI,CAAC,KAAK,EACV,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;QACjC,OAAO;IACX;IACA,kCAAkC,GAClC,IAAI,KAAK,EAAE;QACP,IAAI,iBAAiB,IAAI,CAAC,QAAQ,GAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC1B;IACA,kCAAkC,GAClC,MAAM,IAAI,EAAE,KAAK,EAAE;QACf,IAAI,iBAAiB,IAAI,CAAC,QAAQ,GAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM;IAClC;IACA;;;;;;;;KAQC,GACD,YAAY,IAAI,EAAE,IAAI,EAAE;QACpB,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,MAAM,OAAO,QAAQ,WAAW,CAAC,IAAI;YACrC,KAAK,MAAM,GACP,wEAAwE;YACxE,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,QAAQ,aAAa,CAAC,QAAQ,KAAK,QAAQ;QAC7E;QACA,OAAO,IAAI,MAAM,KAAK,CAAC,KAAK,MAAM;IACtC;IACA,WAAW,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;QACjC,IAAI,YAAY;QAChB,IAAI,OAAO,aAAa,YAAY;YAChC,QAAQ,SAAS,IAAI,CAAC;gBAAE,IAAI;YAAM,GAAG,IAAI;YACzC,YAAY;QAChB,OACK,IAAI,MAAM,OAAO,CAAC,WAAW;YAC9B,MAAM,WAAW,CAAC,IAAM,OAAO,MAAM,YAAY,aAAa,UAAU,aAAa;YACrF,MAAM,QAAQ,SAAS,MAAM,CAAC,UAAU,GAAG,CAAC;YAC5C,IAAI,MAAM,MAAM,GAAG,GACf,WAAW,SAAS,MAAM,CAAC;YAC/B,YAAY;QAChB,OACK,IAAI,YAAY,aAAa,UAAU;YACxC,UAAU;YACV,WAAW;QACf;QACA,MAAM,EAAE,qBAAqB,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC;QAChG,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,QAAQ,iBAAiB,CAAC,IAAI,EAC9E,wEAAwE;QACxE,gBAAgB;QAChB,MAAM,MAAM;YACR,uBAAuB,yBAAyB;YAChD,eAAe,iBAAiB;YAChC;YACA;YACA,UAAU;YACV,QAAQ,IAAI,CAAC,MAAM;YACnB;QACJ;QACA,MAAM,OAAO,WAAW,UAAU,CAAC,OAAO,KAAK;QAC/C,IAAI,QAAQ,SAAS,YAAY,CAAC,OAC9B,KAAK,IAAI,GAAG;QAChB;QACA,OAAO;IACX;IACA;;;KAGC,GACD,WAAW,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;QACjC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM;QACrC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM;QACvC,OAAO,IAAI,KAAK,IAAI,CAAC,GAAG;IAC5B;IACA;;;KAGC,GACD,OAAO,GAAG,EAAE;QACR,OAAO,iBAAiB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;IACzE;IACA;;;KAGC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,WAAW,WAAW,CAAC,OAAO;YAC9B,IAAI,IAAI,CAAC,QAAQ,IAAI,MACjB,OAAO;YACX,+DAA+D;YAC/D,IAAI,CAAC,QAAQ,GAAG;YAChB,OAAO;QACX;QACA,OAAO,iBAAiB,IAAI,CAAC,QAAQ,IAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QACvB;IACV;IACA;;;;KAIC,GACD,IAAI,GAAG,EAAE,UAAU,EAAE;QACjB,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,QAAQ,IACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,cACvB;IACV;IACA;;;;KAIC,GACD,MAAM,IAAI,EAAE,UAAU,EAAE;QACpB,IAAI,WAAW,WAAW,CAAC,OACvB,OAAO,CAAC,cAAc,SAAS,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAC/C,IAAI,CAAC,QAAQ,CAAC,KAAK,GACnB,IAAI,CAAC,QAAQ;QACvB,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,QAAQ,IACpC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,cAC1B;IACV;IACA;;KAEC,GACD,IAAI,GAAG,EAAE;QACL,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO;IAC3E;IACA;;KAEC,GACD,MAAM,IAAI,EAAE;QACR,IAAI,WAAW,WAAW,CAAC,OACvB,OAAO,IAAI,CAAC,QAAQ,KAAK;QAC7B,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ;IAC9E;IACA;;;KAGC,GACD,IAAI,GAAG,EAAE,KAAK,EAAE;QACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM;YACvB,oEAAoE;YACpE,IAAI,CAAC,QAAQ,GAAG,WAAW,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE;gBAAC;aAAI,EAAE;QACtE,OACK,IAAI,iBAAiB,IAAI,CAAC,QAAQ,GAAG;YACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK;QAC3B;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE,KAAK,EAAE;QACf,IAAI,WAAW,WAAW,CAAC,OAAO;YAC9B,oEAAoE;YACpE,IAAI,CAAC,QAAQ,GAAG;QACpB,OACK,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM;YAC5B,oEAAoE;YACpE,IAAI,CAAC,QAAQ,GAAG,WAAW,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO;QACjF,OACK,IAAI,iBAAiB,IAAI,CAAC,QAAQ,GAAG;YACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM;QAC9B;IACJ;IACA;;;;;;KAMC,GACD,UAAU,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QAC7B,IAAI,OAAO,YAAY,UACnB,UAAU,OAAO;QACrB,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG;qBAE/B,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,UAAU,CAAC;oBAAE,SAAS;gBAAM;gBACjE,MAAM;oBAAE,kBAAkB;oBAAO,QAAQ;gBAAW;gBACpD;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG;qBAE/B,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,UAAU,CAAC;oBAAE;gBAAQ;gBAC1D,MAAM;oBAAE,kBAAkB;oBAAM,QAAQ;gBAAO;gBAC/C;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,UAAU,EACf,OAAO,IAAI,CAAC,UAAU;gBAC1B,MAAM;gBACN;YACJ;gBAAS;oBACL,MAAM,KAAK,KAAK,SAAS,CAAC;oBAC1B,MAAM,IAAI,MAAM,CAAC,4DAA4D,EAAE,IAAI;gBACvF;QACJ;QACA,yDAAyD;QACzD,IAAI,QAAQ,MAAM,YAAY,QAC1B,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;aAC3B,IAAI,KACL,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,KAAK;aAEnD,MAAM,IAAI,MAAM,CAAC,mEAAmE,CAAC;IAC7F;IACA,6CAA6C;IAC7C,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE;QACrE,MAAM,MAAM;YACR,SAAS,IAAI;YACb,KAAK,IAAI;YACT,MAAM,CAAC;YACP,UAAU,aAAa;YACvB,cAAc;YACd,eAAe,OAAO,kBAAkB,WAAW,gBAAgB;QACvE;QACA,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,IAAI;QACpD,IAAI,OAAO,aAAa,YACpB,KAAK,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,IAAI,OAAO,CAAC,MAAM,GAC3C,SAAS,KAAK;QACtB,OAAO,OAAO,YAAY,aACpB,aAAa,YAAY,CAAC,SAAS;YAAE,IAAI;QAAI,GAAG,IAAI,OACpD;IACV;IACA;;;;;KAKC,GACD,OAAO,OAAO,EAAE,QAAQ,EAAE;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM;YAAM;YAAS,UAAU;YAAO;QAAS;IACtE;IACA,2CAA2C,GAC3C,SAAS,UAAU,CAAC,CAAC,EAAE;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GACrB,MAAM,IAAI,MAAM;QACpB,IAAI,YAAY,WACZ,CAAC,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,CAAC,GAAG;YACpE,MAAM,IAAI,KAAK,SAAS,CAAC,QAAQ,MAAM;YACvC,MAAM,IAAI,MAAM,CAAC,gDAAgD,EAAE,GAAG;QAC1E;QACA,OAAO,kBAAkB,iBAAiB,CAAC,IAAI,EAAE;IACrD;AACJ;AACA,SAAS,iBAAiB,QAAQ;IAC9B,IAAI,SAAS,YAAY,CAAC,WACtB,OAAO;IACX,MAAM,IAAI,MAAM;AACpB;AAEA,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/errors.js"], "sourcesContent": ["'use strict';\n\nclass YAM<PERSON>rror extends <PERSON>rror {\n    constructor(name, pos, code, message) {\n        super();\n        this.name = name;\n        this.code = code;\n        this.message = message;\n        this.pos = pos;\n    }\n}\nclass YAMLParseError extends YAMLError {\n    constructor(pos, code, message) {\n        super('YAMLParseError', pos, code, message);\n    }\n}\nclass YAMLWarning extends YAM<PERSON>rror {\n    constructor(pos, code, message) {\n        super('YAMLWarning', pos, code, message);\n    }\n}\nconst prettifyError = (src, lc) => (error) => {\n    if (error.pos[0] === -1)\n        return;\n    error.linePos = error.pos.map(pos => lc.linePos(pos));\n    const { line, col } = error.linePos[0];\n    error.message += ` at line ${line}, column ${col}`;\n    let ci = col - 1;\n    let lineStr = src\n        .substring(lc.lineStarts[line - 1], lc.lineStarts[line])\n        .replace(/[\\n\\r]+$/, '');\n    // Trim to max 80 chars, keeping col position near the middle\n    if (ci >= 60 && lineStr.length > 80) {\n        const trimStart = Math.min(ci - 39, lineStr.length - 79);\n        lineStr = '…' + lineStr.substring(trimStart);\n        ci -= trimStart - 1;\n    }\n    if (lineStr.length > 80)\n        lineStr = lineStr.substring(0, 79) + '…';\n    // Include previous line in context if pointing at line start\n    if (line > 1 && /^ *$/.test(lineStr.substring(0, ci))) {\n        // Regexp won't match if start is trimmed\n        let prev = src.substring(lc.lineStarts[line - 2], lc.lineStarts[line - 1]);\n        if (prev.length > 80)\n            prev = prev.substring(0, 79) + '…\\n';\n        lineStr = prev + lineStr;\n    }\n    if (/[^ ]/.test(lineStr)) {\n        let count = 1;\n        const end = error.linePos[1];\n        if (end && end.line === line && end.col > col) {\n            count = Math.max(1, Math.min(end.col - col, 80 - ci));\n        }\n        const pointer = ' '.repeat(ci) + '^'.repeat(count);\n        error.message += `:\\n\\n${lineStr}\\n${pointer}\\n`;\n    }\n};\n\nexports.YAMLError = YAMLError;\nexports.YAMLParseError = YAMLParseError;\nexports.YAMLWarning = YAMLWarning;\nexports.prettifyError = prettifyError;\n"], "names": [], "mappings": "AAEA,MAAM,kBAAkB;IACpB,YAAY,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG;IACf;AACJ;AACA,MAAM,uBAAuB;IACzB,YAAY,GAAG,EAAE,IAAI,EAAE,OAAO,CAAE;QAC5B,KAAK,CAAC,kBAAkB,KAAK,MAAM;IACvC;AACJ;AACA,MAAM,oBAAoB;IACtB,YAAY,GAAG,EAAE,IAAI,EAAE,OAAO,CAAE;QAC5B,KAAK,CAAC,eAAe,KAAK,MAAM;IACpC;AACJ;AACA,MAAM,gBAAgB,CAAC,KAAK,KAAO,CAAC;QAChC,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,GAClB;QACJ,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,OAAO,CAAC;QAChD,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE;QACtC,MAAM,OAAO,IAAI,CAAC,SAAS,EAAE,KAAK,SAAS,EAAE,KAAK;QAClD,IAAI,KAAK,MAAM;QACf,IAAI,UAAU,IACT,SAAS,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,KAAK,EACtD,OAAO,CAAC,YAAY;QACzB,6DAA6D;QAC7D,IAAI,MAAM,MAAM,QAAQ,MAAM,GAAG,IAAI;YACjC,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;YACrD,UAAU,MAAM,QAAQ,SAAS,CAAC;YAClC,MAAM,YAAY;QACtB;QACA,IAAI,QAAQ,MAAM,GAAG,IACjB,UAAU,QAAQ,SAAS,CAAC,GAAG,MAAM;QACzC,6DAA6D;QAC7D,IAAI,OAAO,KAAK,OAAO,IAAI,CAAC,QAAQ,SAAS,CAAC,GAAG,MAAM;YACnD,yCAAyC;YACzC,IAAI,OAAO,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE;YACzE,IAAI,KAAK,MAAM,GAAG,IACd,OAAO,KAAK,SAAS,CAAC,GAAG,MAAM;YACnC,UAAU,OAAO;QACrB;QACA,IAAI,OAAO,IAAI,CAAC,UAAU;YACtB,IAAI,QAAQ;YACZ,MAAM,MAAM,MAAM,OAAO,CAAC,EAAE;YAC5B,IAAI,OAAO,IAAI,IAAI,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK;gBAC3C,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,KAAK;YACrD;YACA,MAAM,UAAU,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC;YAC5C,MAAM,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC;QACpD;IACJ;AAEA,QAAQ,SAAS,GAAG;AACpB,QAAQ,cAAc,GAAG;AACzB,QAAQ,WAAW,GAAG;AACtB,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-props.js"], "sourcesContent": ["'use strict';\n\nfunction resolveProps(tokens, { flow, indicator, next, offset, onError, parentIndent, startOnNewline }) {\n    let spaceBefore = false;\n    let atNewline = startOnNewline;\n    let hasSpace = startOnNewline;\n    let comment = '';\n    let commentSep = '';\n    let hasNewline = false;\n    let reqSpace = false;\n    let tab = null;\n    let anchor = null;\n    let tag = null;\n    let newlineAfterProp = null;\n    let comma = null;\n    let found = null;\n    let start = null;\n    for (const token of tokens) {\n        if (reqSpace) {\n            if (token.type !== 'space' &&\n                token.type !== 'newline' &&\n                token.type !== 'comma')\n                onError(token.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n            reqSpace = false;\n        }\n        if (tab) {\n            if (atNewline && token.type !== 'comment' && token.type !== 'newline') {\n                onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n            }\n            tab = null;\n        }\n        switch (token.type) {\n            case 'space':\n                // At the doc level, tabs at line start may be parsed\n                // as leading white space rather than indentation.\n                // In a flow collection, only the parser handles indent.\n                if (!flow &&\n                    (indicator !== 'doc-start' || next?.type !== 'flow-collection') &&\n                    token.source.includes('\\t')) {\n                    tab = token;\n                }\n                hasSpace = true;\n                break;\n            case 'comment': {\n                if (!hasSpace)\n                    onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                const cb = token.source.substring(1) || ' ';\n                if (!comment)\n                    comment = cb;\n                else\n                    comment += commentSep + cb;\n                commentSep = '';\n                atNewline = false;\n                break;\n            }\n            case 'newline':\n                if (atNewline) {\n                    if (comment)\n                        comment += token.source;\n                    else if (!found || indicator !== 'seq-item-ind')\n                        spaceBefore = true;\n                }\n                else\n                    commentSep += token.source;\n                atNewline = true;\n                hasNewline = true;\n                if (anchor || tag)\n                    newlineAfterProp = token;\n                hasSpace = true;\n                break;\n            case 'anchor':\n                if (anchor)\n                    onError(token, 'MULTIPLE_ANCHORS', 'A node can have at most one anchor');\n                if (token.source.endsWith(':'))\n                    onError(token.offset + token.source.length - 1, 'BAD_ALIAS', 'Anchor ending in : is ambiguous', true);\n                anchor = token;\n                start ?? (start = token.offset);\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            case 'tag': {\n                if (tag)\n                    onError(token, 'MULTIPLE_TAGS', 'A node can have at most one tag');\n                tag = token;\n                start ?? (start = token.offset);\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            }\n            case indicator:\n                // Could here handle preceding comments differently\n                if (anchor || tag)\n                    onError(token, 'BAD_PROP_ORDER', `Anchors and tags must be after the ${token.source} indicator`);\n                if (found)\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.source} in ${flow ?? 'collection'}`);\n                found = token;\n                atNewline =\n                    indicator === 'seq-item-ind' || indicator === 'explicit-key-ind';\n                hasSpace = false;\n                break;\n            case 'comma':\n                if (flow) {\n                    if (comma)\n                        onError(token, 'UNEXPECTED_TOKEN', `Unexpected , in ${flow}`);\n                    comma = token;\n                    atNewline = false;\n                    hasSpace = false;\n                    break;\n                }\n            // else fallthrough\n            default:\n                onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.type} token`);\n                atNewline = false;\n                hasSpace = false;\n        }\n    }\n    const last = tokens[tokens.length - 1];\n    const end = last ? last.offset + last.source.length : offset;\n    if (reqSpace &&\n        next &&\n        next.type !== 'space' &&\n        next.type !== 'newline' &&\n        next.type !== 'comma' &&\n        (next.type !== 'scalar' || next.source !== '')) {\n        onError(next.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n    }\n    if (tab &&\n        ((atNewline && tab.indent <= parentIndent) ||\n            next?.type === 'block-map' ||\n            next?.type === 'block-seq'))\n        onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n    return {\n        comma,\n        found,\n        spaceBefore,\n        comment,\n        hasNewline,\n        anchor,\n        tag,\n        newlineAfterProp,\n        end,\n        start: start ?? end\n    };\n}\n\nexports.resolveProps = resolveProps;\n"], "names": [], "mappings": "AAEA,SAAS,aAAa,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE;IAClG,IAAI,cAAc;IAClB,IAAI,YAAY;IAChB,IAAI,WAAW;IACf,IAAI,UAAU;IACd,IAAI,aAAa;IACjB,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI,MAAM;IACV,IAAI,SAAS;IACb,IAAI,MAAM;IACV,IAAI,mBAAmB;IACvB,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,KAAK,MAAM,SAAS,OAAQ;QACxB,IAAI,UAAU;YACV,IAAI,MAAM,IAAI,KAAK,WACf,MAAM,IAAI,KAAK,aACf,MAAM,IAAI,KAAK,SACf,QAAQ,MAAM,MAAM,EAAE,gBAAgB;YAC1C,WAAW;QACf;QACA,IAAI,KAAK;YACL,IAAI,aAAa,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,KAAK,WAAW;gBACnE,QAAQ,KAAK,iBAAiB;YAClC;YACA,MAAM;QACV;QACA,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,qDAAqD;gBACrD,kDAAkD;gBAClD,wDAAwD;gBACxD,IAAI,CAAC,QACD,CAAC,cAAc,eAAe,MAAM,SAAS,iBAAiB,KAC9D,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO;oBAC7B,MAAM;gBACV;gBACA,WAAW;gBACX;YACJ,KAAK;gBAAW;oBACZ,IAAI,CAAC,UACD,QAAQ,OAAO,gBAAgB;oBACnC,MAAM,KAAK,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM;oBACxC,IAAI,CAAC,SACD,UAAU;yBAEV,WAAW,aAAa;oBAC5B,aAAa;oBACb,YAAY;oBACZ;gBACJ;YACA,KAAK;gBACD,IAAI,WAAW;oBACX,IAAI,SACA,WAAW,MAAM,MAAM;yBACtB,IAAI,CAAC,SAAS,cAAc,gBAC7B,cAAc;gBACtB,OAEI,cAAc,MAAM,MAAM;gBAC9B,YAAY;gBACZ,aAAa;gBACb,IAAI,UAAU,KACV,mBAAmB;gBACvB,WAAW;gBACX;YACJ,KAAK;gBACD,IAAI,QACA,QAAQ,OAAO,oBAAoB;gBACvC,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MACtB,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,aAAa,mCAAmC;gBACpG,SAAS;gBACT,SAAS,CAAC,QAAQ,MAAM,MAAM;gBAC9B,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX;YACJ,KAAK;gBAAO;oBACR,IAAI,KACA,QAAQ,OAAO,iBAAiB;oBACpC,MAAM;oBACN,SAAS,CAAC,QAAQ,MAAM,MAAM;oBAC9B,YAAY;oBACZ,WAAW;oBACX,WAAW;oBACX;gBACJ;YACA,KAAK;gBACD,mDAAmD;gBACnD,IAAI,UAAU,KACV,QAAQ,OAAO,kBAAkB,CAAC,mCAAmC,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnG,IAAI,OACA,QAAQ,OAAO,oBAAoB,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,IAAI,EAAE,QAAQ,cAAc;gBAC9F,QAAQ;gBACR,YACI,cAAc,kBAAkB,cAAc;gBAClD,WAAW;gBACX;YACJ,KAAK;gBACD,IAAI,MAAM;oBACN,IAAI,OACA,QAAQ,OAAO,oBAAoB,CAAC,gBAAgB,EAAE,MAAM;oBAChE,QAAQ;oBACR,YAAY;oBACZ,WAAW;oBACX;gBACJ;YACJ,mBAAmB;YACnB;gBACI,QAAQ,OAAO,oBAAoB,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;gBACnE,YAAY;gBACZ,WAAW;QACnB;IACJ;IACA,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;IACtC,MAAM,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,MAAM,GAAG;IACtD,IAAI,YACA,QACA,KAAK,IAAI,KAAK,WACd,KAAK,IAAI,KAAK,aACd,KAAK,IAAI,KAAK,WACd,CAAC,KAAK,IAAI,KAAK,YAAY,KAAK,MAAM,KAAK,EAAE,GAAG;QAChD,QAAQ,KAAK,MAAM,EAAE,gBAAgB;IACzC;IACA,IAAI,OACA,CAAC,AAAC,aAAa,IAAI,MAAM,IAAI,gBACzB,MAAM,SAAS,eACf,MAAM,SAAS,WAAW,GAC9B,QAAQ,KAAK,iBAAiB;IAClC,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAO,SAAS;IACpB;AACJ;AAEA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/util-contains-newline.js"], "sourcesContent": ["'use strict';\n\nfunction containsNewline(key) {\n    if (!key)\n        return null;\n    switch (key.type) {\n        case 'alias':\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            if (key.source.includes('\\n'))\n                return true;\n            if (key.end)\n                for (const st of key.end)\n                    if (st.type === 'newline')\n                        return true;\n            return false;\n        case 'flow-collection':\n            for (const it of key.items) {\n                for (const st of it.start)\n                    if (st.type === 'newline')\n                        return true;\n                if (it.sep)\n                    for (const st of it.sep)\n                        if (st.type === 'newline')\n                            return true;\n                if (containsNewline(it.key) || containsNewline(it.value))\n                    return true;\n            }\n            return false;\n        default:\n            return true;\n    }\n}\n\nexports.containsNewline = containsNewline;\n"], "names": [], "mappings": "AAEA,SAAS,gBAAgB,GAAG;IACxB,IAAI,CAAC,KACD,OAAO;IACX,OAAQ,IAAI,IAAI;QACZ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,OACpB,OAAO;YACX,IAAI,IAAI,GAAG,EACP;gBAAA,KAAK,MAAM,MAAM,IAAI,GAAG,CACpB,IAAI,GAAG,IAAI,KAAK,WACZ,OAAO;YAAI;YACvB,OAAO;QACX,KAAK;YACD,KAAK,MAAM,MAAM,IAAI,KAAK,CAAE;gBACxB,KAAK,MAAM,MAAM,GAAG,KAAK,CACrB,IAAI,GAAG,IAAI,KAAK,WACZ,OAAO;gBACf,IAAI,GAAG,GAAG,EACN;oBAAA,KAAK,MAAM,MAAM,GAAG,GAAG,CACnB,IAAI,GAAG,IAAI,KAAK,WACZ,OAAO;gBAAI;gBACvB,IAAI,gBAAgB,GAAG,GAAG,KAAK,gBAAgB,GAAG,KAAK,GACnD,OAAO;YACf;YACA,OAAO;QACX;YACI,OAAO;IACf;AACJ;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/util-flow-indent-check.js"], "sourcesContent": ["'use strict';\n\nvar utilContainsNewline = require('./util-contains-newline.js');\n\nfunction flowIndentCheck(indent, fc, onError) {\n    if (fc?.type === 'flow-collection') {\n        const end = fc.end[0];\n        if (end.indent === indent &&\n            (end.source === ']' || end.source === '}') &&\n            utilContainsNewline.containsNewline(fc)) {\n            const msg = 'Flow end indicator should be more indented than parent';\n            onError(end, 'BAD_INDENT', msg, true);\n        }\n    }\n}\n\nexports.flowIndentCheck = flowIndentCheck;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,SAAS,gBAAgB,MAAM,EAAE,EAAE,EAAE,OAAO;IACxC,IAAI,IAAI,SAAS,mBAAmB;QAChC,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE;QACrB,IAAI,IAAI,MAAM,KAAK,UACf,CAAC,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,GAAG,KACzC,oBAAoB,eAAe,CAAC,KAAK;YACzC,MAAM,MAAM;YACZ,QAAQ,KAAK,cAAc,KAAK;QACpC;IACJ;AACJ;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/util-map-includes.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\n\nfunction mapIncludes(ctx, items, search) {\n    const { uniqueKeys } = ctx.options;\n    if (uniqueKeys === false)\n        return false;\n    const isEqual = typeof uniqueKeys === 'function'\n        ? uniqueKeys\n        : (a, b) => a === b || (identity.isScalar(a) && identity.isScalar(b) && a.value === b.value);\n    return items.some(pair => isEqual(pair.key, search));\n}\n\nexports.mapIncludes = mapIncludes;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,SAAS,YAAY,GAAG,EAAE,KAAK,EAAE,MAAM;IACnC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,OAAO;IAClC,IAAI,eAAe,OACf,OAAO;IACX,MAAM,UAAU,OAAO,eAAe,aAChC,aACA,CAAC,GAAG,IAAM,MAAM,KAAM,SAAS,QAAQ,CAAC,MAAM,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK;IAC/F,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,QAAQ,KAAK,GAAG,EAAE;AAChD;AAEA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-block-map.js"], "sourcesContent": ["'use strict';\n\nvar Pair = require('../nodes/Pair.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilContainsNewline = require('./util-contains-newline.js');\nvar utilFlowIndentCheck = require('./util-flow-indent-check.js');\nvar utilMapIncludes = require('./util-map-includes.js');\n\nconst startColMsg = 'All mapping items must start at the same column';\nfunction resolveBlockMap({ composeNode, composeEmptyNode }, ctx, bm, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLMap.YAMLMap;\n    const map = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    let offset = bm.offset;\n    let commentEnd = null;\n    for (const collItem of bm.items) {\n        const { start, key, sep, value } = collItem;\n        // key properties\n        const keyProps = resolveProps.resolveProps(start, {\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: true\n        });\n        const implicitKey = !keyProps.found;\n        if (implicitKey) {\n            if (key) {\n                if (key.type === 'block-seq')\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'A block sequence may not be used as an implicit map key');\n                else if ('indent' in key && key.indent !== bm.indent)\n                    onError(offset, 'BAD_INDENT', startColMsg);\n            }\n            if (!keyProps.anchor && !keyProps.tag && !sep) {\n                commentEnd = keyProps.end;\n                if (keyProps.comment) {\n                    if (map.comment)\n                        map.comment += '\\n' + keyProps.comment;\n                    else\n                        map.comment = keyProps.comment;\n                }\n                continue;\n            }\n            if (keyProps.newlineAfterProp || utilContainsNewline.containsNewline(key)) {\n                onError(key ?? start[start.length - 1], 'MULTILINE_IMPLICIT_KEY', 'Implicit keys need to be on a single line');\n            }\n        }\n        else if (keyProps.found?.indent !== bm.indent) {\n            onError(offset, 'BAD_INDENT', startColMsg);\n        }\n        // key value\n        ctx.atKey = true;\n        const keyStart = keyProps.end;\n        const keyNode = key\n            ? composeNode(ctx, key, keyProps, onError)\n            : composeEmptyNode(ctx, keyStart, start, null, keyProps, onError);\n        if (ctx.schema.compat)\n            utilFlowIndentCheck.flowIndentCheck(bm.indent, key, onError);\n        ctx.atKey = false;\n        if (utilMapIncludes.mapIncludes(ctx, map.items, keyNode))\n            onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n        // value properties\n        const valueProps = resolveProps.resolveProps(sep ?? [], {\n            indicator: 'map-value-ind',\n            next: value,\n            offset: keyNode.range[2],\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: !key || key.type === 'block-scalar'\n        });\n        offset = valueProps.end;\n        if (valueProps.found) {\n            if (implicitKey) {\n                if (value?.type === 'block-map' && !valueProps.hasNewline)\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'Nested mappings are not allowed in compact mappings');\n                if (ctx.options.strict &&\n                    keyProps.start < valueProps.found.offset - 1024)\n                    onError(keyNode.range, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit block mapping key');\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : composeEmptyNode(ctx, offset, sep, null, valueProps, onError);\n            if (ctx.schema.compat)\n                utilFlowIndentCheck.flowIndentCheck(bm.indent, value, onError);\n            offset = valueNode.range[2];\n            const pair = new Pair.Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n        else {\n            // key with no value\n            if (implicitKey)\n                onError(keyNode.range, 'MISSING_CHAR', 'Implicit map keys need to be followed by map values');\n            if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair.Pair(keyNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n    }\n    if (commentEnd && commentEnd < offset)\n        onError(commentEnd, 'IMPOSSIBLE', 'Map comment with trailing content');\n    map.range = [bm.offset, offset, commentEnd ?? offset];\n    return map;\n}\n\nexports.resolveBlockMap = resolveBlockMap;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,cAAc;AACpB,SAAS,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG;IAC7E,MAAM,YAAY,KAAK,aAAa,QAAQ,OAAO;IACnD,MAAM,MAAM,IAAI,UAAU,IAAI,MAAM;IACpC,IAAI,IAAI,MAAM,EACV,IAAI,MAAM,GAAG;IACjB,IAAI,SAAS,GAAG,MAAM;IACtB,IAAI,aAAa;IACjB,KAAK,MAAM,YAAY,GAAG,KAAK,CAAE;QAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;QACnC,iBAAiB;QACjB,MAAM,WAAW,aAAa,YAAY,CAAC,OAAO;YAC9C,WAAW;YACX,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB;YACA;YACA,cAAc,GAAG,MAAM;YACvB,gBAAgB;QACpB;QACA,MAAM,cAAc,CAAC,SAAS,KAAK;QACnC,IAAI,aAAa;YACb,IAAI,KAAK;gBACL,IAAI,IAAI,IAAI,KAAK,aACb,QAAQ,QAAQ,yBAAyB;qBACxC,IAAI,YAAY,OAAO,IAAI,MAAM,KAAK,GAAG,MAAM,EAChD,QAAQ,QAAQ,cAAc;YACtC;YACA,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;gBAC3C,aAAa,SAAS,GAAG;gBACzB,IAAI,SAAS,OAAO,EAAE;oBAClB,IAAI,IAAI,OAAO,EACX,IAAI,OAAO,IAAI,OAAO,SAAS,OAAO;yBAEtC,IAAI,OAAO,GAAG,SAAS,OAAO;gBACtC;gBACA;YACJ;YACA,IAAI,SAAS,gBAAgB,IAAI,oBAAoB,eAAe,CAAC,MAAM;gBACvE,QAAQ,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAE,0BAA0B;YACtE;QACJ,OACK,IAAI,SAAS,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE;YAC3C,QAAQ,QAAQ,cAAc;QAClC;QACA,YAAY;QACZ,IAAI,KAAK,GAAG;QACZ,MAAM,WAAW,SAAS,GAAG;QAC7B,MAAM,UAAU,MACV,YAAY,KAAK,KAAK,UAAU,WAChC,iBAAiB,KAAK,UAAU,OAAO,MAAM,UAAU;QAC7D,IAAI,IAAI,MAAM,CAAC,MAAM,EACjB,oBAAoB,eAAe,CAAC,GAAG,MAAM,EAAE,KAAK;QACxD,IAAI,KAAK,GAAG;QACZ,IAAI,gBAAgB,WAAW,CAAC,KAAK,IAAI,KAAK,EAAE,UAC5C,QAAQ,UAAU,iBAAiB;QACvC,mBAAmB;QACnB,MAAM,aAAa,aAAa,YAAY,CAAC,OAAO,EAAE,EAAE;YACpD,WAAW;YACX,MAAM;YACN,QAAQ,QAAQ,KAAK,CAAC,EAAE;YACxB;YACA,cAAc,GAAG,MAAM;YACvB,gBAAgB,CAAC,OAAO,IAAI,IAAI,KAAK;QACzC;QACA,SAAS,WAAW,GAAG;QACvB,IAAI,WAAW,KAAK,EAAE;YAClB,IAAI,aAAa;gBACb,IAAI,OAAO,SAAS,eAAe,CAAC,WAAW,UAAU,EACrD,QAAQ,QAAQ,yBAAyB;gBAC7C,IAAI,IAAI,OAAO,CAAC,MAAM,IAClB,SAAS,KAAK,GAAG,WAAW,KAAK,CAAC,MAAM,GAAG,MAC3C,QAAQ,QAAQ,KAAK,EAAE,uBAAuB;YACtD;YACA,cAAc;YACd,MAAM,YAAY,QACZ,YAAY,KAAK,OAAO,YAAY,WACpC,iBAAiB,KAAK,QAAQ,KAAK,MAAM,YAAY;YAC3D,IAAI,IAAI,MAAM,CAAC,MAAM,EACjB,oBAAoB,eAAe,CAAC,GAAG,MAAM,EAAE,OAAO;YAC1D,SAAS,UAAU,KAAK,CAAC,EAAE;YAC3B,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,SAAS;YACpC,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAC5B,KAAK,QAAQ,GAAG;YACpB,IAAI,KAAK,CAAC,IAAI,CAAC;QACnB,OACK;YACD,oBAAoB;YACpB,IAAI,aACA,QAAQ,QAAQ,KAAK,EAAE,gBAAgB;YAC3C,IAAI,WAAW,OAAO,EAAE;gBACpB,IAAI,QAAQ,OAAO,EACf,QAAQ,OAAO,IAAI,OAAO,WAAW,OAAO;qBAE5C,QAAQ,OAAO,GAAG,WAAW,OAAO;YAC5C;YACA,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC;YAC3B,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAC5B,KAAK,QAAQ,GAAG;YACpB,IAAI,KAAK,CAAC,IAAI,CAAC;QACnB;IACJ;IACA,IAAI,cAAc,aAAa,QAC3B,QAAQ,YAAY,cAAc;IACtC,IAAI,KAAK,GAAG;QAAC,GAAG,MAAM;QAAE;QAAQ,cAAc;KAAO;IACrD,OAAO;AACX;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-block-seq.js"], "sourcesContent": ["'use strict';\n\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilFlowIndentCheck = require('./util-flow-indent-check.js');\n\nfunction resolveBlockSeq({ composeNode, composeEmptyNode }, ctx, bs, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLSeq.YAMLSeq;\n    const seq = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = bs.offset;\n    let commentEnd = null;\n    for (const { start, value } of bs.items) {\n        const props = resolveProps.resolveProps(start, {\n            indicator: 'seq-item-ind',\n            next: value,\n            offset,\n            onError,\n            parentIndent: bs.indent,\n            startOnNewline: true\n        });\n        if (!props.found) {\n            if (props.anchor || props.tag || value) {\n                if (value && value.type === 'block-seq')\n                    onError(props.end, 'BAD_INDENT', 'All sequence items must start at the same column');\n                else\n                    onError(offset, 'MISSING_CHAR', 'Sequence item without - indicator');\n            }\n            else {\n                commentEnd = props.end;\n                if (props.comment)\n                    seq.comment = props.comment;\n                continue;\n            }\n        }\n        const node = value\n            ? composeNode(ctx, value, props, onError)\n            : composeEmptyNode(ctx, props.end, start, null, props, onError);\n        if (ctx.schema.compat)\n            utilFlowIndentCheck.flowIndentCheck(bs.indent, value, onError);\n        offset = node.range[2];\n        seq.items.push(node);\n    }\n    seq.range = [bs.offset, offset, commentEnd ?? offset];\n    return seq;\n}\n\nexports.resolveBlockSeq = resolveBlockSeq;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG;IAC7E,MAAM,YAAY,KAAK,aAAa,QAAQ,OAAO;IACnD,MAAM,MAAM,IAAI,UAAU,IAAI,MAAM;IACpC,IAAI,IAAI,MAAM,EACV,IAAI,MAAM,GAAG;IACjB,IAAI,IAAI,KAAK,EACT,IAAI,KAAK,GAAG;IAChB,IAAI,SAAS,GAAG,MAAM;IACtB,IAAI,aAAa;IACjB,KAAK,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAG,KAAK,CAAE;QACrC,MAAM,QAAQ,aAAa,YAAY,CAAC,OAAO;YAC3C,WAAW;YACX,MAAM;YACN;YACA;YACA,cAAc,GAAG,MAAM;YACvB,gBAAgB;QACpB;QACA,IAAI,CAAC,MAAM,KAAK,EAAE;YACd,IAAI,MAAM,MAAM,IAAI,MAAM,GAAG,IAAI,OAAO;gBACpC,IAAI,SAAS,MAAM,IAAI,KAAK,aACxB,QAAQ,MAAM,GAAG,EAAE,cAAc;qBAEjC,QAAQ,QAAQ,gBAAgB;YACxC,OACK;gBACD,aAAa,MAAM,GAAG;gBACtB,IAAI,MAAM,OAAO,EACb,IAAI,OAAO,GAAG,MAAM,OAAO;gBAC/B;YACJ;QACJ;QACA,MAAM,OAAO,QACP,YAAY,KAAK,OAAO,OAAO,WAC/B,iBAAiB,KAAK,MAAM,GAAG,EAAE,OAAO,MAAM,OAAO;QAC3D,IAAI,IAAI,MAAM,CAAC,MAAM,EACjB,oBAAoB,eAAe,CAAC,GAAG,MAAM,EAAE,OAAO;QAC1D,SAAS,KAAK,KAAK,CAAC,EAAE;QACtB,IAAI,KAAK,CAAC,IAAI,CAAC;IACnB;IACA,IAAI,KAAK,GAAG;QAAC,GAAG,MAAM;QAAE;QAAQ,cAAc;KAAO;IACrD,OAAO;AACX;AAEA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-end.js"], "sourcesContent": ["'use strict';\n\nfunction resolveEnd(end, offset, reqSpace, onError) {\n    let comment = '';\n    if (end) {\n        let hasSpace = false;\n        let sep = '';\n        for (const token of end) {\n            const { source, type } = token;\n            switch (type) {\n                case 'space':\n                    hasSpace = true;\n                    break;\n                case 'comment': {\n                    if (reqSpace && !hasSpace)\n                        onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                    const cb = source.substring(1) || ' ';\n                    if (!comment)\n                        comment = cb;\n                    else\n                        comment += sep + cb;\n                    sep = '';\n                    break;\n                }\n                case 'newline':\n                    if (comment)\n                        sep += source;\n                    hasSpace = true;\n                    break;\n                default:\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${type} at node end`);\n            }\n            offset += source.length;\n        }\n    }\n    return { comment, offset };\n}\n\nexports.resolveEnd = resolveEnd;\n"], "names": [], "mappings": "AAEA,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC9C,IAAI,UAAU;IACd,IAAI,KAAK;QACL,IAAI,WAAW;QACf,IAAI,MAAM;QACV,KAAK,MAAM,SAAS,IAAK;YACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;YACzB,OAAQ;gBACJ,KAAK;oBACD,WAAW;oBACX;gBACJ,KAAK;oBAAW;wBACZ,IAAI,YAAY,CAAC,UACb,QAAQ,OAAO,gBAAgB;wBACnC,MAAM,KAAK,OAAO,SAAS,CAAC,MAAM;wBAClC,IAAI,CAAC,SACD,UAAU;6BAEV,WAAW,MAAM;wBACrB,MAAM;wBACN;oBACJ;gBACA,KAAK;oBACD,IAAI,SACA,OAAO;oBACX,WAAW;oBACX;gBACJ;oBACI,QAAQ,OAAO,oBAAoB,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC;YAC3E;YACA,UAAU,OAAO,MAAM;QAC3B;IACJ;IACA,OAAO;QAAE;QAAS;IAAO;AAC7B;AAEA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3884, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-flow-collection.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Pair = require('../nodes/Pair.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveEnd = require('./resolve-end.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilContainsNewline = require('./util-contains-newline.js');\nvar utilMapIncludes = require('./util-map-includes.js');\n\nconst blockMsg = 'Block collections are not allowed within flow collections';\nconst isBlock = (token) => token && (token.type === 'block-map' || token.type === 'block-seq');\nfunction resolveFlowCollection({ composeNode, composeEmptyNode }, ctx, fc, onError, tag) {\n    const isMap = fc.start.source === '{';\n    const fcName = isMap ? 'flow map' : 'flow sequence';\n    const NodeClass = (tag?.nodeClass ?? (isMap ? YAMLMap.YAMLMap : YAMLSeq.YAMLSeq));\n    const coll = new NodeClass(ctx.schema);\n    coll.flow = true;\n    const atRoot = ctx.atRoot;\n    if (atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = fc.offset + fc.start.source.length;\n    for (let i = 0; i < fc.items.length; ++i) {\n        const collItem = fc.items[i];\n        const { start, key, sep, value } = collItem;\n        const props = resolveProps.resolveProps(start, {\n            flow: fcName,\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: fc.indent,\n            startOnNewline: false\n        });\n        if (!props.found) {\n            if (!props.anchor && !props.tag && !sep && !value) {\n                if (i === 0 && props.comma)\n                    onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n                else if (i < fc.items.length - 1)\n                    onError(props.start, 'UNEXPECTED_TOKEN', `Unexpected empty item in ${fcName}`);\n                if (props.comment) {\n                    if (coll.comment)\n                        coll.comment += '\\n' + props.comment;\n                    else\n                        coll.comment = props.comment;\n                }\n                offset = props.end;\n                continue;\n            }\n            if (!isMap && ctx.options.strict && utilContainsNewline.containsNewline(key))\n                onError(key, // checked by containsNewline()\n                'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n        }\n        if (i === 0) {\n            if (props.comma)\n                onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n        }\n        else {\n            if (!props.comma)\n                onError(props.start, 'MISSING_CHAR', `Missing , between ${fcName} items`);\n            if (props.comment) {\n                let prevItemComment = '';\n                loop: for (const st of start) {\n                    switch (st.type) {\n                        case 'comma':\n                        case 'space':\n                            break;\n                        case 'comment':\n                            prevItemComment = st.source.substring(1);\n                            break loop;\n                        default:\n                            break loop;\n                    }\n                }\n                if (prevItemComment) {\n                    let prev = coll.items[coll.items.length - 1];\n                    if (identity.isPair(prev))\n                        prev = prev.value ?? prev.key;\n                    if (prev.comment)\n                        prev.comment += '\\n' + prevItemComment;\n                    else\n                        prev.comment = prevItemComment;\n                    props.comment = props.comment.substring(prevItemComment.length + 1);\n                }\n            }\n        }\n        if (!isMap && !sep && !props.found) {\n            // item is a value in a seq\n            // → key & sep are empty, start does not include ? or :\n            const valueNode = value\n                ? composeNode(ctx, value, props, onError)\n                : composeEmptyNode(ctx, props.end, sep, null, props, onError);\n            coll.items.push(valueNode);\n            offset = valueNode.range[2];\n            if (isBlock(value))\n                onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n        }\n        else {\n            // item is a key+value pair\n            // key value\n            ctx.atKey = true;\n            const keyStart = props.end;\n            const keyNode = key\n                ? composeNode(ctx, key, props, onError)\n                : composeEmptyNode(ctx, keyStart, start, null, props, onError);\n            if (isBlock(key))\n                onError(keyNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            ctx.atKey = false;\n            // value properties\n            const valueProps = resolveProps.resolveProps(sep ?? [], {\n                flow: fcName,\n                indicator: 'map-value-ind',\n                next: value,\n                offset: keyNode.range[2],\n                onError,\n                parentIndent: fc.indent,\n                startOnNewline: false\n            });\n            if (valueProps.found) {\n                if (!isMap && !props.found && ctx.options.strict) {\n                    if (sep)\n                        for (const st of sep) {\n                            if (st === valueProps.found)\n                                break;\n                            if (st.type === 'newline') {\n                                onError(st, 'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n                                break;\n                            }\n                        }\n                    if (props.start < valueProps.found.offset - 1024)\n                        onError(valueProps.found, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit flow sequence key');\n                }\n            }\n            else if (value) {\n                if ('source' in value && value.source && value.source[0] === ':')\n                    onError(value, 'MISSING_CHAR', `Missing space after : in ${fcName}`);\n                else\n                    onError(valueProps.start, 'MISSING_CHAR', `Missing , or : between ${fcName} items`);\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : valueProps.found\n                    ? composeEmptyNode(ctx, valueProps.end, sep, null, valueProps, onError)\n                    : null;\n            if (valueNode) {\n                if (isBlock(value))\n                    onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            }\n            else if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair.Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            if (isMap) {\n                const map = coll;\n                if (utilMapIncludes.mapIncludes(ctx, map.items, keyNode))\n                    onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n                map.items.push(pair);\n            }\n            else {\n                const map = new YAMLMap.YAMLMap(ctx.schema);\n                map.flow = true;\n                map.items.push(pair);\n                const endRange = (valueNode ?? keyNode).range;\n                map.range = [keyNode.range[0], endRange[1], endRange[2]];\n                coll.items.push(map);\n            }\n            offset = valueNode ? valueNode.range[2] : valueProps.end;\n        }\n    }\n    const expectedEnd = isMap ? '}' : ']';\n    const [ce, ...ee] = fc.end;\n    let cePos = offset;\n    if (ce && ce.source === expectedEnd)\n        cePos = ce.offset + ce.source.length;\n    else {\n        const name = fcName[0].toUpperCase() + fcName.substring(1);\n        const msg = atRoot\n            ? `${name} must end with a ${expectedEnd}`\n            : `${name} in block collection must be sufficiently indented and end with a ${expectedEnd}`;\n        onError(offset, atRoot ? 'MISSING_CHAR' : 'BAD_INDENT', msg);\n        if (ce && ce.source.length !== 1)\n            ee.unshift(ce);\n    }\n    if (ee.length > 0) {\n        const end = resolveEnd.resolveEnd(ee, cePos, ctx.options.strict, onError);\n        if (end.comment) {\n            if (coll.comment)\n                coll.comment += '\\n' + end.comment;\n            else\n                coll.comment = end.comment;\n        }\n        coll.range = [fc.offset, cePos, end.offset];\n    }\n    else {\n        coll.range = [fc.offset, cePos, cePos];\n    }\n    return coll;\n}\n\nexports.resolveFlowCollection = resolveFlowCollection;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,WAAW;AACjB,MAAM,UAAU,CAAC,QAAU,SAAS,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,WAAW;AAC7F,SAAS,sBAAsB,EAAE,WAAW,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG;IACnF,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,KAAK;IAClC,MAAM,SAAS,QAAQ,aAAa;IACpC,MAAM,YAAa,KAAK,aAAa,CAAC,QAAQ,QAAQ,OAAO,GAAG,QAAQ,OAAO;IAC/E,MAAM,OAAO,IAAI,UAAU,IAAI,MAAM;IACrC,KAAK,IAAI,GAAG;IACZ,MAAM,SAAS,IAAI,MAAM;IACzB,IAAI,QACA,IAAI,MAAM,GAAG;IACjB,IAAI,IAAI,KAAK,EACT,IAAI,KAAK,GAAG;IAChB,IAAI,SAAS,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM;IAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;QACtC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;QACnC,MAAM,QAAQ,aAAa,YAAY,CAAC,OAAO;YAC3C,MAAM;YACN,WAAW;YACX,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB;YACA;YACA,cAAc,GAAG,MAAM;YACvB,gBAAgB;QACpB;QACA,IAAI,CAAC,MAAM,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC/C,IAAI,MAAM,KAAK,MAAM,KAAK,EACtB,QAAQ,MAAM,KAAK,EAAE,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ;qBACnE,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,GAC3B,QAAQ,MAAM,KAAK,EAAE,oBAAoB,CAAC,yBAAyB,EAAE,QAAQ;gBACjF,IAAI,MAAM,OAAO,EAAE;oBACf,IAAI,KAAK,OAAO,EACZ,KAAK,OAAO,IAAI,OAAO,MAAM,OAAO;yBAEpC,KAAK,OAAO,GAAG,MAAM,OAAO;gBACpC;gBACA,SAAS,MAAM,GAAG;gBAClB;YACJ;YACA,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,IAAI,oBAAoB,eAAe,CAAC,MACpE,QAAQ,KACR,0BAA0B;QAClC;QACA,IAAI,MAAM,GAAG;YACT,IAAI,MAAM,KAAK,EACX,QAAQ,MAAM,KAAK,EAAE,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ;QAC5E,OACK;YACD,IAAI,CAAC,MAAM,KAAK,EACZ,QAAQ,MAAM,KAAK,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC;YAC5E,IAAI,MAAM,OAAO,EAAE;gBACf,IAAI,kBAAkB;gBACtB,MAAM,KAAK,MAAM,MAAM,MAAO;oBAC1B,OAAQ,GAAG,IAAI;wBACX,KAAK;wBACL,KAAK;4BACD;wBACJ,KAAK;4BACD,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC;4BACtC,MAAM;wBACV;4BACI,MAAM;oBACd;gBACJ;gBACA,IAAI,iBAAiB;oBACjB,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,EAAE;oBAC5C,IAAI,SAAS,MAAM,CAAC,OAChB,OAAO,KAAK,KAAK,IAAI,KAAK,GAAG;oBACjC,IAAI,KAAK,OAAO,EACZ,KAAK,OAAO,IAAI,OAAO;yBAEvB,KAAK,OAAO,GAAG;oBACnB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,MAAM,GAAG;gBACrE;YACJ;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE;YAChC,2BAA2B;YAC3B,uDAAuD;YACvD,MAAM,YAAY,QACZ,YAAY,KAAK,OAAO,OAAO,WAC/B,iBAAiB,KAAK,MAAM,GAAG,EAAE,KAAK,MAAM,OAAO;YACzD,KAAK,KAAK,CAAC,IAAI,CAAC;YAChB,SAAS,UAAU,KAAK,CAAC,EAAE;YAC3B,IAAI,QAAQ,QACR,QAAQ,UAAU,KAAK,EAAE,iBAAiB;QAClD,OACK;YACD,2BAA2B;YAC3B,YAAY;YACZ,IAAI,KAAK,GAAG;YACZ,MAAM,WAAW,MAAM,GAAG;YAC1B,MAAM,UAAU,MACV,YAAY,KAAK,KAAK,OAAO,WAC7B,iBAAiB,KAAK,UAAU,OAAO,MAAM,OAAO;YAC1D,IAAI,QAAQ,MACR,QAAQ,QAAQ,KAAK,EAAE,iBAAiB;YAC5C,IAAI,KAAK,GAAG;YACZ,mBAAmB;YACnB,MAAM,aAAa,aAAa,YAAY,CAAC,OAAO,EAAE,EAAE;gBACpD,MAAM;gBACN,WAAW;gBACX,MAAM;gBACN,QAAQ,QAAQ,KAAK,CAAC,EAAE;gBACxB;gBACA,cAAc,GAAG,MAAM;gBACvB,gBAAgB;YACpB;YACA,IAAI,WAAW,KAAK,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;oBAC9C,IAAI,KACA,KAAK,MAAM,MAAM,IAAK;wBAClB,IAAI,OAAO,WAAW,KAAK,EACvB;wBACJ,IAAI,GAAG,IAAI,KAAK,WAAW;4BACvB,QAAQ,IAAI,0BAA0B;4BACtC;wBACJ;oBACJ;oBACJ,IAAI,MAAM,KAAK,GAAG,WAAW,KAAK,CAAC,MAAM,GAAG,MACxC,QAAQ,WAAW,KAAK,EAAE,uBAAuB;gBACzD;YACJ,OACK,IAAI,OAAO;gBACZ,IAAI,YAAY,SAAS,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,KACzD,QAAQ,OAAO,gBAAgB,CAAC,yBAAyB,EAAE,QAAQ;qBAEnE,QAAQ,WAAW,KAAK,EAAE,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,MAAM,CAAC;YAC1F;YACA,cAAc;YACd,MAAM,YAAY,QACZ,YAAY,KAAK,OAAO,YAAY,WACpC,WAAW,KAAK,GACZ,iBAAiB,KAAK,WAAW,GAAG,EAAE,KAAK,MAAM,YAAY,WAC7D;YACV,IAAI,WAAW;gBACX,IAAI,QAAQ,QACR,QAAQ,UAAU,KAAK,EAAE,iBAAiB;YAClD,OACK,IAAI,WAAW,OAAO,EAAE;gBACzB,IAAI,QAAQ,OAAO,EACf,QAAQ,OAAO,IAAI,OAAO,WAAW,OAAO;qBAE5C,QAAQ,OAAO,GAAG,WAAW,OAAO;YAC5C;YACA,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,SAAS;YACpC,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAC5B,KAAK,QAAQ,GAAG;YACpB,IAAI,OAAO;gBACP,MAAM,MAAM;gBACZ,IAAI,gBAAgB,WAAW,CAAC,KAAK,IAAI,KAAK,EAAE,UAC5C,QAAQ,UAAU,iBAAiB;gBACvC,IAAI,KAAK,CAAC,IAAI,CAAC;YACnB,OACK;gBACD,MAAM,MAAM,IAAI,QAAQ,OAAO,CAAC,IAAI,MAAM;gBAC1C,IAAI,IAAI,GAAG;gBACX,IAAI,KAAK,CAAC,IAAI,CAAC;gBACf,MAAM,WAAW,CAAC,aAAa,OAAO,EAAE,KAAK;gBAC7C,IAAI,KAAK,GAAG;oBAAC,QAAQ,KAAK,CAAC,EAAE;oBAAE,QAAQ,CAAC,EAAE;oBAAE,QAAQ,CAAC,EAAE;iBAAC;gBACxD,KAAK,KAAK,CAAC,IAAI,CAAC;YACpB;YACA,SAAS,YAAY,UAAU,KAAK,CAAC,EAAE,GAAG,WAAW,GAAG;QAC5D;IACJ;IACA,MAAM,cAAc,QAAQ,MAAM;IAClC,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;IAC1B,IAAI,QAAQ;IACZ,IAAI,MAAM,GAAG,MAAM,KAAK,aACpB,QAAQ,GAAG,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM;SACnC;QACD,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,WAAW,KAAK,OAAO,SAAS,CAAC;QACxD,MAAM,MAAM,SACN,GAAG,KAAK,iBAAiB,EAAE,aAAa,GACxC,GAAG,KAAK,kEAAkE,EAAE,aAAa;QAC/F,QAAQ,QAAQ,SAAS,iBAAiB,cAAc;QACxD,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,GAC3B,GAAG,OAAO,CAAC;IACnB;IACA,IAAI,GAAG,MAAM,GAAG,GAAG;QACf,MAAM,MAAM,WAAW,UAAU,CAAC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;QACjE,IAAI,IAAI,OAAO,EAAE;YACb,IAAI,KAAK,OAAO,EACZ,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO;iBAElC,KAAK,OAAO,GAAG,IAAI,OAAO;QAClC;QACA,KAAK,KAAK,GAAG;YAAC,GAAG,MAAM;YAAE;YAAO,IAAI,MAAM;SAAC;IAC/C,OACK;QACD,KAAK,KAAK,GAAG;YAAC,GAAG,MAAM;YAAE;YAAO;SAAM;IAC1C;IACA,OAAO;AACX;AAEA,QAAQ,qBAAqB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/compose-collection.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveBlockMap = require('./resolve-block-map.js');\nvar resolveBlockSeq = require('./resolve-block-seq.js');\nvar resolveFlowCollection = require('./resolve-flow-collection.js');\n\nfunction resolveCollection(CN, ctx, token, onError, tagName, tag) {\n    const coll = token.type === 'block-map'\n        ? resolveBlockMap.resolveBlockMap(CN, ctx, token, onError, tag)\n        : token.type === 'block-seq'\n            ? resolveBlockSeq.resolveBlockSeq(CN, ctx, token, onError, tag)\n            : resolveFlowCollection.resolveFlowCollection(CN, ctx, token, onError, tag);\n    const Coll = coll.constructor;\n    // If we got a tagName matching the class, or the tag name is '!',\n    // then use the tagName from the node class used to create it.\n    if (tagName === '!' || tagName === Coll.tagName) {\n        coll.tag = Coll.tagName;\n        return coll;\n    }\n    if (tagName)\n        coll.tag = tagName;\n    return coll;\n}\nfunction composeCollection(CN, ctx, token, props, onError) {\n    const tagToken = props.tag;\n    const tagName = !tagToken\n        ? null\n        : ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg));\n    if (token.type === 'block-seq') {\n        const { anchor, newlineAfterProp: nl } = props;\n        const lastProp = anchor && tagToken\n            ? anchor.offset > tagToken.offset\n                ? anchor\n                : tagToken\n            : (anchor ?? tagToken);\n        if (lastProp && (!nl || nl.offset < lastProp.offset)) {\n            const message = 'Missing newline after block sequence props';\n            onError(lastProp, 'MISSING_CHAR', message);\n        }\n    }\n    const expType = token.type === 'block-map'\n        ? 'map'\n        : token.type === 'block-seq'\n            ? 'seq'\n            : token.start.source === '{'\n                ? 'map'\n                : 'seq';\n    // shortcut: check if it's a generic YAMLMap or YAMLSeq\n    // before jumping into the custom tag logic.\n    if (!tagToken ||\n        !tagName ||\n        tagName === '!' ||\n        (tagName === YAMLMap.YAMLMap.tagName && expType === 'map') ||\n        (tagName === YAMLSeq.YAMLSeq.tagName && expType === 'seq')) {\n        return resolveCollection(CN, ctx, token, onError, tagName);\n    }\n    let tag = ctx.schema.tags.find(t => t.tag === tagName && t.collection === expType);\n    if (!tag) {\n        const kt = ctx.schema.knownTags[tagName];\n        if (kt && kt.collection === expType) {\n            ctx.schema.tags.push(Object.assign({}, kt, { default: false }));\n            tag = kt;\n        }\n        else {\n            if (kt) {\n                onError(tagToken, 'BAD_COLLECTION_TYPE', `${kt.tag} used for ${expType} collection, but expects ${kt.collection ?? 'scalar'}`, true);\n            }\n            else {\n                onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, true);\n            }\n            return resolveCollection(CN, ctx, token, onError, tagName);\n        }\n    }\n    const coll = resolveCollection(CN, ctx, token, onError, tagName, tag);\n    const res = tag.resolve?.(coll, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg), ctx.options) ?? coll;\n    const node = identity.isNode(res)\n        ? res\n        : new Scalar.Scalar(res);\n    node.range = coll.range;\n    node.tag = tagName;\n    if (tag?.format)\n        node.format = tag.format;\n    return node;\n}\n\nexports.composeCollection = composeCollection;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,kBAAkB,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC5D,MAAM,OAAO,MAAM,IAAI,KAAK,cACtB,gBAAgB,eAAe,CAAC,IAAI,KAAK,OAAO,SAAS,OACzD,MAAM,IAAI,KAAK,cACX,gBAAgB,eAAe,CAAC,IAAI,KAAK,OAAO,SAAS,OACzD,sBAAsB,qBAAqB,CAAC,IAAI,KAAK,OAAO,SAAS;IAC/E,MAAM,OAAO,KAAK,WAAW;IAC7B,kEAAkE;IAClE,8DAA8D;IAC9D,IAAI,YAAY,OAAO,YAAY,KAAK,OAAO,EAAE;QAC7C,KAAK,GAAG,GAAG,KAAK,OAAO;QACvB,OAAO;IACX;IACA,IAAI,SACA,KAAK,GAAG,GAAG;IACf,OAAO;AACX;AACA,SAAS,kBAAkB,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO;IACrD,MAAM,WAAW,MAAM,GAAG;IAC1B,MAAM,UAAU,CAAC,WACX,OACA,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE,CAAA,MAAO,QAAQ,UAAU,sBAAsB;IAC7F,IAAI,MAAM,IAAI,KAAK,aAAa;QAC5B,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAAE,GAAG;QACzC,MAAM,WAAW,UAAU,WACrB,OAAO,MAAM,GAAG,SAAS,MAAM,GAC3B,SACA,WACH,UAAU;QACjB,IAAI,YAAY,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;YAClD,MAAM,UAAU;YAChB,QAAQ,UAAU,gBAAgB;QACtC;IACJ;IACA,MAAM,UAAU,MAAM,IAAI,KAAK,cACzB,QACA,MAAM,IAAI,KAAK,cACX,QACA,MAAM,KAAK,CAAC,MAAM,KAAK,MACnB,QACA;IACd,uDAAuD;IACvD,4CAA4C;IAC5C,IAAI,CAAC,YACD,CAAC,WACD,YAAY,OACX,YAAY,QAAQ,OAAO,CAAC,OAAO,IAAI,YAAY,SACnD,YAAY,QAAQ,OAAO,CAAC,OAAO,IAAI,YAAY,OAAQ;QAC5D,OAAO,kBAAkB,IAAI,KAAK,OAAO,SAAS;IACtD;IACA,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,WAAW,EAAE,UAAU,KAAK;IAC1E,IAAI,CAAC,KAAK;QACN,MAAM,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ;QACxC,IAAI,MAAM,GAAG,UAAU,KAAK,SAAS;YACjC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI;gBAAE,SAAS;YAAM;YAC5D,MAAM;QACV,OACK;YACD,IAAI,IAAI;gBACJ,QAAQ,UAAU,uBAAuB,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE,QAAQ,yBAAyB,EAAE,GAAG,UAAU,IAAI,UAAU,EAAE;YACnI,OACK;gBACD,QAAQ,UAAU,sBAAsB,CAAC,gBAAgB,EAAE,SAAS,EAAE;YAC1E;YACA,OAAO,kBAAkB,IAAI,KAAK,OAAO,SAAS;QACtD;IACJ;IACA,MAAM,OAAO,kBAAkB,IAAI,KAAK,OAAO,SAAS,SAAS;IACjE,MAAM,MAAM,IAAI,OAAO,GAAG,MAAM,CAAA,MAAO,QAAQ,UAAU,sBAAsB,MAAM,IAAI,OAAO,KAAK;IACrG,MAAM,OAAO,SAAS,MAAM,CAAC,OACvB,MACA,IAAI,OAAO,MAAM,CAAC;IACxB,KAAK,KAAK,GAAG,KAAK,KAAK;IACvB,KAAK,GAAG,GAAG;IACX,IAAI,KAAK,QACL,KAAK,MAAM,GAAG,IAAI,MAAM;IAC5B,OAAO;AACX;AAEA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-block-scalar.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\n\nfunction resolveBlockScalar(ctx, scalar, onError) {\n    const start = scalar.offset;\n    const header = parseBlockScalarHeader(scalar, ctx.options.strict, onError);\n    if (!header)\n        return { value: '', type: null, comment: '', range: [start, start, start] };\n    const type = header.mode === '>' ? Scalar.Scalar.BLOCK_FOLDED : Scalar.Scalar.BLOCK_LITERAL;\n    const lines = scalar.source ? splitLines(scalar.source) : [];\n    // determine the end of content & start of chomping\n    let chompStart = lines.length;\n    for (let i = lines.length - 1; i >= 0; --i) {\n        const content = lines[i][1];\n        if (content === '' || content === '\\r')\n            chompStart = i;\n        else\n            break;\n    }\n    // shortcut for empty contents\n    if (chompStart === 0) {\n        const value = header.chomp === '+' && lines.length > 0\n            ? '\\n'.repeat(Math.max(1, lines.length - 1))\n            : '';\n        let end = start + header.length;\n        if (scalar.source)\n            end += scalar.source.length;\n        return { value, type, comment: header.comment, range: [start, end, end] };\n    }\n    // find the indentation level to trim from start\n    let trimIndent = scalar.indent + header.indent;\n    let offset = scalar.offset + header.length;\n    let contentStart = 0;\n    for (let i = 0; i < chompStart; ++i) {\n        const [indent, content] = lines[i];\n        if (content === '' || content === '\\r') {\n            if (header.indent === 0 && indent.length > trimIndent)\n                trimIndent = indent.length;\n        }\n        else {\n            if (indent.length < trimIndent) {\n                const message = 'Block scalars with more-indented leading empty lines must use an explicit indentation indicator';\n                onError(offset + indent.length, 'MISSING_CHAR', message);\n            }\n            if (header.indent === 0)\n                trimIndent = indent.length;\n            contentStart = i;\n            if (trimIndent === 0 && !ctx.atRoot) {\n                const message = 'Block scalar values in collections must be indented';\n                onError(offset, 'BAD_INDENT', message);\n            }\n            break;\n        }\n        offset += indent.length + content.length + 1;\n    }\n    // include trailing more-indented empty lines in content\n    for (let i = lines.length - 1; i >= chompStart; --i) {\n        if (lines[i][0].length > trimIndent)\n            chompStart = i + 1;\n    }\n    let value = '';\n    let sep = '';\n    let prevMoreIndented = false;\n    // leading whitespace is kept intact\n    for (let i = 0; i < contentStart; ++i)\n        value += lines[i][0].slice(trimIndent) + '\\n';\n    for (let i = contentStart; i < chompStart; ++i) {\n        let [indent, content] = lines[i];\n        offset += indent.length + content.length + 1;\n        const crlf = content[content.length - 1] === '\\r';\n        if (crlf)\n            content = content.slice(0, -1);\n        /* istanbul ignore if already caught in lexer */\n        if (content && indent.length < trimIndent) {\n            const src = header.indent\n                ? 'explicit indentation indicator'\n                : 'first line';\n            const message = `Block scalar lines must not be less indented than their ${src}`;\n            onError(offset - content.length - (crlf ? 2 : 1), 'BAD_INDENT', message);\n            indent = '';\n        }\n        if (type === Scalar.Scalar.BLOCK_LITERAL) {\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n        }\n        else if (indent.length > trimIndent || content[0] === '\\t') {\n            // more-indented content within a folded block\n            if (sep === ' ')\n                sep = '\\n';\n            else if (!prevMoreIndented && sep === '\\n')\n                sep = '\\n\\n';\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n            prevMoreIndented = true;\n        }\n        else if (content === '') {\n            // empty line\n            if (sep === '\\n')\n                value += '\\n';\n            else\n                sep = '\\n';\n        }\n        else {\n            value += sep + content;\n            sep = ' ';\n            prevMoreIndented = false;\n        }\n    }\n    switch (header.chomp) {\n        case '-':\n            break;\n        case '+':\n            for (let i = chompStart; i < lines.length; ++i)\n                value += '\\n' + lines[i][0].slice(trimIndent);\n            if (value[value.length - 1] !== '\\n')\n                value += '\\n';\n            break;\n        default:\n            value += '\\n';\n    }\n    const end = start + header.length + scalar.source.length;\n    return { value, type, comment: header.comment, range: [start, end, end] };\n}\nfunction parseBlockScalarHeader({ offset, props }, strict, onError) {\n    /* istanbul ignore if should not happen */\n    if (props[0].type !== 'block-scalar-header') {\n        onError(props[0], 'IMPOSSIBLE', 'Block scalar header not found');\n        return null;\n    }\n    const { source } = props[0];\n    const mode = source[0];\n    let indent = 0;\n    let chomp = '';\n    let error = -1;\n    for (let i = 1; i < source.length; ++i) {\n        const ch = source[i];\n        if (!chomp && (ch === '-' || ch === '+'))\n            chomp = ch;\n        else {\n            const n = Number(ch);\n            if (!indent && n)\n                indent = n;\n            else if (error === -1)\n                error = offset + i;\n        }\n    }\n    if (error !== -1)\n        onError(error, 'UNEXPECTED_TOKEN', `Block scalar header includes extra characters: ${source}`);\n    let hasSpace = false;\n    let comment = '';\n    let length = source.length;\n    for (let i = 1; i < props.length; ++i) {\n        const token = props[i];\n        switch (token.type) {\n            case 'space':\n                hasSpace = true;\n            // fallthrough\n            case 'newline':\n                length += token.source.length;\n                break;\n            case 'comment':\n                if (strict && !hasSpace) {\n                    const message = 'Comments must be separated from other tokens by white space characters';\n                    onError(token, 'MISSING_CHAR', message);\n                }\n                length += token.source.length;\n                comment = token.source.substring(1);\n                break;\n            case 'error':\n                onError(token, 'UNEXPECTED_TOKEN', token.message);\n                length += token.source.length;\n                break;\n            /* istanbul ignore next should not happen */\n            default: {\n                const message = `Unexpected token in block scalar header: ${token.type}`;\n                onError(token, 'UNEXPECTED_TOKEN', message);\n                const ts = token.source;\n                if (ts && typeof ts === 'string')\n                    length += ts.length;\n            }\n        }\n    }\n    return { mode, indent, chomp, comment, length };\n}\n/** @returns Array of lines split up as `[indent, content]` */\nfunction splitLines(source) {\n    const split = source.split(/\\n( *)/);\n    const first = split[0];\n    const m = first.match(/^( *)/);\n    const line0 = m?.[1]\n        ? [m[1], first.slice(m[1].length)]\n        : ['', first];\n    const lines = [line0];\n    for (let i = 1; i < split.length; i += 2)\n        lines.push([split[i], split[i + 1]]);\n    return lines;\n}\n\nexports.resolveBlockScalar = resolveBlockScalar;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,SAAS,mBAAmB,GAAG,EAAE,MAAM,EAAE,OAAO;IAC5C,MAAM,QAAQ,OAAO,MAAM;IAC3B,MAAM,SAAS,uBAAuB,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;IAClE,IAAI,CAAC,QACD,OAAO;QAAE,OAAO;QAAI,MAAM;QAAM,SAAS;QAAI,OAAO;YAAC;YAAO;YAAO;SAAM;IAAC;IAC9E,MAAM,OAAO,OAAO,IAAI,KAAK,MAAM,OAAO,MAAM,CAAC,YAAY,GAAG,OAAO,MAAM,CAAC,aAAa;IAC3F,MAAM,QAAQ,OAAO,MAAM,GAAG,WAAW,OAAO,MAAM,IAAI,EAAE;IAC5D,mDAAmD;IACnD,IAAI,aAAa,MAAM,MAAM;IAC7B,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;QACxC,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE;QAC3B,IAAI,YAAY,MAAM,YAAY,MAC9B,aAAa;aAEb;IACR;IACA,8BAA8B;IAC9B,IAAI,eAAe,GAAG;QAClB,MAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,MAAM,MAAM,GAAG,IAC/C,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,MACvC;QACN,IAAI,MAAM,QAAQ,OAAO,MAAM;QAC/B,IAAI,OAAO,MAAM,EACb,OAAO,OAAO,MAAM,CAAC,MAAM;QAC/B,OAAO;YAAE;YAAO;YAAM,SAAS,OAAO,OAAO;YAAE,OAAO;gBAAC;gBAAO;gBAAK;aAAI;QAAC;IAC5E;IACA,gDAAgD;IAChD,IAAI,aAAa,OAAO,MAAM,GAAG,OAAO,MAAM;IAC9C,IAAI,SAAS,OAAO,MAAM,GAAG,OAAO,MAAM;IAC1C,IAAI,eAAe;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,EAAE,EAAG;QACjC,MAAM,CAAC,QAAQ,QAAQ,GAAG,KAAK,CAAC,EAAE;QAClC,IAAI,YAAY,MAAM,YAAY,MAAM;YACpC,IAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG,YACvC,aAAa,OAAO,MAAM;QAClC,OACK;YACD,IAAI,OAAO,MAAM,GAAG,YAAY;gBAC5B,MAAM,UAAU;gBAChB,QAAQ,SAAS,OAAO,MAAM,EAAE,gBAAgB;YACpD;YACA,IAAI,OAAO,MAAM,KAAK,GAClB,aAAa,OAAO,MAAM;YAC9B,eAAe;YACf,IAAI,eAAe,KAAK,CAAC,IAAI,MAAM,EAAE;gBACjC,MAAM,UAAU;gBAChB,QAAQ,QAAQ,cAAc;YAClC;YACA;QACJ;QACA,UAAU,OAAO,MAAM,GAAG,QAAQ,MAAM,GAAG;IAC/C;IACA,wDAAwD;IACxD,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,YAAY,EAAE,EAAG;QACjD,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,YACrB,aAAa,IAAI;IACzB;IACA,IAAI,QAAQ;IACZ,IAAI,MAAM;IACV,IAAI,mBAAmB;IACvB,oCAAoC;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,EAAE,EAChC,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc;IAC7C,IAAK,IAAI,IAAI,cAAc,IAAI,YAAY,EAAE,EAAG;QAC5C,IAAI,CAAC,QAAQ,QAAQ,GAAG,KAAK,CAAC,EAAE;QAChC,UAAU,OAAO,MAAM,GAAG,QAAQ,MAAM,GAAG;QAC3C,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK;QAC7C,IAAI,MACA,UAAU,QAAQ,KAAK,CAAC,GAAG,CAAC;QAChC,8CAA8C,GAC9C,IAAI,WAAW,OAAO,MAAM,GAAG,YAAY;YACvC,MAAM,MAAM,OAAO,MAAM,GACnB,mCACA;YACN,MAAM,UAAU,CAAC,wDAAwD,EAAE,KAAK;YAChF,QAAQ,SAAS,QAAQ,MAAM,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,cAAc;YAChE,SAAS;QACb;QACA,IAAI,SAAS,OAAO,MAAM,CAAC,aAAa,EAAE;YACtC,SAAS,MAAM,OAAO,KAAK,CAAC,cAAc;YAC1C,MAAM;QACV,OACK,IAAI,OAAO,MAAM,GAAG,cAAc,OAAO,CAAC,EAAE,KAAK,MAAM;YACxD,8CAA8C;YAC9C,IAAI,QAAQ,KACR,MAAM;iBACL,IAAI,CAAC,oBAAoB,QAAQ,MAClC,MAAM;YACV,SAAS,MAAM,OAAO,KAAK,CAAC,cAAc;YAC1C,MAAM;YACN,mBAAmB;QACvB,OACK,IAAI,YAAY,IAAI;YACrB,aAAa;YACb,IAAI,QAAQ,MACR,SAAS;iBAET,MAAM;QACd,OACK;YACD,SAAS,MAAM;YACf,MAAM;YACN,mBAAmB;QACvB;IACJ;IACA,OAAQ,OAAO,KAAK;QAChB,KAAK;YACD;QACJ,KAAK;YACD,IAAK,IAAI,IAAI,YAAY,IAAI,MAAM,MAAM,EAAE,EAAE,EACzC,SAAS,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;YACtC,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,MAC5B,SAAS;YACb;QACJ;YACI,SAAS;IACjB;IACA,MAAM,MAAM,QAAQ,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,MAAM;IACxD,OAAO;QAAE;QAAO;QAAM,SAAS,OAAO,OAAO;QAAE,OAAO;YAAC;YAAO;YAAK;SAAI;IAAC;AAC5E;AACA,SAAS,uBAAuB,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,OAAO;IAC9D,wCAAwC,GACxC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uBAAuB;QACzC,QAAQ,KAAK,CAAC,EAAE,EAAE,cAAc;QAChC,OAAO;IACX;IACA,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;IAC3B,MAAM,OAAO,MAAM,CAAC,EAAE;IACtB,IAAI,SAAS;IACb,IAAI,QAAQ;IACZ,IAAI,QAAQ,CAAC;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACpC,MAAM,KAAK,MAAM,CAAC,EAAE;QACpB,IAAI,CAAC,SAAS,CAAC,OAAO,OAAO,OAAO,GAAG,GACnC,QAAQ;aACP;YACD,MAAM,IAAI,OAAO;YACjB,IAAI,CAAC,UAAU,GACX,SAAS;iBACR,IAAI,UAAU,CAAC,GAChB,QAAQ,SAAS;QACzB;IACJ;IACA,IAAI,UAAU,CAAC,GACX,QAAQ,OAAO,oBAAoB,CAAC,+CAA+C,EAAE,QAAQ;IACjG,IAAI,WAAW;IACf,IAAI,UAAU;IACd,IAAI,SAAS,OAAO,MAAM;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,MAAM,QAAQ,KAAK,CAAC,EAAE;QACtB,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,WAAW;YACf,cAAc;YACd,KAAK;gBACD,UAAU,MAAM,MAAM,CAAC,MAAM;gBAC7B;YACJ,KAAK;gBACD,IAAI,UAAU,CAAC,UAAU;oBACrB,MAAM,UAAU;oBAChB,QAAQ,OAAO,gBAAgB;gBACnC;gBACA,UAAU,MAAM,MAAM,CAAC,MAAM;gBAC7B,UAAU,MAAM,MAAM,CAAC,SAAS,CAAC;gBACjC;YACJ,KAAK;gBACD,QAAQ,OAAO,oBAAoB,MAAM,OAAO;gBAChD,UAAU,MAAM,MAAM,CAAC,MAAM;gBAC7B;YACJ,0CAA0C,GAC1C;gBAAS;oBACL,MAAM,UAAU,CAAC,yCAAyC,EAAE,MAAM,IAAI,EAAE;oBACxE,QAAQ,OAAO,oBAAoB;oBACnC,MAAM,KAAK,MAAM,MAAM;oBACvB,IAAI,MAAM,OAAO,OAAO,UACpB,UAAU,GAAG,MAAM;gBAC3B;QACJ;IACJ;IACA,OAAO;QAAE;QAAM;QAAQ;QAAO;QAAS;IAAO;AAClD;AACA,4DAA4D,GAC5D,SAAS,WAAW,MAAM;IACtB,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,MAAM,QAAQ,KAAK,CAAC,EAAE;IACtB,MAAM,IAAI,MAAM,KAAK,CAAC;IACtB,MAAM,QAAQ,GAAG,CAAC,EAAE,GACd;QAAC,CAAC,CAAC,EAAE;QAAE,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM;KAAE,GAChC;QAAC;QAAI;KAAM;IACjB,MAAM,QAAQ;QAAC;KAAM;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EACnC,MAAM,IAAI,CAAC;QAAC,KAAK,CAAC,EAAE;QAAE,KAAK,CAAC,IAAI,EAAE;KAAC;IACvC,OAAO;AACX;AAEA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/resolve-flow-scalar.js"], "sourcesContent": ["'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\nvar resolveEnd = require('./resolve-end.js');\n\nfunction resolveFlowScalar(scalar, strict, onError) {\n    const { offset, type, source, end } = scalar;\n    let _type;\n    let value;\n    const _onError = (rel, code, msg) => onError(offset + rel, code, msg);\n    switch (type) {\n        case 'scalar':\n            _type = Scalar.Scalar.PLAIN;\n            value = plainValue(source, _onError);\n            break;\n        case 'single-quoted-scalar':\n            _type = Scalar.Scalar.QUOTE_SINGLE;\n            value = singleQuotedValue(source, _onError);\n            break;\n        case 'double-quoted-scalar':\n            _type = Scalar.Scalar.QUOTE_DOUBLE;\n            value = doubleQuotedValue(source, _onError);\n            break;\n        /* istanbul ignore next should not happen */\n        default:\n            onError(scalar, 'UNEXPECTED_TOKEN', `Expected a flow scalar value, but found: ${type}`);\n            return {\n                value: '',\n                type: null,\n                comment: '',\n                range: [offset, offset + source.length, offset + source.length]\n            };\n    }\n    const valueEnd = offset + source.length;\n    const re = resolveEnd.resolveEnd(end, valueEnd, strict, onError);\n    return {\n        value,\n        type: _type,\n        comment: re.comment,\n        range: [offset, valueEnd, re.offset]\n    };\n}\nfunction plainValue(source, onError) {\n    let badChar = '';\n    switch (source[0]) {\n        /* istanbul ignore next should not happen */\n        case '\\t':\n            badChar = 'a tab character';\n            break;\n        case ',':\n            badChar = 'flow indicator character ,';\n            break;\n        case '%':\n            badChar = 'directive indicator character %';\n            break;\n        case '|':\n        case '>': {\n            badChar = `block scalar indicator ${source[0]}`;\n            break;\n        }\n        case '@':\n        case '`': {\n            badChar = `reserved character ${source[0]}`;\n            break;\n        }\n    }\n    if (badChar)\n        onError(0, 'BAD_SCALAR_START', `Plain value cannot start with ${badChar}`);\n    return foldLines(source);\n}\nfunction singleQuotedValue(source, onError) {\n    if (source[source.length - 1] !== \"'\" || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', \"Missing closing 'quote\");\n    return foldLines(source.slice(1, -1)).replace(/''/g, \"'\");\n}\nfunction foldLines(source) {\n    /**\n     * The negative lookbehind here and in the `re` RegExp is to\n     * prevent causing a polynomial search time in certain cases.\n     *\n     * The try-catch is for Safari, which doesn't support this yet:\n     * https://caniuse.com/js-regexp-lookbehind\n     */\n    let first, line;\n    try {\n        first = new RegExp('(.*?)(?<![ \\t])[ \\t]*\\r?\\n', 'sy');\n        line = new RegExp('[ \\t]*(.*?)(?:(?<![ \\t])[ \\t]*)?\\r?\\n', 'sy');\n    }\n    catch {\n        first = /(.*?)[ \\t]*\\r?\\n/sy;\n        line = /[ \\t]*(.*?)[ \\t]*\\r?\\n/sy;\n    }\n    let match = first.exec(source);\n    if (!match)\n        return source;\n    let res = match[1];\n    let sep = ' ';\n    let pos = first.lastIndex;\n    line.lastIndex = pos;\n    while ((match = line.exec(source))) {\n        if (match[1] === '') {\n            if (sep === '\\n')\n                res += sep;\n            else\n                sep = '\\n';\n        }\n        else {\n            res += sep + match[1];\n            sep = ' ';\n        }\n        pos = line.lastIndex;\n    }\n    const last = /[ \\t]*(.*)/sy;\n    last.lastIndex = pos;\n    match = last.exec(source);\n    return res + sep + (match?.[1] ?? '');\n}\nfunction doubleQuotedValue(source, onError) {\n    let res = '';\n    for (let i = 1; i < source.length - 1; ++i) {\n        const ch = source[i];\n        if (ch === '\\r' && source[i + 1] === '\\n')\n            continue;\n        if (ch === '\\n') {\n            const { fold, offset } = foldNewline(source, i);\n            res += fold;\n            i = offset;\n        }\n        else if (ch === '\\\\') {\n            let next = source[++i];\n            const cc = escapeCodes[next];\n            if (cc)\n                res += cc;\n            else if (next === '\\n') {\n                // skip escaped newlines, but still trim the following line\n                next = source[i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === '\\r' && source[i + 1] === '\\n') {\n                // skip escaped CRLF newlines, but still trim the following line\n                next = source[++i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === 'x' || next === 'u' || next === 'U') {\n                const length = { x: 2, u: 4, U: 8 }[next];\n                res += parseCharCode(source, i + 1, length, onError);\n                i += length;\n            }\n            else {\n                const raw = source.substr(i - 1, 2);\n                onError(i - 1, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n                res += raw;\n            }\n        }\n        else if (ch === ' ' || ch === '\\t') {\n            // trim trailing whitespace\n            const wsStart = i;\n            let next = source[i + 1];\n            while (next === ' ' || next === '\\t')\n                next = source[++i + 1];\n            if (next !== '\\n' && !(next === '\\r' && source[i + 2] === '\\n'))\n                res += i > wsStart ? source.slice(wsStart, i + 1) : ch;\n        }\n        else {\n            res += ch;\n        }\n    }\n    if (source[source.length - 1] !== '\"' || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', 'Missing closing \"quote');\n    return res;\n}\n/**\n * Fold a single newline into a space, multiple newlines to N - 1 newlines.\n * Presumes `source[offset] === '\\n'`\n */\nfunction foldNewline(source, offset) {\n    let fold = '';\n    let ch = source[offset + 1];\n    while (ch === ' ' || ch === '\\t' || ch === '\\n' || ch === '\\r') {\n        if (ch === '\\r' && source[offset + 2] !== '\\n')\n            break;\n        if (ch === '\\n')\n            fold += '\\n';\n        offset += 1;\n        ch = source[offset + 1];\n    }\n    if (!fold)\n        fold = ' ';\n    return { fold, offset };\n}\nconst escapeCodes = {\n    '0': '\\0', // null character\n    a: '\\x07', // bell character\n    b: '\\b', // backspace\n    e: '\\x1b', // escape character\n    f: '\\f', // form feed\n    n: '\\n', // line feed\n    r: '\\r', // carriage return\n    t: '\\t', // horizontal tab\n    v: '\\v', // vertical tab\n    N: '\\u0085', // Unicode next line\n    _: '\\u00a0', // Unicode non-breaking space\n    L: '\\u2028', // Unicode line separator\n    P: '\\u2029', // Unicode paragraph separator\n    ' ': ' ',\n    '\"': '\"',\n    '/': '/',\n    '\\\\': '\\\\',\n    '\\t': '\\t'\n};\nfunction parseCharCode(source, offset, length, onError) {\n    const cc = source.substr(offset, length);\n    const ok = cc.length === length && /^[0-9a-fA-F]+$/.test(cc);\n    const code = ok ? parseInt(cc, 16) : NaN;\n    if (isNaN(code)) {\n        const raw = source.substr(offset - 2, length + 2);\n        onError(offset - 2, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n        return raw;\n    }\n    return String.fromCodePoint(code);\n}\n\nexports.resolveFlowScalar = resolveFlowScalar;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,OAAO;IAC9C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG;IACtC,IAAI;IACJ,IAAI;IACJ,MAAM,WAAW,CAAC,KAAK,MAAM,MAAQ,QAAQ,SAAS,KAAK,MAAM;IACjE,OAAQ;QACJ,KAAK;YACD,QAAQ,OAAO,MAAM,CAAC,KAAK;YAC3B,QAAQ,WAAW,QAAQ;YAC3B;QACJ,KAAK;YACD,QAAQ,OAAO,MAAM,CAAC,YAAY;YAClC,QAAQ,kBAAkB,QAAQ;YAClC;QACJ,KAAK;YACD,QAAQ,OAAO,MAAM,CAAC,YAAY;YAClC,QAAQ,kBAAkB,QAAQ;YAClC;QACJ,0CAA0C,GAC1C;YACI,QAAQ,QAAQ,oBAAoB,CAAC,yCAAyC,EAAE,MAAM;YACtF,OAAO;gBACH,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO;oBAAC;oBAAQ,SAAS,OAAO,MAAM;oBAAE,SAAS,OAAO,MAAM;iBAAC;YACnE;IACR;IACA,MAAM,WAAW,SAAS,OAAO,MAAM;IACvC,MAAM,KAAK,WAAW,UAAU,CAAC,KAAK,UAAU,QAAQ;IACxD,OAAO;QACH;QACA,MAAM;QACN,SAAS,GAAG,OAAO;QACnB,OAAO;YAAC;YAAQ;YAAU,GAAG,MAAM;SAAC;IACxC;AACJ;AACA,SAAS,WAAW,MAAM,EAAE,OAAO;IAC/B,IAAI,UAAU;IACd,OAAQ,MAAM,CAAC,EAAE;QACb,0CAA0C,GAC1C,KAAK;YACD,UAAU;YACV;QACJ,KAAK;YACD,UAAU;YACV;QACJ,KAAK;YACD,UAAU;YACV;QACJ,KAAK;QACL,KAAK;YAAK;gBACN,UAAU,CAAC,uBAAuB,EAAE,MAAM,CAAC,EAAE,EAAE;gBAC/C;YACJ;QACA,KAAK;QACL,KAAK;YAAK;gBACN,UAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,EAAE,EAAE;gBAC3C;YACJ;IACJ;IACA,IAAI,SACA,QAAQ,GAAG,oBAAoB,CAAC,8BAA8B,EAAE,SAAS;IAC7E,OAAO,UAAU;AACrB;AACA,SAAS,kBAAkB,MAAM,EAAE,OAAO;IACtC,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,OAAO,OAAO,MAAM,KAAK,GACvD,QAAQ,OAAO,MAAM,EAAE,gBAAgB;IAC3C,OAAO,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,OAAO;AACzD;AACA,SAAS,UAAU,MAAM;IACrB;;;;;;KAMC,GACD,IAAI,OAAO;IACX,IAAI;QACA,QAAQ,IAAI,OAAO,8BAA8B;QACjD,OAAO,IAAI,OAAO,yCAAyC;IAC/D,EACA,OAAM;QACF,QAAQ;QACR,OAAO;IACX;IACA,IAAI,QAAQ,MAAM,IAAI,CAAC;IACvB,IAAI,CAAC,OACD,OAAO;IACX,IAAI,MAAM,KAAK,CAAC,EAAE;IAClB,IAAI,MAAM;IACV,IAAI,MAAM,MAAM,SAAS;IACzB,KAAK,SAAS,GAAG;IACjB,MAAQ,QAAQ,KAAK,IAAI,CAAC,QAAU;QAChC,IAAI,KAAK,CAAC,EAAE,KAAK,IAAI;YACjB,IAAI,QAAQ,MACR,OAAO;iBAEP,MAAM;QACd,OACK;YACD,OAAO,MAAM,KAAK,CAAC,EAAE;YACrB,MAAM;QACV;QACA,MAAM,KAAK,SAAS;IACxB;IACA,MAAM,OAAO;IACb,KAAK,SAAS,GAAG;IACjB,QAAQ,KAAK,IAAI,CAAC;IAClB,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE;AACxC;AACA,SAAS,kBAAkB,MAAM,EAAE,OAAO;IACtC,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,EAAG;QACxC,MAAM,KAAK,MAAM,CAAC,EAAE;QACpB,IAAI,OAAO,QAAQ,MAAM,CAAC,IAAI,EAAE,KAAK,MACjC;QACJ,IAAI,OAAO,MAAM;YACb,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,YAAY,QAAQ;YAC7C,OAAO;YACP,IAAI;QACR,OACK,IAAI,OAAO,MAAM;YAClB,IAAI,OAAO,MAAM,CAAC,EAAE,EAAE;YACtB,MAAM,KAAK,WAAW,CAAC,KAAK;YAC5B,IAAI,IACA,OAAO;iBACN,IAAI,SAAS,MAAM;gBACpB,2DAA2D;gBAC3D,OAAO,MAAM,CAAC,IAAI,EAAE;gBACpB,MAAO,SAAS,OAAO,SAAS,KAC5B,OAAO,MAAM,CAAC,EAAE,IAAI,EAAE;YAC9B,OACK,IAAI,SAAS,QAAQ,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM;gBAC9C,gEAAgE;gBAChE,OAAO,MAAM,CAAC,EAAE,IAAI,EAAE;gBACtB,MAAO,SAAS,OAAO,SAAS,KAC5B,OAAO,MAAM,CAAC,EAAE,IAAI,EAAE;YAC9B,OACK,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;gBACnD,MAAM,SAAS;oBAAE,GAAG;oBAAG,GAAG;oBAAG,GAAG;gBAAE,CAAC,CAAC,KAAK;gBACzC,OAAO,cAAc,QAAQ,IAAI,GAAG,QAAQ;gBAC5C,KAAK;YACT,OACK;gBACD,MAAM,MAAM,OAAO,MAAM,CAAC,IAAI,GAAG;gBACjC,QAAQ,IAAI,GAAG,iBAAiB,CAAC,wBAAwB,EAAE,KAAK;gBAChE,OAAO;YACX;QACJ,OACK,IAAI,OAAO,OAAO,OAAO,MAAM;YAChC,2BAA2B;YAC3B,MAAM,UAAU;YAChB,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;YACxB,MAAO,SAAS,OAAO,SAAS,KAC5B,OAAO,MAAM,CAAC,EAAE,IAAI,EAAE;YAC1B,IAAI,SAAS,QAAQ,CAAC,CAAC,SAAS,QAAQ,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,GAC1D,OAAO,IAAI,UAAU,OAAO,KAAK,CAAC,SAAS,IAAI,KAAK;QAC5D,OACK;YACD,OAAO;QACX;IACJ;IACA,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,OAAO,OAAO,MAAM,KAAK,GACvD,QAAQ,OAAO,MAAM,EAAE,gBAAgB;IAC3C,OAAO;AACX;AACA;;;CAGC,GACD,SAAS,YAAY,MAAM,EAAE,MAAM;IAC/B,IAAI,OAAO;IACX,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE;IAC3B,MAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAM;QAC5D,IAAI,OAAO,QAAQ,MAAM,CAAC,SAAS,EAAE,KAAK,MACtC;QACJ,IAAI,OAAO,MACP,QAAQ;QACZ,UAAU;QACV,KAAK,MAAM,CAAC,SAAS,EAAE;IAC3B;IACA,IAAI,CAAC,MACD,OAAO;IACX,OAAO;QAAE;QAAM;IAAO;AAC1B;AACA,MAAM,cAAc;IAChB,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;AACV;AACA,SAAS,cAAc,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;IAClD,MAAM,KAAK,OAAO,MAAM,CAAC,QAAQ;IACjC,MAAM,KAAK,GAAG,MAAM,KAAK,UAAU,iBAAiB,IAAI,CAAC;IACzD,MAAM,OAAO,KAAK,SAAS,IAAI,MAAM;IACrC,IAAI,MAAM,OAAO;QACb,MAAM,MAAM,OAAO,MAAM,CAAC,SAAS,GAAG,SAAS;QAC/C,QAAQ,SAAS,GAAG,iBAAiB,CAAC,wBAAwB,EAAE,KAAK;QACrE,OAAO;IACX;IACA,OAAO,OAAO,aAAa,CAAC;AAChC;AAEA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/compose-scalar.js"], "sourcesContent": ["'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar resolveBlockScalar = require('./resolve-block-scalar.js');\nvar resolveFlowScalar = require('./resolve-flow-scalar.js');\n\nfunction composeScalar(ctx, token, tagToken, onError) {\n    const { value, type, comment, range } = token.type === 'block-scalar'\n        ? resolveBlockScalar.resolveBlockScalar(ctx, token, onError)\n        : resolveFlowScalar.resolveFlowScalar(token, ctx.options.strict, onError);\n    const tagName = tagToken\n        ? ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg))\n        : null;\n    let tag;\n    if (ctx.options.stringKeys && ctx.atKey) {\n        tag = ctx.schema[identity.SCALAR];\n    }\n    else if (tagName)\n        tag = findScalarTagByName(ctx.schema, value, tagName, tagToken, onError);\n    else if (token.type === 'scalar')\n        tag = findScalarTagByTest(ctx, value, token, onError);\n    else\n        tag = ctx.schema[identity.SCALAR];\n    let scalar;\n    try {\n        const res = tag.resolve(value, msg => onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg), ctx.options);\n        scalar = identity.isScalar(res) ? res : new Scalar.Scalar(res);\n    }\n    catch (error) {\n        const msg = error instanceof Error ? error.message : String(error);\n        onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg);\n        scalar = new Scalar.Scalar(value);\n    }\n    scalar.range = range;\n    scalar.source = value;\n    if (type)\n        scalar.type = type;\n    if (tagName)\n        scalar.tag = tagName;\n    if (tag.format)\n        scalar.format = tag.format;\n    if (comment)\n        scalar.comment = comment;\n    return scalar;\n}\nfunction findScalarTagByName(schema, value, tagName, tagToken, onError) {\n    if (tagName === '!')\n        return schema[identity.SCALAR]; // non-specific tag\n    const matchWithTest = [];\n    for (const tag of schema.tags) {\n        if (!tag.collection && tag.tag === tagName) {\n            if (tag.default && tag.test)\n                matchWithTest.push(tag);\n            else\n                return tag;\n        }\n    }\n    for (const tag of matchWithTest)\n        if (tag.test?.test(value))\n            return tag;\n    const kt = schema.knownTags[tagName];\n    if (kt && !kt.collection) {\n        // Ensure that the known tag is available for stringifying,\n        // but does not get used by default.\n        schema.tags.push(Object.assign({}, kt, { default: false, test: undefined }));\n        return kt;\n    }\n    onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, tagName !== 'tag:yaml.org,2002:str');\n    return schema[identity.SCALAR];\n}\nfunction findScalarTagByTest({ atKey, directives, schema }, value, token, onError) {\n    const tag = schema.tags.find(tag => (tag.default === true || (atKey && tag.default === 'key')) &&\n        tag.test?.test(value)) || schema[identity.SCALAR];\n    if (schema.compat) {\n        const compat = schema.compat.find(tag => tag.default && tag.test?.test(value)) ??\n            schema[identity.SCALAR];\n        if (tag.tag !== compat.tag) {\n            const ts = directives.tagString(tag.tag);\n            const cs = directives.tagString(compat.tag);\n            const msg = `Value may be parsed as either ${ts} or ${cs}`;\n            onError(token, 'TAG_RESOLVE_FAILED', msg, true);\n        }\n    }\n    return tag;\n}\n\nexports.composeScalar = composeScalar;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,cAAc,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;IAChD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,KAAK,iBACjD,mBAAmB,kBAAkB,CAAC,KAAK,OAAO,WAClD,kBAAkB,iBAAiB,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;IACrE,MAAM,UAAU,WACV,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE,CAAA,MAAO,QAAQ,UAAU,sBAAsB,QACvF;IACN,IAAI;IACJ,IAAI,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,KAAK,EAAE;QACrC,MAAM,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC;IACrC,OACK,IAAI,SACL,MAAM,oBAAoB,IAAI,MAAM,EAAE,OAAO,SAAS,UAAU;SAC/D,IAAI,MAAM,IAAI,KAAK,UACpB,MAAM,oBAAoB,KAAK,OAAO,OAAO;SAE7C,MAAM,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC;IACrC,IAAI;IACJ,IAAI;QACA,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAA,MAAO,QAAQ,YAAY,OAAO,sBAAsB,MAAM,IAAI,OAAO;QACxG,SAAS,SAAS,QAAQ,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;IAC9D,EACA,OAAO,OAAO;QACV,MAAM,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAC5D,QAAQ,YAAY,OAAO,sBAAsB;QACjD,SAAS,IAAI,OAAO,MAAM,CAAC;IAC/B;IACA,OAAO,KAAK,GAAG;IACf,OAAO,MAAM,GAAG;IAChB,IAAI,MACA,OAAO,IAAI,GAAG;IAClB,IAAI,SACA,OAAO,GAAG,GAAG;IACjB,IAAI,IAAI,MAAM,EACV,OAAO,MAAM,GAAG,IAAI,MAAM;IAC9B,IAAI,SACA,OAAO,OAAO,GAAG;IACrB,OAAO;AACX;AACA,SAAS,oBAAoB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;IAClE,IAAI,YAAY,KACZ,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,EAAE,mBAAmB;IACvD,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,OAAO,OAAO,IAAI,CAAE;QAC3B,IAAI,CAAC,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,SAAS;YACxC,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,EACvB,cAAc,IAAI,CAAC;iBAEnB,OAAO;QACf;IACJ;IACA,KAAK,MAAM,OAAO,cACd,IAAI,IAAI,IAAI,EAAE,KAAK,QACf,OAAO;IACf,MAAM,KAAK,OAAO,SAAS,CAAC,QAAQ;IACpC,IAAI,MAAM,CAAC,GAAG,UAAU,EAAE;QACtB,2DAA2D;QAC3D,oCAAoC;QACpC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI;YAAE,SAAS;YAAO,MAAM;QAAU;QACzE,OAAO;IACX;IACA,QAAQ,UAAU,sBAAsB,CAAC,gBAAgB,EAAE,SAAS,EAAE,YAAY;IAClF,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC;AAClC;AACA,SAAS,oBAAoB,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO;IAC7E,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI,OAAO,KAAK,QAAS,SAAS,IAAI,OAAO,KAAK,KAAM,KACzF,IAAI,IAAI,EAAE,KAAK,WAAW,MAAM,CAAC,SAAS,MAAM,CAAC;IACrD,IAAI,OAAO,MAAM,EAAE;QACf,MAAM,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,KAAK,WACnE,MAAM,CAAC,SAAS,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,KAAK,OAAO,GAAG,EAAE;YACxB,MAAM,KAAK,WAAW,SAAS,CAAC,IAAI,GAAG;YACvC,MAAM,KAAK,WAAW,SAAS,CAAC,OAAO,GAAG;YAC1C,MAAM,MAAM,CAAC,8BAA8B,EAAE,GAAG,IAAI,EAAE,IAAI;YAC1D,QAAQ,OAAO,sBAAsB,KAAK;QAC9C;IACJ;IACA,OAAO;AACX;AAEA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/util-empty-scalar-position.js"], "sourcesContent": ["'use strict';\n\nfunction emptyScalarPosition(offset, before, pos) {\n    if (before) {\n        pos ?? (pos = before.length);\n        for (let i = pos - 1; i >= 0; --i) {\n            let st = before[i];\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                case 'newline':\n                    offset -= st.source.length;\n                    continue;\n            }\n            // Technically, an empty scalar is immediately after the last non-empty\n            // node, but it's more useful to place it after any whitespace.\n            st = before[++i];\n            while (st?.type === 'space') {\n                offset += st.source.length;\n                st = before[++i];\n            }\n            break;\n        }\n    }\n    return offset;\n}\n\nexports.emptyScalarPosition = emptyScalarPosition;\n"], "names": [], "mappings": "AAEA,SAAS,oBAAoB,MAAM,EAAE,MAAM,EAAE,GAAG;IAC5C,IAAI,QAAQ;QACR,OAAO,CAAC,MAAM,OAAO,MAAM;QAC3B,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,EAAE,EAAG;YAC/B,IAAI,KAAK,MAAM,CAAC,EAAE;YAClB,OAAQ,GAAG,IAAI;gBACX,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,UAAU,GAAG,MAAM,CAAC,MAAM;oBAC1B;YACR;YACA,uEAAuE;YACvE,+DAA+D;YAC/D,KAAK,MAAM,CAAC,EAAE,EAAE;YAChB,MAAO,IAAI,SAAS,QAAS;gBACzB,UAAU,GAAG,MAAM,CAAC,MAAM;gBAC1B,KAAK,MAAM,CAAC,EAAE,EAAE;YACpB;YACA;QACJ;IACJ;IACA,OAAO;AACX;AAEA,QAAQ,mBAAmB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/compose-node.js"], "sourcesContent": ["'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar identity = require('../nodes/identity.js');\nvar composeCollection = require('./compose-collection.js');\nvar composeScalar = require('./compose-scalar.js');\nvar resolveEnd = require('./resolve-end.js');\nvar utilEmptyScalarPosition = require('./util-empty-scalar-position.js');\n\nconst CN = { composeNode, composeEmptyNode };\nfunction composeNode(ctx, token, props, onError) {\n    const atKey = ctx.atKey;\n    const { spaceBefore, comment, anchor, tag } = props;\n    let node;\n    let isSrcToken = true;\n    switch (token.type) {\n        case 'alias':\n            node = composeAlias(ctx, token, onError);\n            if (anchor || tag)\n                onError(token, 'ALIAS_PROPS', 'An alias node must not specify any properties');\n            break;\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'block-scalar':\n            node = composeScalar.composeScalar(ctx, token, tag, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        case 'block-map':\n        case 'block-seq':\n        case 'flow-collection':\n            node = composeCollection.composeCollection(CN, ctx, token, props, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        default: {\n            const message = token.type === 'error'\n                ? token.message\n                : `Unsupported token (type: ${token.type})`;\n            onError(token, 'UNEXPECTED_TOKEN', message);\n            node = composeEmptyNode(ctx, token.offset, undefined, null, props, onError);\n            isSrcToken = false;\n        }\n    }\n    if (anchor && node.anchor === '')\n        onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    if (atKey &&\n        ctx.options.stringKeys &&\n        (!identity.isScalar(node) ||\n            typeof node.value !== 'string' ||\n            (node.tag && node.tag !== 'tag:yaml.org,2002:str'))) {\n        const msg = 'With stringKeys, all keys must be strings';\n        onError(tag ?? token, 'NON_STRING_KEY', msg);\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        if (token.type === 'scalar' && token.source === '')\n            node.comment = comment;\n        else\n            node.commentBefore = comment;\n    }\n    // @ts-expect-error Type checking misses meaning of isSrcToken\n    if (ctx.options.keepSourceTokens && isSrcToken)\n        node.srcToken = token;\n    return node;\n}\nfunction composeEmptyNode(ctx, offset, before, pos, { spaceBefore, comment, anchor, tag, end }, onError) {\n    const token = {\n        type: 'scalar',\n        offset: utilEmptyScalarPosition.emptyScalarPosition(offset, before, pos),\n        indent: -1,\n        source: ''\n    };\n    const node = composeScalar.composeScalar(ctx, token, tag, onError);\n    if (anchor) {\n        node.anchor = anchor.source.substring(1);\n        if (node.anchor === '')\n            onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        node.comment = comment;\n        node.range[2] = end;\n    }\n    return node;\n}\nfunction composeAlias({ options }, { offset, source, end }, onError) {\n    const alias = new Alias.Alias(source.substring(1));\n    if (alias.source === '')\n        onError(offset, 'BAD_ALIAS', 'Alias cannot be an empty string');\n    if (alias.source.endsWith(':'))\n        onError(offset + source.length - 1, 'BAD_ALIAS', 'Alias ending in : is ambiguous', true);\n    const valueEnd = offset + source.length;\n    const re = resolveEnd.resolveEnd(end, valueEnd, options.strict, onError);\n    alias.range = [offset, valueEnd, re.offset];\n    if (re.comment)\n        alias.comment = re.comment;\n    return alias;\n}\n\nexports.composeEmptyNode = composeEmptyNode;\nexports.composeNode = composeNode;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,KAAK;IAAE;IAAa;AAAiB;AAC3C,SAAS,YAAY,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO;IAC3C,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG;IAC9C,IAAI;IACJ,IAAI,aAAa;IACjB,OAAQ,MAAM,IAAI;QACd,KAAK;YACD,OAAO,aAAa,KAAK,OAAO;YAChC,IAAI,UAAU,KACV,QAAQ,OAAO,eAAe;YAClC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,cAAc,aAAa,CAAC,KAAK,OAAO,KAAK;YACpD,IAAI,QACA,KAAK,MAAM,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC;YAC1C;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,kBAAkB,iBAAiB,CAAC,IAAI,KAAK,OAAO,OAAO;YAClE,IAAI,QACA,KAAK,MAAM,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC;YAC1C;QACJ;YAAS;gBACL,MAAM,UAAU,MAAM,IAAI,KAAK,UACzB,MAAM,OAAO,GACb,CAAC,yBAAyB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;gBAC/C,QAAQ,OAAO,oBAAoB;gBACnC,OAAO,iBAAiB,KAAK,MAAM,MAAM,EAAE,WAAW,MAAM,OAAO;gBACnE,aAAa;YACjB;IACJ;IACA,IAAI,UAAU,KAAK,MAAM,KAAK,IAC1B,QAAQ,QAAQ,aAAa;IACjC,IAAI,SACA,IAAI,OAAO,CAAC,UAAU,IACtB,CAAC,CAAC,SAAS,QAAQ,CAAC,SAChB,OAAO,KAAK,KAAK,KAAK,YACrB,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,uBAAwB,GAAG;QACzD,MAAM,MAAM;QACZ,QAAQ,OAAO,OAAO,kBAAkB;IAC5C;IACA,IAAI,aACA,KAAK,WAAW,GAAG;IACvB,IAAI,SAAS;QACT,IAAI,MAAM,IAAI,KAAK,YAAY,MAAM,MAAM,KAAK,IAC5C,KAAK,OAAO,GAAG;aAEf,KAAK,aAAa,GAAG;IAC7B;IACA,8DAA8D;IAC9D,IAAI,IAAI,OAAO,CAAC,gBAAgB,IAAI,YAChC,KAAK,QAAQ,GAAG;IACpB,OAAO;AACX;AACA,SAAS,iBAAiB,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,OAAO;IACnG,MAAM,QAAQ;QACV,MAAM;QACN,QAAQ,wBAAwB,mBAAmB,CAAC,QAAQ,QAAQ;QACpE,QAAQ,CAAC;QACT,QAAQ;IACZ;IACA,MAAM,OAAO,cAAc,aAAa,CAAC,KAAK,OAAO,KAAK;IAC1D,IAAI,QAAQ;QACR,KAAK,MAAM,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC;QACtC,IAAI,KAAK,MAAM,KAAK,IAChB,QAAQ,QAAQ,aAAa;IACrC;IACA,IAAI,aACA,KAAK,WAAW,GAAG;IACvB,IAAI,SAAS;QACT,KAAK,OAAO,GAAG;QACf,KAAK,KAAK,CAAC,EAAE,GAAG;IACpB;IACA,OAAO;AACX;AACA,SAAS,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,OAAO;IAC/D,MAAM,QAAQ,IAAI,MAAM,KAAK,CAAC,OAAO,SAAS,CAAC;IAC/C,IAAI,MAAM,MAAM,KAAK,IACjB,QAAQ,QAAQ,aAAa;IACjC,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MACtB,QAAQ,SAAS,OAAO,MAAM,GAAG,GAAG,aAAa,kCAAkC;IACvF,MAAM,WAAW,SAAS,OAAO,MAAM;IACvC,MAAM,KAAK,WAAW,UAAU,CAAC,KAAK,UAAU,QAAQ,MAAM,EAAE;IAChE,MAAM,KAAK,GAAG;QAAC;QAAQ;QAAU,GAAG,MAAM;KAAC;IAC3C,IAAI,GAAG,OAAO,EACV,MAAM,OAAO,GAAG,GAAG,OAAO;IAC9B,OAAO;AACX;AAEA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/compose-doc.js"], "sourcesContent": ["'use strict';\n\nvar Document = require('../doc/Document.js');\nvar composeNode = require('./compose-node.js');\nvar resolveEnd = require('./resolve-end.js');\nvar resolveProps = require('./resolve-props.js');\n\nfunction composeDoc(options, directives, { offset, start, value, end }, onError) {\n    const opts = Object.assign({ _directives: directives }, options);\n    const doc = new Document.Document(undefined, opts);\n    const ctx = {\n        atKey: false,\n        atRoot: true,\n        directives: doc.directives,\n        options: doc.options,\n        schema: doc.schema\n    };\n    const props = resolveProps.resolveProps(start, {\n        indicator: 'doc-start',\n        next: value ?? end?.[0],\n        offset,\n        onError,\n        parentIndent: 0,\n        startOnNewline: true\n    });\n    if (props.found) {\n        doc.directives.docStart = true;\n        if (value &&\n            (value.type === 'block-map' || value.type === 'block-seq') &&\n            !props.hasNewline)\n            onError(props.end, 'MISSING_CHAR', 'Block collection cannot start on same line with directives-end marker');\n    }\n    // @ts-expect-error If Contents is set, let's trust the user\n    doc.contents = value\n        ? composeNode.composeNode(ctx, value, props, onError)\n        : composeNode.composeEmptyNode(ctx, props.end, start, null, props, onError);\n    const contentEnd = doc.contents.range[2];\n    const re = resolveEnd.resolveEnd(end, contentEnd, false, onError);\n    if (re.comment)\n        doc.comment = re.comment;\n    doc.range = [offset, contentEnd, re.offset];\n    return doc;\n}\n\nexports.composeDoc = composeDoc;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,WAAW,OAAO,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,OAAO;IAC3E,MAAM,OAAO,OAAO,MAAM,CAAC;QAAE,aAAa;IAAW,GAAG;IACxD,MAAM,MAAM,IAAI,SAAS,QAAQ,CAAC,WAAW;IAC7C,MAAM,MAAM;QACR,OAAO;QACP,QAAQ;QACR,YAAY,IAAI,UAAU;QAC1B,SAAS,IAAI,OAAO;QACpB,QAAQ,IAAI,MAAM;IACtB;IACA,MAAM,QAAQ,aAAa,YAAY,CAAC,OAAO;QAC3C,WAAW;QACX,MAAM,SAAS,KAAK,CAAC,EAAE;QACvB;QACA;QACA,cAAc;QACd,gBAAgB;IACpB;IACA,IAAI,MAAM,KAAK,EAAE;QACb,IAAI,UAAU,CAAC,QAAQ,GAAG;QAC1B,IAAI,SACA,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,WAAW,KACzD,CAAC,MAAM,UAAU,EACjB,QAAQ,MAAM,GAAG,EAAE,gBAAgB;IAC3C;IACA,4DAA4D;IAC5D,IAAI,QAAQ,GAAG,QACT,YAAY,WAAW,CAAC,KAAK,OAAO,OAAO,WAC3C,YAAY,gBAAgB,CAAC,KAAK,MAAM,GAAG,EAAE,OAAO,MAAM,OAAO;IACvE,MAAM,aAAa,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;IACxC,MAAM,KAAK,WAAW,UAAU,CAAC,KAAK,YAAY,OAAO;IACzD,IAAI,GAAG,OAAO,EACV,IAAI,OAAO,GAAG,GAAG,OAAO;IAC5B,IAAI,KAAK,GAAG;QAAC;QAAQ;QAAY,GAAG,MAAM;KAAC;IAC3C,OAAO;AACX;AAEA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/compose/composer.js"], "sourcesContent": ["'use strict';\n\nvar node_process = require('process');\nvar directives = require('../doc/directives.js');\nvar Document = require('../doc/Document.js');\nvar errors = require('../errors.js');\nvar identity = require('../nodes/identity.js');\nvar composeDoc = require('./compose-doc.js');\nvar resolveEnd = require('./resolve-end.js');\n\nfunction getErrorPos(src) {\n    if (typeof src === 'number')\n        return [src, src + 1];\n    if (Array.isArray(src))\n        return src.length === 2 ? src : [src[0], src[1]];\n    const { offset, source } = src;\n    return [offset, offset + (typeof source === 'string' ? source.length : 1)];\n}\nfunction parsePrelude(prelude) {\n    let comment = '';\n    let atComment = false;\n    let afterEmptyLine = false;\n    for (let i = 0; i < prelude.length; ++i) {\n        const source = prelude[i];\n        switch (source[0]) {\n            case '#':\n                comment +=\n                    (comment === '' ? '' : afterEmptyLine ? '\\n\\n' : '\\n') +\n                        (source.substring(1) || ' ');\n                atComment = true;\n                afterEmptyLine = false;\n                break;\n            case '%':\n                if (prelude[i + 1]?.[0] !== '#')\n                    i += 1;\n                atComment = false;\n                break;\n            default:\n                // This may be wrong after doc-end, but in that case it doesn't matter\n                if (!atComment)\n                    afterEmptyLine = true;\n                atComment = false;\n        }\n    }\n    return { comment, afterEmptyLine };\n}\n/**\n * Compose a stream of CST nodes into a stream of YAML Documents.\n *\n * ```ts\n * import { Composer, Parser } from 'yaml'\n *\n * const src: string = ...\n * const tokens = new Parser().parse(src)\n * const docs = new Composer().compose(tokens)\n * ```\n */\nclass Composer {\n    constructor(options = {}) {\n        this.doc = null;\n        this.atDirectives = false;\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n        this.onError = (source, code, message, warning) => {\n            const pos = getErrorPos(source);\n            if (warning)\n                this.warnings.push(new errors.YAMLWarning(pos, code, message));\n            else\n                this.errors.push(new errors.YAMLParseError(pos, code, message));\n        };\n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        this.directives = new directives.Directives({ version: options.version || '1.2' });\n        this.options = options;\n    }\n    decorate(doc, afterDoc) {\n        const { comment, afterEmptyLine } = parsePrelude(this.prelude);\n        //console.log({ dc: doc.comment, prelude, comment })\n        if (comment) {\n            const dc = doc.contents;\n            if (afterDoc) {\n                doc.comment = doc.comment ? `${doc.comment}\\n${comment}` : comment;\n            }\n            else if (afterEmptyLine || doc.directives.docStart || !dc) {\n                doc.commentBefore = comment;\n            }\n            else if (identity.isCollection(dc) && !dc.flow && dc.items.length > 0) {\n                let it = dc.items[0];\n                if (identity.isPair(it))\n                    it = it.key;\n                const cb = it.commentBefore;\n                it.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n            else {\n                const cb = dc.commentBefore;\n                dc.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n        }\n        if (afterDoc) {\n            Array.prototype.push.apply(doc.errors, this.errors);\n            Array.prototype.push.apply(doc.warnings, this.warnings);\n        }\n        else {\n            doc.errors = this.errors;\n            doc.warnings = this.warnings;\n        }\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n    }\n    /**\n     * Current stream status information.\n     *\n     * Mostly useful at the end of input for an empty stream.\n     */\n    streamInfo() {\n        return {\n            comment: parsePrelude(this.prelude).comment,\n            directives: this.directives,\n            errors: this.errors,\n            warnings: this.warnings\n        };\n    }\n    /**\n     * Compose tokens into documents.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *compose(tokens, forceDoc = false, endOffset = -1) {\n        for (const token of tokens)\n            yield* this.next(token);\n        yield* this.end(forceDoc, endOffset);\n    }\n    /** Advance the composer by one CST token. */\n    *next(token) {\n        if (node_process.env.LOG_STREAM)\n            console.dir(token, { depth: null });\n        switch (token.type) {\n            case 'directive':\n                this.directives.add(token.source, (offset, message, warning) => {\n                    const pos = getErrorPos(token);\n                    pos[0] += offset;\n                    this.onError(pos, 'BAD_DIRECTIVE', message, warning);\n                });\n                this.prelude.push(token.source);\n                this.atDirectives = true;\n                break;\n            case 'document': {\n                const doc = composeDoc.composeDoc(this.options, this.directives, token, this.onError);\n                if (this.atDirectives && !doc.directives.docStart)\n                    this.onError(token, 'MISSING_CHAR', 'Missing directives-end/doc-start indicator line');\n                this.decorate(doc, false);\n                if (this.doc)\n                    yield this.doc;\n                this.doc = doc;\n                this.atDirectives = false;\n                break;\n            }\n            case 'byte-order-mark':\n            case 'space':\n                break;\n            case 'comment':\n            case 'newline':\n                this.prelude.push(token.source);\n                break;\n            case 'error': {\n                const msg = token.source\n                    ? `${token.message}: ${JSON.stringify(token.source)}`\n                    : token.message;\n                const error = new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg);\n                if (this.atDirectives || !this.doc)\n                    this.errors.push(error);\n                else\n                    this.doc.errors.push(error);\n                break;\n            }\n            case 'doc-end': {\n                if (!this.doc) {\n                    const msg = 'Unexpected doc-end without preceding document';\n                    this.errors.push(new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg));\n                    break;\n                }\n                this.doc.directives.docEnd = true;\n                const end = resolveEnd.resolveEnd(token.end, token.offset + token.source.length, this.doc.options.strict, this.onError);\n                this.decorate(this.doc, true);\n                if (end.comment) {\n                    const dc = this.doc.comment;\n                    this.doc.comment = dc ? `${dc}\\n${end.comment}` : end.comment;\n                }\n                this.doc.range[2] = end.offset;\n                break;\n            }\n            default:\n                this.errors.push(new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', `Unsupported token ${token.type}`));\n        }\n    }\n    /**\n     * Call at end of input to yield any remaining document.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *end(forceDoc = false, endOffset = -1) {\n        if (this.doc) {\n            this.decorate(this.doc, true);\n            yield this.doc;\n            this.doc = null;\n        }\n        else if (forceDoc) {\n            const opts = Object.assign({ _directives: this.directives }, this.options);\n            const doc = new Document.Document(undefined, opts);\n            if (this.atDirectives)\n                this.onError(endOffset, 'MISSING_CHAR', 'Missing directives-end indicator line');\n            doc.range = [0, endOffset, endOffset];\n            this.decorate(doc, false);\n            yield doc;\n        }\n    }\n}\n\nexports.Composer = Composer;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,YAAY,GAAG;IACpB,IAAI,OAAO,QAAQ,UACf,OAAO;QAAC;QAAK,MAAM;KAAE;IACzB,IAAI,MAAM,OAAO,CAAC,MACd,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM;QAAC,GAAG,CAAC,EAAE;QAAE,GAAG,CAAC,EAAE;KAAC;IACpD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;IAC3B,OAAO;QAAC;QAAQ,SAAS,CAAC,OAAO,WAAW,WAAW,OAAO,MAAM,GAAG,CAAC;KAAE;AAC9E;AACA,SAAS,aAAa,OAAO;IACzB,IAAI,UAAU;IACd,IAAI,YAAY;IAChB,IAAI,iBAAiB;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACrC,MAAM,SAAS,OAAO,CAAC,EAAE;QACzB,OAAQ,MAAM,CAAC,EAAE;YACb,KAAK;gBACD,WACI,CAAC,YAAY,KAAK,KAAK,iBAAiB,SAAS,IAAI,IACjD,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG;gBACnC,YAAY;gBACZ,iBAAiB;gBACjB;YACJ,KAAK;gBACD,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,KACxB,KAAK;gBACT,YAAY;gBACZ;YACJ;gBACI,sEAAsE;gBACtE,IAAI,CAAC,WACD,iBAAiB;gBACrB,YAAY;QACpB;IACJ;IACA,OAAO;QAAE;QAAS;IAAe;AACrC;AACA;;;;;;;;;;CAUC,GACD,MAAM;IACF,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,OAAO,GAAG,CAAC,QAAQ,MAAM,SAAS;YACnC,MAAM,MAAM,YAAY;YACxB,IAAI,SACA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,WAAW,CAAC,KAAK,MAAM;iBAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,cAAc,CAAC,KAAK,MAAM;QAC9D;QACA,wEAAwE;QACxE,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,UAAU,CAAC;YAAE,SAAS,QAAQ,OAAO,IAAI;QAAM;QAChF,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,SAAS,GAAG,EAAE,QAAQ,EAAE;QACpB,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,aAAa,IAAI,CAAC,OAAO;QAC7D,oDAAoD;QACpD,IAAI,SAAS;YACT,MAAM,KAAK,IAAI,QAAQ;YACvB,IAAI,UAAU;gBACV,IAAI,OAAO,GAAG,IAAI,OAAO,GAAG,GAAG,IAAI,OAAO,CAAC,EAAE,EAAE,SAAS,GAAG;YAC/D,OACK,IAAI,kBAAkB,IAAI,UAAU,CAAC,QAAQ,IAAI,CAAC,IAAI;gBACvD,IAAI,aAAa,GAAG;YACxB,OACK,IAAI,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;gBACnE,IAAI,KAAK,GAAG,KAAK,CAAC,EAAE;gBACpB,IAAI,SAAS,MAAM,CAAC,KAChB,KAAK,GAAG,GAAG;gBACf,MAAM,KAAK,GAAG,aAAa;gBAC3B,GAAG,aAAa,GAAG,KAAK,GAAG,QAAQ,EAAE,EAAE,IAAI,GAAG;YAClD,OACK;gBACD,MAAM,KAAK,GAAG,aAAa;gBAC3B,GAAG,aAAa,GAAG,KAAK,GAAG,QAAQ,EAAE,EAAE,IAAI,GAAG;YAClD;QACJ;QACA,IAAI,UAAU;YACV,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM;YAClD,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,QAAQ;QAC1D,OACK;YACD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;YACxB,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAChC;QACA,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;IACtB;IACA;;;;KAIC,GACD,aAAa;QACT,OAAO;YACH,SAAS,aAAa,IAAI,CAAC,OAAO,EAAE,OAAO;YAC3C,YAAY,IAAI,CAAC,UAAU;YAC3B,QAAQ,IAAI,CAAC,MAAM;YACnB,UAAU,IAAI,CAAC,QAAQ;QAC3B;IACJ;IACA;;;;;KAKC,GACD,CAAC,QAAQ,MAAM,EAAE,WAAW,KAAK,EAAE,YAAY,CAAC,CAAC,EAAE;QAC/C,KAAK,MAAM,SAAS,OAChB,OAAO,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;IAC9B;IACA,2CAA2C,GAC3C,CAAC,KAAK,KAAK,EAAE;QACT,IAAI,aAAa,GAAG,CAAC,UAAU,EAC3B,QAAQ,GAAG,CAAC,OAAO;YAAE,OAAO;QAAK;QACrC,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,MAAM,EAAE,CAAC,QAAQ,SAAS;oBAChD,MAAM,MAAM,YAAY;oBACxB,GAAG,CAAC,EAAE,IAAI;oBACV,IAAI,CAAC,OAAO,CAAC,KAAK,iBAAiB,SAAS;gBAChD;gBACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM;gBAC9B,IAAI,CAAC,YAAY,GAAG;gBACpB;YACJ,KAAK;gBAAY;oBACb,MAAM,MAAM,WAAW,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,OAAO;oBACpF,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,EAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,gBAAgB;oBACxC,IAAI,CAAC,QAAQ,CAAC,KAAK;oBACnB,IAAI,IAAI,CAAC,GAAG,EACR,MAAM,IAAI,CAAC,GAAG;oBAClB,IAAI,CAAC,GAAG,GAAG;oBACX,IAAI,CAAC,YAAY,GAAG;oBACpB;gBACJ;YACA,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM;gBAC9B;YACJ,KAAK;gBAAS;oBACV,MAAM,MAAM,MAAM,MAAM,GAClB,GAAG,MAAM,OAAO,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,GAAG,GACnD,MAAM,OAAO;oBACnB,MAAM,QAAQ,IAAI,OAAO,cAAc,CAAC,YAAY,QAAQ,oBAAoB;oBAChF,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,GAAG,EAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;yBAEjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;oBACzB;gBACJ;YACA,KAAK;gBAAW;oBACZ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;wBACX,MAAM,MAAM;wBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,cAAc,CAAC,YAAY,QAAQ,oBAAoB;wBACnF;oBACJ;oBACA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG;oBAC7B,MAAM,MAAM,WAAW,UAAU,CAAC,MAAM,GAAG,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;oBACtH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE;oBACxB,IAAI,IAAI,OAAO,EAAE;wBACb,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO;wBAC3B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,GAAG,GAAG,EAAE,EAAE,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO;oBACjE;oBACA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM;oBAC9B;gBACJ;YACA;gBACI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,cAAc,CAAC,YAAY,QAAQ,oBAAoB,CAAC,kBAAkB,EAAE,MAAM,IAAI,EAAE;QAC5H;IACJ;IACA;;;;;KAKC,GACD,CAAC,IAAI,WAAW,KAAK,EAAE,YAAY,CAAC,CAAC,EAAE;QACnC,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE;YACxB,MAAM,IAAI,CAAC,GAAG;YACd,IAAI,CAAC,GAAG,GAAG;QACf,OACK,IAAI,UAAU;YACf,MAAM,OAAO,OAAO,MAAM,CAAC;gBAAE,aAAa,IAAI,CAAC,UAAU;YAAC,GAAG,IAAI,CAAC,OAAO;YACzE,MAAM,MAAM,IAAI,SAAS,QAAQ,CAAC,WAAW;YAC7C,IAAI,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,OAAO,CAAC,WAAW,gBAAgB;YAC5C,IAAI,KAAK,GAAG;gBAAC;gBAAG;gBAAW;aAAU;YACrC,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,MAAM;QACV;IACJ;AACJ;AAEA,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/cst-scalar.js"], "sourcesContent": ["'use strict';\n\nvar resolveBlockScalar = require('../compose/resolve-block-scalar.js');\nvar resolveFlowScalar = require('../compose/resolve-flow-scalar.js');\nvar errors = require('../errors.js');\nvar stringifyString = require('../stringify/stringifyString.js');\n\nfunction resolveAsScalar(token, strict = true, onError) {\n    if (token) {\n        const _onError = (pos, code, message) => {\n            const offset = typeof pos === 'number' ? pos : Array.isArray(pos) ? pos[0] : pos.offset;\n            if (onError)\n                onError(offset, code, message);\n            else\n                throw new errors.YAMLParseError([offset, offset + 1], code, message);\n        };\n        switch (token.type) {\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return resolveFlowScalar.resolveFlowScalar(token, strict, _onError);\n            case 'block-scalar':\n                return resolveBlockScalar.resolveBlockScalar({ options: { strict } }, token, _onError);\n        }\n    }\n    return null;\n}\n/**\n * Create a new scalar token with `value`\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.end Comments and whitespace after the end of the value, or after the block scalar header. If undefined, a newline will be added.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.indent The indent level of the token.\n * @param context.inFlow Is this scalar within a flow collection? This may affect the resolved type of the token's value.\n * @param context.offset The offset position of the token.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction createScalarToken(value, context) {\n    const { implicitKey = false, indent, inFlow = false, offset = -1, type = 'PLAIN' } = context;\n    const source = stringifyString.stringifyString({ type, value }, {\n        implicitKey,\n        indent: indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    const end = context.end ?? [\n        { type: 'newline', offset: -1, indent, source: '\\n' }\n    ];\n    switch (source[0]) {\n        case '|':\n        case '>': {\n            const he = source.indexOf('\\n');\n            const head = source.substring(0, he);\n            const body = source.substring(he + 1) + '\\n';\n            const props = [\n                { type: 'block-scalar-header', offset, indent, source: head }\n            ];\n            if (!addEndtoBlockProps(props, end))\n                props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n            return { type: 'block-scalar', offset, indent, props, source: body };\n        }\n        case '\"':\n            return { type: 'double-quoted-scalar', offset, indent, source, end };\n        case \"'\":\n            return { type: 'single-quoted-scalar', offset, indent, source, end };\n        default:\n            return { type: 'scalar', offset, indent, source, end };\n    }\n}\n/**\n * Set the value of `token` to the given string `value`, overwriting any previous contents and type that it may have.\n *\n * Best efforts are made to retain any comments previously associated with the `token`,\n * though all contents within a collection's `items` will be overwritten.\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param token Any token. If it does not include an `indent` value, the value will be stringified as if it were an implicit key.\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.afterKey In most cases, values after a key should have an additional level of indentation.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.inFlow Being within a flow collection may affect the resolved type of the token's value.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction setScalarValue(token, value, context = {}) {\n    let { afterKey = false, implicitKey = false, inFlow = false, type } = context;\n    let indent = 'indent' in token ? token.indent : null;\n    if (afterKey && typeof indent === 'number')\n        indent += 2;\n    if (!type)\n        switch (token.type) {\n            case 'single-quoted-scalar':\n                type = 'QUOTE_SINGLE';\n                break;\n            case 'double-quoted-scalar':\n                type = 'QUOTE_DOUBLE';\n                break;\n            case 'block-scalar': {\n                const header = token.props[0];\n                if (header.type !== 'block-scalar-header')\n                    throw new Error('Invalid block scalar header');\n                type = header.source[0] === '>' ? 'BLOCK_FOLDED' : 'BLOCK_LITERAL';\n                break;\n            }\n            default:\n                type = 'PLAIN';\n        }\n    const source = stringifyString.stringifyString({ type, value }, {\n        implicitKey: implicitKey || indent === null,\n        indent: indent !== null && indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    switch (source[0]) {\n        case '|':\n        case '>':\n            setBlockScalarValue(token, source);\n            break;\n        case '\"':\n            setFlowScalarValue(token, source, 'double-quoted-scalar');\n            break;\n        case \"'\":\n            setFlowScalarValue(token, source, 'single-quoted-scalar');\n            break;\n        default:\n            setFlowScalarValue(token, source, 'scalar');\n    }\n}\nfunction setBlockScalarValue(token, source) {\n    const he = source.indexOf('\\n');\n    const head = source.substring(0, he);\n    const body = source.substring(he + 1) + '\\n';\n    if (token.type === 'block-scalar') {\n        const header = token.props[0];\n        if (header.type !== 'block-scalar-header')\n            throw new Error('Invalid block scalar header');\n        header.source = head;\n        token.source = body;\n    }\n    else {\n        const { offset } = token;\n        const indent = 'indent' in token ? token.indent : -1;\n        const props = [\n            { type: 'block-scalar-header', offset, indent, source: head }\n        ];\n        if (!addEndtoBlockProps(props, 'end' in token ? token.end : undefined))\n            props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n        for (const key of Object.keys(token))\n            if (key !== 'type' && key !== 'offset')\n                delete token[key];\n        Object.assign(token, { type: 'block-scalar', indent, props, source: body });\n    }\n}\n/** @returns `true` if last token is a newline */\nfunction addEndtoBlockProps(props, end) {\n    if (end)\n        for (const st of end)\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                    props.push(st);\n                    break;\n                case 'newline':\n                    props.push(st);\n                    return true;\n            }\n    return false;\n}\nfunction setFlowScalarValue(token, source, type) {\n    switch (token.type) {\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            token.type = type;\n            token.source = source;\n            break;\n        case 'block-scalar': {\n            const end = token.props.slice(1);\n            let oa = source.length;\n            if (token.props[0].type === 'block-scalar-header')\n                oa -= token.props[0].source.length;\n            for (const tok of end)\n                tok.offset += oa;\n            delete token.props;\n            Object.assign(token, { type, source, end });\n            break;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            const offset = token.offset + source.length;\n            const nl = { type: 'newline', offset, indent: token.indent, source: '\\n' };\n            delete token.items;\n            Object.assign(token, { type, source, end: [nl] });\n            break;\n        }\n        default: {\n            const indent = 'indent' in token ? token.indent : -1;\n            const end = 'end' in token && Array.isArray(token.end)\n                ? token.end.filter(st => st.type === 'space' ||\n                    st.type === 'comment' ||\n                    st.type === 'newline')\n                : [];\n            for (const key of Object.keys(token))\n                if (key !== 'type' && key !== 'offset')\n                    delete token[key];\n            Object.assign(token, { type, indent, source, end });\n        }\n    }\n}\n\nexports.createScalarToken = createScalarToken;\nexports.resolveAsScalar = resolveAsScalar;\nexports.setScalarValue = setScalarValue;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAgB,KAAK,EAAE,SAAS,IAAI,EAAE,OAAO;IAClD,IAAI,OAAO;QACP,MAAM,WAAW,CAAC,KAAK,MAAM;YACzB,MAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,MAAM,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;YACvF,IAAI,SACA,QAAQ,QAAQ,MAAM;iBAEtB,MAAM,IAAI,OAAO,cAAc,CAAC;gBAAC;gBAAQ,SAAS;aAAE,EAAE,MAAM;QACpE;QACA,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,kBAAkB,iBAAiB,CAAC,OAAO,QAAQ;YAC9D,KAAK;gBACD,OAAO,mBAAmB,kBAAkB,CAAC;oBAAE,SAAS;wBAAE;oBAAO;gBAAE,GAAG,OAAO;QACrF;IACJ;IACA,OAAO;AACX;AACA;;;;;;;;;;;;;CAaC,GACD,SAAS,kBAAkB,KAAK,EAAE,OAAO;IACrC,MAAM,EAAE,cAAc,KAAK,EAAE,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,GAAG;IACrF,MAAM,SAAS,gBAAgB,eAAe,CAAC;QAAE;QAAM;IAAM,GAAG;QAC5D;QACA,QAAQ,SAAS,IAAI,IAAI,MAAM,CAAC,UAAU;QAC1C;QACA,SAAS;YAAE,YAAY;YAAM,WAAW,CAAC;QAAE;IAC/C;IACA,MAAM,MAAM,QAAQ,GAAG,IAAI;QACvB;YAAE,MAAM;YAAW,QAAQ,CAAC;YAAG;YAAQ,QAAQ;QAAK;KACvD;IACD,OAAQ,MAAM,CAAC,EAAE;QACb,KAAK;QACL,KAAK;YAAK;gBACN,MAAM,KAAK,OAAO,OAAO,CAAC;gBAC1B,MAAM,OAAO,OAAO,SAAS,CAAC,GAAG;gBACjC,MAAM,OAAO,OAAO,SAAS,CAAC,KAAK,KAAK;gBACxC,MAAM,QAAQ;oBACV;wBAAE,MAAM;wBAAuB;wBAAQ;wBAAQ,QAAQ;oBAAK;iBAC/D;gBACD,IAAI,CAAC,mBAAmB,OAAO,MAC3B,MAAM,IAAI,CAAC;oBAAE,MAAM;oBAAW,QAAQ,CAAC;oBAAG;oBAAQ,QAAQ;gBAAK;gBACnE,OAAO;oBAAE,MAAM;oBAAgB;oBAAQ;oBAAQ;oBAAO,QAAQ;gBAAK;YACvE;QACA,KAAK;YACD,OAAO;gBAAE,MAAM;gBAAwB;gBAAQ;gBAAQ;gBAAQ;YAAI;QACvE,KAAK;YACD,OAAO;gBAAE,MAAM;gBAAwB;gBAAQ;gBAAQ;gBAAQ;YAAI;QACvE;YACI,OAAO;gBAAE,MAAM;gBAAU;gBAAQ;gBAAQ;gBAAQ;YAAI;IAC7D;AACJ;AACA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IAC9C,IAAI,EAAE,WAAW,KAAK,EAAE,cAAc,KAAK,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,GAAG;IACtE,IAAI,SAAS,YAAY,QAAQ,MAAM,MAAM,GAAG;IAChD,IAAI,YAAY,OAAO,WAAW,UAC9B,UAAU;IACd,IAAI,CAAC,MACD,OAAQ,MAAM,IAAI;QACd,KAAK;YACD,OAAO;YACP;QACJ,KAAK;YACD,OAAO;YACP;QACJ,KAAK;YAAgB;gBACjB,MAAM,SAAS,MAAM,KAAK,CAAC,EAAE;gBAC7B,IAAI,OAAO,IAAI,KAAK,uBAChB,MAAM,IAAI,MAAM;gBACpB,OAAO,OAAO,MAAM,CAAC,EAAE,KAAK,MAAM,iBAAiB;gBACnD;YACJ;QACA;YACI,OAAO;IACf;IACJ,MAAM,SAAS,gBAAgB,eAAe,CAAC;QAAE;QAAM;IAAM,GAAG;QAC5D,aAAa,eAAe,WAAW;QACvC,QAAQ,WAAW,QAAQ,SAAS,IAAI,IAAI,MAAM,CAAC,UAAU;QAC7D;QACA,SAAS;YAAE,YAAY;YAAM,WAAW,CAAC;QAAE;IAC/C;IACA,OAAQ,MAAM,CAAC,EAAE;QACb,KAAK;QACL,KAAK;YACD,oBAAoB,OAAO;YAC3B;QACJ,KAAK;YACD,mBAAmB,OAAO,QAAQ;YAClC;QACJ,KAAK;YACD,mBAAmB,OAAO,QAAQ;YAClC;QACJ;YACI,mBAAmB,OAAO,QAAQ;IAC1C;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,MAAM;IACtC,MAAM,KAAK,OAAO,OAAO,CAAC;IAC1B,MAAM,OAAO,OAAO,SAAS,CAAC,GAAG;IACjC,MAAM,OAAO,OAAO,SAAS,CAAC,KAAK,KAAK;IACxC,IAAI,MAAM,IAAI,KAAK,gBAAgB;QAC/B,MAAM,SAAS,MAAM,KAAK,CAAC,EAAE;QAC7B,IAAI,OAAO,IAAI,KAAK,uBAChB,MAAM,IAAI,MAAM;QACpB,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,GAAG;IACnB,OACK;QACD,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,SAAS,YAAY,QAAQ,MAAM,MAAM,GAAG,CAAC;QACnD,MAAM,QAAQ;YACV;gBAAE,MAAM;gBAAuB;gBAAQ;gBAAQ,QAAQ;YAAK;SAC/D;QACD,IAAI,CAAC,mBAAmB,OAAO,SAAS,QAAQ,MAAM,GAAG,GAAG,YACxD,MAAM,IAAI,CAAC;YAAE,MAAM;YAAW,QAAQ,CAAC;YAAG;YAAQ,QAAQ;QAAK;QACnE,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,OAC1B,IAAI,QAAQ,UAAU,QAAQ,UAC1B,OAAO,KAAK,CAAC,IAAI;QACzB,OAAO,MAAM,CAAC,OAAO;YAAE,MAAM;YAAgB;YAAQ;YAAO,QAAQ;QAAK;IAC7E;AACJ;AACA,+CAA+C,GAC/C,SAAS,mBAAmB,KAAK,EAAE,GAAG;IAClC,IAAI,KACA,KAAK,MAAM,MAAM,IACb,OAAQ,GAAG,IAAI;QACX,KAAK;QACL,KAAK;YACD,MAAM,IAAI,CAAC;YACX;QACJ,KAAK;YACD,MAAM,IAAI,CAAC;YACX,OAAO;IACf;IACR,OAAO;AACX;AACA,SAAS,mBAAmB,KAAK,EAAE,MAAM,EAAE,IAAI;IAC3C,OAAQ,MAAM,IAAI;QACd,KAAK;QACL,KAAK;QACL,KAAK;YACD,MAAM,IAAI,GAAG;YACb,MAAM,MAAM,GAAG;YACf;QACJ,KAAK;YAAgB;gBACjB,MAAM,MAAM,MAAM,KAAK,CAAC,KAAK,CAAC;gBAC9B,IAAI,KAAK,OAAO,MAAM;gBACtB,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uBACxB,MAAM,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM;gBACtC,KAAK,MAAM,OAAO,IACd,IAAI,MAAM,IAAI;gBAClB,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,CAAC,OAAO;oBAAE;oBAAM;oBAAQ;gBAAI;gBACzC;YACJ;QACA,KAAK;QACL,KAAK;YAAa;gBACd,MAAM,SAAS,MAAM,MAAM,GAAG,OAAO,MAAM;gBAC3C,MAAM,KAAK;oBAAE,MAAM;oBAAW;oBAAQ,QAAQ,MAAM,MAAM;oBAAE,QAAQ;gBAAK;gBACzE,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,CAAC,OAAO;oBAAE;oBAAM;oBAAQ,KAAK;wBAAC;qBAAG;gBAAC;gBAC/C;YACJ;QACA;YAAS;gBACL,MAAM,SAAS,YAAY,QAAQ,MAAM,MAAM,GAAG,CAAC;gBACnD,MAAM,MAAM,SAAS,SAAS,MAAM,OAAO,CAAC,MAAM,GAAG,IAC/C,MAAM,GAAG,CAAC,MAAM,CAAC,CAAA,KAAM,GAAG,IAAI,KAAK,WACjC,GAAG,IAAI,KAAK,aACZ,GAAG,IAAI,KAAK,aACd,EAAE;gBACR,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,OAC1B,IAAI,QAAQ,UAAU,QAAQ,UAC1B,OAAO,KAAK,CAAC,IAAI;gBACzB,OAAO,MAAM,CAAC,OAAO;oBAAE;oBAAM;oBAAQ;oBAAQ;gBAAI;YACrD;IACJ;AACJ;AAEA,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,eAAe,GAAG;AAC1B,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/cst-stringify.js"], "sourcesContent": ["'use strict';\n\n/**\n * Stringify a CST document, token, or collection item\n *\n * Fair warning: This applies no validation whatsoever, and\n * simply concatenates the sources in their logical order.\n */\nconst stringify = (cst) => 'type' in cst ? stringifyToken(cst) : stringifyItem(cst);\nfunction stringifyToken(token) {\n    switch (token.type) {\n        case 'block-scalar': {\n            let res = '';\n            for (const tok of token.props)\n                res += stringifyToken(tok);\n            return res + token.source;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            let res = '';\n            for (const item of token.items)\n                res += stringifyItem(item);\n            return res;\n        }\n        case 'flow-collection': {\n            let res = token.start.source;\n            for (const item of token.items)\n                res += stringifyItem(item);\n            for (const st of token.end)\n                res += st.source;\n            return res;\n        }\n        case 'document': {\n            let res = stringifyItem(token);\n            if (token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n        default: {\n            let res = token.source;\n            if ('end' in token && token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n    }\n}\nfunction stringifyItem({ start, key, sep, value }) {\n    let res = '';\n    for (const st of start)\n        res += st.source;\n    if (key)\n        res += stringifyToken(key);\n    if (sep)\n        for (const st of sep)\n            res += st.source;\n    if (value)\n        res += stringifyToken(value);\n    return res;\n}\n\nexports.stringify = stringify;\n"], "names": [], "mappings": "AAEA;;;;;CAKC,GACD,MAAM,YAAY,CAAC,MAAQ,UAAU,MAAM,eAAe,OAAO,cAAc;AAC/E,SAAS,eAAe,KAAK;IACzB,OAAQ,MAAM,IAAI;QACd,KAAK;YAAgB;gBACjB,IAAI,MAAM;gBACV,KAAK,MAAM,OAAO,MAAM,KAAK,CACzB,OAAO,eAAe;gBAC1B,OAAO,MAAM,MAAM,MAAM;YAC7B;QACA,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,MAAM;gBACV,KAAK,MAAM,QAAQ,MAAM,KAAK,CAC1B,OAAO,cAAc;gBACzB,OAAO;YACX;QACA,KAAK;YAAmB;gBACpB,IAAI,MAAM,MAAM,KAAK,CAAC,MAAM;gBAC5B,KAAK,MAAM,QAAQ,MAAM,KAAK,CAC1B,OAAO,cAAc;gBACzB,KAAK,MAAM,MAAM,MAAM,GAAG,CACtB,OAAO,GAAG,MAAM;gBACpB,OAAO;YACX;QACA,KAAK;YAAY;gBACb,IAAI,MAAM,cAAc;gBACxB,IAAI,MAAM,GAAG,EACT,KAAK,MAAM,MAAM,MAAM,GAAG,CACtB,OAAO,GAAG,MAAM;gBACxB,OAAO;YACX;QACA;YAAS;gBACL,IAAI,MAAM,MAAM,MAAM;gBACtB,IAAI,SAAS,SAAS,MAAM,GAAG,EAC3B,KAAK,MAAM,MAAM,MAAM,GAAG,CACtB,OAAO,GAAG,MAAM;gBACxB,OAAO;YACX;IACJ;AACJ;AACA,SAAS,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IAC7C,IAAI,MAAM;IACV,KAAK,MAAM,MAAM,MACb,OAAO,GAAG,MAAM;IACpB,IAAI,KACA,OAAO,eAAe;IAC1B,IAAI,KACA,KAAK,MAAM,MAAM,IACb,OAAO,GAAG,MAAM;IACxB,IAAI,OACA,OAAO,eAAe;IAC1B,OAAO;AACX;AAEA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/cst-visit.js"], "sourcesContent": ["'use strict';\n\nconst BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove item');\n/**\n * Apply a visitor to a CST document or item.\n *\n * Walks through the tree (depth-first) starting from the root, calling a\n * `visitor` function with two arguments when entering each item:\n *   - `item`: The current item, which included the following members:\n *     - `start: SourceToken[]` – Source tokens before the key or value,\n *       possibly including its anchor or tag.\n *     - `key?: Token | null` – Set for pair values. May then be `null`, if\n *       the key before the `:` separator is empty.\n *     - `sep?: SourceToken[]` – Source tokens between the key and the value,\n *       which should include the `:` map value indicator if `value` is set.\n *     - `value?: Token` – The value of a sequence item, or of a map pair.\n *   - `path`: The steps from the root to the current node, as an array of\n *     `['key' | 'value', number]` tuples.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this token, continue with\n *      next sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current item, then continue with the next one\n *   - `number`: Set the index of the next step. This is useful especially if\n *     the index of the current token has changed.\n *   - `function`: Define the next visitor for this item. After the original\n *     visitor is called on item entry, next visitors are called after handling\n *     a non-empty `key` and when exiting the item.\n */\nfunction visit(cst, visitor) {\n    if ('type' in cst && cst.type === 'document')\n        cst = { start: cst.start, value: cst.value };\n    _visit(Object.freeze([]), cst, visitor);\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current item */\nvisit.SKIP = SKIP;\n/** Remove the current item */\nvisit.REMOVE = REMOVE;\n/** Find the item at `path` from `cst` as the root */\nvisit.itemAtPath = (cst, path) => {\n    let item = cst;\n    for (const [field, index] of path) {\n        const tok = item?.[field];\n        if (tok && 'items' in tok) {\n            item = tok.items[index];\n        }\n        else\n            return undefined;\n    }\n    return item;\n};\n/**\n * Get the immediate parent collection of the item at `path` from `cst` as the root.\n *\n * Throws an error if the collection is not found, which should never happen if the item itself exists.\n */\nvisit.parentCollection = (cst, path) => {\n    const parent = visit.itemAtPath(cst, path.slice(0, -1));\n    const field = path[path.length - 1][0];\n    const coll = parent?.[field];\n    if (coll && 'items' in coll)\n        return coll;\n    throw new Error('Parent collection not found');\n};\nfunction _visit(path, item, visitor) {\n    let ctrl = visitor(item, path);\n    if (typeof ctrl === 'symbol')\n        return ctrl;\n    for (const field of ['key', 'value']) {\n        const token = item[field];\n        if (token && 'items' in token) {\n            for (let i = 0; i < token.items.length; ++i) {\n                const ci = _visit(Object.freeze(path.concat([[field, i]])), token.items[i], visitor);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    token.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n            if (typeof ctrl === 'function' && field === 'key')\n                ctrl = ctrl(item, path);\n        }\n    }\n    return typeof ctrl === 'function' ? ctrl(item, path) : ctrl;\n}\n\nexports.visit = visit;\n"], "names": [], "mappings": "AAEA,MAAM,QAAQ,OAAO;AACrB,MAAM,OAAO,OAAO;AACpB,MAAM,SAAS,OAAO;AACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,SAAS,MAAM,GAAG,EAAE,OAAO;IACvB,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,YAC9B,MAAM;QAAE,OAAO,IAAI,KAAK;QAAE,OAAO,IAAI,KAAK;IAAC;IAC/C,OAAO,OAAO,MAAM,CAAC,EAAE,GAAG,KAAK;AACnC;AACA,kEAAkE;AAClE,+DAA+D;AAC/D,mCAAmC;AACnC,yCAAyC,GACzC,MAAM,KAAK,GAAG;AACd,kDAAkD,GAClD,MAAM,IAAI,GAAG;AACb,4BAA4B,GAC5B,MAAM,MAAM,GAAG;AACf,mDAAmD,GACnD,MAAM,UAAU,GAAG,CAAC,KAAK;IACrB,IAAI,OAAO;IACX,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,KAAM;QAC/B,MAAM,MAAM,MAAM,CAAC,MAAM;QACzB,IAAI,OAAO,WAAW,KAAK;YACvB,OAAO,IAAI,KAAK,CAAC,MAAM;QAC3B,OAEI,OAAO;IACf;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,MAAM,gBAAgB,GAAG,CAAC,KAAK;IAC3B,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC;IACpD,MAAM,QAAQ,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE;IACtC,MAAM,OAAO,QAAQ,CAAC,MAAM;IAC5B,IAAI,QAAQ,WAAW,MACnB,OAAO;IACX,MAAM,IAAI,MAAM;AACpB;AACA,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,OAAO;IAC/B,IAAI,OAAO,QAAQ,MAAM;IACzB,IAAI,OAAO,SAAS,UAChB,OAAO;IACX,KAAK,MAAM,SAAS;QAAC;QAAO;KAAQ,CAAE;QAClC,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,IAAI,SAAS,WAAW,OAAO;YAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;gBACzC,MAAM,KAAK,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;oBAAC;wBAAC;wBAAO;qBAAE;iBAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;gBAC5E,IAAI,OAAO,OAAO,UACd,IAAI,KAAK;qBACR,IAAI,OAAO,OACZ,OAAO;qBACN,IAAI,OAAO,QAAQ;oBACpB,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG;oBACtB,KAAK;gBACT;YACJ;YACA,IAAI,OAAO,SAAS,cAAc,UAAU,OACxC,OAAO,KAAK,MAAM;QAC1B;IACJ;IACA,OAAO,OAAO,SAAS,aAAa,KAAK,MAAM,QAAQ;AAC3D;AAEA,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/cst.js"], "sourcesContent": ["'use strict';\n\nvar cstScalar = require('./cst-scalar.js');\nvar cstStringify = require('./cst-stringify.js');\nvar cstVisit = require('./cst-visit.js');\n\n/** The byte order mark */\nconst BOM = '\\u{FEFF}';\n/** Start of doc-mode */\nconst DOCUMENT = '\\x02'; // C0: Start of Text\n/** Unexpected end of flow-mode */\nconst FLOW_END = '\\x18'; // C0: Cancel\n/** Next token is a scalar value */\nconst SCALAR = '\\x1f'; // C0: Unit Separator\n/** @returns `true` if `token` is a flow or block collection */\nconst isCollection = (token) => !!token && 'items' in token;\n/** @returns `true` if `token` is a flow or block scalar; not an alias */\nconst isScalar = (token) => !!token &&\n    (token.type === 'scalar' ||\n        token.type === 'single-quoted-scalar' ||\n        token.type === 'double-quoted-scalar' ||\n        token.type === 'block-scalar');\n/* istanbul ignore next */\n/** Get a printable representation of a lexer token */\nfunction prettyToken(token) {\n    switch (token) {\n        case BOM:\n            return '<BOM>';\n        case DOCUMENT:\n            return '<DOC>';\n        case FLOW_END:\n            return '<FLOW_END>';\n        case SCALAR:\n            return '<SCALAR>';\n        default:\n            return JSON.stringify(token);\n    }\n}\n/** Identify the type of a lexer token. May return `null` for unknown tokens. */\nfunction tokenType(source) {\n    switch (source) {\n        case BOM:\n            return 'byte-order-mark';\n        case DOCUMENT:\n            return 'doc-mode';\n        case FLOW_END:\n            return 'flow-error-end';\n        case SCALAR:\n            return 'scalar';\n        case '---':\n            return 'doc-start';\n        case '...':\n            return 'doc-end';\n        case '':\n        case '\\n':\n        case '\\r\\n':\n            return 'newline';\n        case '-':\n            return 'seq-item-ind';\n        case '?':\n            return 'explicit-key-ind';\n        case ':':\n            return 'map-value-ind';\n        case '{':\n            return 'flow-map-start';\n        case '}':\n            return 'flow-map-end';\n        case '[':\n            return 'flow-seq-start';\n        case ']':\n            return 'flow-seq-end';\n        case ',':\n            return 'comma';\n    }\n    switch (source[0]) {\n        case ' ':\n        case '\\t':\n            return 'space';\n        case '#':\n            return 'comment';\n        case '%':\n            return 'directive-line';\n        case '*':\n            return 'alias';\n        case '&':\n            return 'anchor';\n        case '!':\n            return 'tag';\n        case \"'\":\n            return 'single-quoted-scalar';\n        case '\"':\n            return 'double-quoted-scalar';\n        case '|':\n        case '>':\n            return 'block-scalar-header';\n    }\n    return null;\n}\n\nexports.createScalarToken = cstScalar.createScalarToken;\nexports.resolveAsScalar = cstScalar.resolveAsScalar;\nexports.setScalarValue = cstScalar.setScalarValue;\nexports.stringify = cstStringify.stringify;\nexports.visit = cstVisit.visit;\nexports.BOM = BOM;\nexports.DOCUMENT = DOCUMENT;\nexports.FLOW_END = FLOW_END;\nexports.SCALAR = SCALAR;\nexports.isCollection = isCollection;\nexports.isScalar = isScalar;\nexports.prettyToken = prettyToken;\nexports.tokenType = tokenType;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,wBAAwB,GACxB,MAAM,MAAM;AACZ,sBAAsB,GACtB,MAAM,WAAW,QAAQ,oBAAoB;AAC7C,gCAAgC,GAChC,MAAM,WAAW,QAAQ,aAAa;AACtC,iCAAiC,GACjC,MAAM,SAAS,QAAQ,qBAAqB;AAC5C,6DAA6D,GAC7D,MAAM,eAAe,CAAC,QAAU,CAAC,CAAC,SAAS,WAAW;AACtD,uEAAuE,GACvE,MAAM,WAAW,CAAC,QAAU,CAAC,CAAC,SAC1B,CAAC,MAAM,IAAI,KAAK,YACZ,MAAM,IAAI,KAAK,0BACf,MAAM,IAAI,KAAK,0BACf,MAAM,IAAI,KAAK,cAAc;AACrC,wBAAwB,GACxB,oDAAoD,GACpD,SAAS,YAAY,KAAK;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO,KAAK,SAAS,CAAC;IAC9B;AACJ;AACA,8EAA8E,GAC9E,SAAS,UAAU,MAAM;IACrB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;IACf;IACA,OAAQ,MAAM,CAAC,EAAE;QACb,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;IACf;IACA,OAAO;AACX;AAEA,QAAQ,iBAAiB,GAAG,UAAU,iBAAiB;AACvD,QAAQ,eAAe,GAAG,UAAU,eAAe;AACnD,QAAQ,cAAc,GAAG,UAAU,cAAc;AACjD,QAAQ,SAAS,GAAG,aAAa,SAAS;AAC1C,QAAQ,KAAK,GAAG,SAAS,KAAK;AAC9B,QAAQ,GAAG,GAAG;AACd,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,MAAM,GAAG;AACjB,QAAQ,YAAY,GAAG;AACvB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/lexer.js"], "sourcesContent": ["'use strict';\n\nvar cst = require('./cst.js');\n\n/*\nSTART -> stream\n\nstream\n  directive -> line-end -> stream\n  indent + line-end -> stream\n  [else] -> line-start\n\nline-end\n  comment -> line-end\n  newline -> .\n  input-end -> END\n\nline-start\n  doc-start -> doc\n  doc-end -> stream\n  [else] -> indent -> block-start\n\nblock-start\n  seq-item-start -> block-start\n  explicit-key-start -> block-start\n  map-value-start -> block-start\n  [else] -> doc\n\ndoc\n  line-end -> line-start\n  spaces -> doc\n  anchor -> doc\n  tag -> doc\n  flow-start -> flow -> doc\n  flow-end -> error -> doc\n  seq-item-start -> error -> doc\n  explicit-key-start -> error -> doc\n  map-value-start -> doc\n  alias -> doc\n  quote-start -> quoted-scalar -> doc\n  block-scalar-header -> line-end -> block-scalar(min) -> line-start\n  [else] -> plain-scalar(false, min) -> doc\n\nflow\n  line-end -> flow\n  spaces -> flow\n  anchor -> flow\n  tag -> flow\n  flow-start -> flow -> flow\n  flow-end -> .\n  seq-item-start -> error -> flow\n  explicit-key-start -> flow\n  map-value-start -> flow\n  alias -> flow\n  quote-start -> quoted-scalar -> flow\n  comma -> flow\n  [else] -> plain-scalar(true, 0) -> flow\n\nquoted-scalar\n  quote-end -> .\n  [else] -> quoted-scalar\n\nblock-scalar(min)\n  newline + peek(indent < min) -> .\n  [else] -> block-scalar(min)\n\nplain-scalar(is-flow, min)\n  scalar-end(is-flow) -> .\n  peek(newline + (indent < min)) -> .\n  [else] -> plain-scalar(min)\n*/\nfunction isEmpty(ch) {\n    switch (ch) {\n        case undefined:\n        case ' ':\n        case '\\n':\n        case '\\r':\n        case '\\t':\n            return true;\n        default:\n            return false;\n    }\n}\nconst hexDigits = new Set('0123456789ABCDEFabcdef');\nconst tagChars = new Set(\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()\");\nconst flowIndicatorChars = new Set(',[]{}');\nconst invalidAnchorChars = new Set(' ,[]{}\\n\\r\\t');\nconst isNotAnchorChar = (ch) => !ch || invalidAnchorChars.has(ch);\n/**\n * Splits an input string into lexical tokens, i.e. smaller strings that are\n * easily identifiable by `tokens.tokenType()`.\n *\n * Lexing starts always in a \"stream\" context. Incomplete input may be buffered\n * until a complete token can be emitted.\n *\n * In addition to slices of the original input, the following control characters\n * may also be emitted:\n *\n * - `\\x02` (Start of Text): A document starts with the next token\n * - `\\x18` (Cancel): Unexpected end of flow-mode (indicates an error)\n * - `\\x1f` (Unit Separator): Next token is a scalar value\n * - `\\u{FEFF}` (Byte order mark): Emitted separately outside documents\n */\nclass Lexer {\n    constructor() {\n        /**\n         * Flag indicating whether the end of the current buffer marks the end of\n         * all input\n         */\n        this.atEnd = false;\n        /**\n         * Explicit indent set in block scalar header, as an offset from the current\n         * minimum indent, so e.g. set to 1 from a header `|2+`. Set to -1 if not\n         * explicitly set.\n         */\n        this.blockScalarIndent = -1;\n        /**\n         * Block scalars that include a + (keep) chomping indicator in their header\n         * include trailing empty lines, which are otherwise excluded from the\n         * scalar's contents.\n         */\n        this.blockScalarKeep = false;\n        /** Current input */\n        this.buffer = '';\n        /**\n         * Flag noting whether the map value indicator : can immediately follow this\n         * node within a flow context.\n         */\n        this.flowKey = false;\n        /** Count of surrounding flow collection levels. */\n        this.flowLevel = 0;\n        /**\n         * Minimum level of indentation required for next lines to be parsed as a\n         * part of the current scalar value.\n         */\n        this.indentNext = 0;\n        /** Indentation level of the current line. */\n        this.indentValue = 0;\n        /** Position of the next \\n character. */\n        this.lineEndPos = null;\n        /** Stores the state of the lexer if reaching the end of incpomplete input */\n        this.next = null;\n        /** A pointer to `buffer`; the current position of the lexer. */\n        this.pos = 0;\n    }\n    /**\n     * Generate YAML tokens from the `source` string. If `incomplete`,\n     * a part of the last line may be left as a buffer for the next call.\n     *\n     * @returns A generator of lexical tokens\n     */\n    *lex(source, incomplete = false) {\n        if (source) {\n            if (typeof source !== 'string')\n                throw TypeError('source is not a string');\n            this.buffer = this.buffer ? this.buffer + source : source;\n            this.lineEndPos = null;\n        }\n        this.atEnd = !incomplete;\n        let next = this.next ?? 'stream';\n        while (next && (incomplete || this.hasChars(1)))\n            next = yield* this.parseNext(next);\n    }\n    atLineEnd() {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (ch === ' ' || ch === '\\t')\n            ch = this.buffer[++i];\n        if (!ch || ch === '#' || ch === '\\n')\n            return true;\n        if (ch === '\\r')\n            return this.buffer[i + 1] === '\\n';\n        return false;\n    }\n    charAt(n) {\n        return this.buffer[this.pos + n];\n    }\n    continueScalar(offset) {\n        let ch = this.buffer[offset];\n        if (this.indentNext > 0) {\n            let indent = 0;\n            while (ch === ' ')\n                ch = this.buffer[++indent + offset];\n            if (ch === '\\r') {\n                const next = this.buffer[indent + offset + 1];\n                if (next === '\\n' || (!next && !this.atEnd))\n                    return offset + indent + 1;\n            }\n            return ch === '\\n' || indent >= this.indentNext || (!ch && !this.atEnd)\n                ? offset + indent\n                : -1;\n        }\n        if (ch === '-' || ch === '.') {\n            const dt = this.buffer.substr(offset, 3);\n            if ((dt === '---' || dt === '...') && isEmpty(this.buffer[offset + 3]))\n                return -1;\n        }\n        return offset;\n    }\n    getLine() {\n        let end = this.lineEndPos;\n        if (typeof end !== 'number' || (end !== -1 && end < this.pos)) {\n            end = this.buffer.indexOf('\\n', this.pos);\n            this.lineEndPos = end;\n        }\n        if (end === -1)\n            return this.atEnd ? this.buffer.substring(this.pos) : null;\n        if (this.buffer[end - 1] === '\\r')\n            end -= 1;\n        return this.buffer.substring(this.pos, end);\n    }\n    hasChars(n) {\n        return this.pos + n <= this.buffer.length;\n    }\n    setNext(state) {\n        this.buffer = this.buffer.substring(this.pos);\n        this.pos = 0;\n        this.lineEndPos = null;\n        this.next = state;\n        return null;\n    }\n    peek(n) {\n        return this.buffer.substr(this.pos, n);\n    }\n    *parseNext(next) {\n        switch (next) {\n            case 'stream':\n                return yield* this.parseStream();\n            case 'line-start':\n                return yield* this.parseLineStart();\n            case 'block-start':\n                return yield* this.parseBlockStart();\n            case 'doc':\n                return yield* this.parseDocument();\n            case 'flow':\n                return yield* this.parseFlowCollection();\n            case 'quoted-scalar':\n                return yield* this.parseQuotedScalar();\n            case 'block-scalar':\n                return yield* this.parseBlockScalar();\n            case 'plain-scalar':\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseStream() {\n        let line = this.getLine();\n        if (line === null)\n            return this.setNext('stream');\n        if (line[0] === cst.BOM) {\n            yield* this.pushCount(1);\n            line = line.substring(1);\n        }\n        if (line[0] === '%') {\n            let dirEnd = line.length;\n            let cs = line.indexOf('#');\n            while (cs !== -1) {\n                const ch = line[cs - 1];\n                if (ch === ' ' || ch === '\\t') {\n                    dirEnd = cs - 1;\n                    break;\n                }\n                else {\n                    cs = line.indexOf('#', cs + 1);\n                }\n            }\n            while (true) {\n                const ch = line[dirEnd - 1];\n                if (ch === ' ' || ch === '\\t')\n                    dirEnd -= 1;\n                else\n                    break;\n            }\n            const n = (yield* this.pushCount(dirEnd)) + (yield* this.pushSpaces(true));\n            yield* this.pushCount(line.length - n); // possible comment\n            this.pushNewline();\n            return 'stream';\n        }\n        if (this.atLineEnd()) {\n            const sp = yield* this.pushSpaces(true);\n            yield* this.pushCount(line.length - sp);\n            yield* this.pushNewline();\n            return 'stream';\n        }\n        yield cst.DOCUMENT;\n        return yield* this.parseLineStart();\n    }\n    *parseLineStart() {\n        const ch = this.charAt(0);\n        if (!ch && !this.atEnd)\n            return this.setNext('line-start');\n        if (ch === '-' || ch === '.') {\n            if (!this.atEnd && !this.hasChars(4))\n                return this.setNext('line-start');\n            const s = this.peek(3);\n            if ((s === '---' || s === '...') && isEmpty(this.charAt(3))) {\n                yield* this.pushCount(3);\n                this.indentValue = 0;\n                this.indentNext = 0;\n                return s === '---' ? 'doc' : 'stream';\n            }\n        }\n        this.indentValue = yield* this.pushSpaces(false);\n        if (this.indentNext > this.indentValue && !isEmpty(this.charAt(1)))\n            this.indentNext = this.indentValue;\n        return yield* this.parseBlockStart();\n    }\n    *parseBlockStart() {\n        const [ch0, ch1] = this.peek(2);\n        if (!ch1 && !this.atEnd)\n            return this.setNext('block-start');\n        if ((ch0 === '-' || ch0 === '?' || ch0 === ':') && isEmpty(ch1)) {\n            const n = (yield* this.pushCount(1)) + (yield* this.pushSpaces(true));\n            this.indentNext = this.indentValue + 1;\n            this.indentValue += n;\n            return yield* this.parseBlockStart();\n        }\n        return 'doc';\n    }\n    *parseDocument() {\n        yield* this.pushSpaces(true);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('doc');\n        let n = yield* this.pushIndicators();\n        switch (line[n]) {\n            case '#':\n                yield* this.pushCount(line.length - n);\n            // fallthrough\n            case undefined:\n                yield* this.pushNewline();\n                return yield* this.parseLineStart();\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel = 1;\n                return 'flow';\n            case '}':\n            case ']':\n                // this is an error\n                yield* this.pushCount(1);\n                return 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'doc';\n            case '\"':\n            case \"'\":\n                return yield* this.parseQuotedScalar();\n            case '|':\n            case '>':\n                n += yield* this.parseBlockScalarHeader();\n                n += yield* this.pushSpaces(true);\n                yield* this.pushCount(line.length - n);\n                yield* this.pushNewline();\n                return yield* this.parseBlockScalar();\n            default:\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseFlowCollection() {\n        let nl, sp;\n        let indent = -1;\n        do {\n            nl = yield* this.pushNewline();\n            if (nl > 0) {\n                sp = yield* this.pushSpaces(false);\n                this.indentValue = indent = sp;\n            }\n            else {\n                sp = 0;\n            }\n            sp += yield* this.pushSpaces(true);\n        } while (nl + sp > 0);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('flow');\n        if ((indent !== -1 && indent < this.indentNext && line[0] !== '#') ||\n            (indent === 0 &&\n                (line.startsWith('---') || line.startsWith('...')) &&\n                isEmpty(line[3]))) {\n            // Allowing for the terminal ] or } at the same (rather than greater)\n            // indent level as the initial [ or { is technically invalid, but\n            // failing here would be surprising to users.\n            const atFlowEndMarker = indent === this.indentNext - 1 &&\n                this.flowLevel === 1 &&\n                (line[0] === ']' || line[0] === '}');\n            if (!atFlowEndMarker) {\n                // this is an error\n                this.flowLevel = 0;\n                yield cst.FLOW_END;\n                return yield* this.parseLineStart();\n            }\n        }\n        let n = 0;\n        while (line[n] === ',') {\n            n += yield* this.pushCount(1);\n            n += yield* this.pushSpaces(true);\n            this.flowKey = false;\n        }\n        n += yield* this.pushIndicators();\n        switch (line[n]) {\n            case undefined:\n                return 'flow';\n            case '#':\n                yield* this.pushCount(line.length - n);\n                return 'flow';\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel += 1;\n                return 'flow';\n            case '}':\n            case ']':\n                yield* this.pushCount(1);\n                this.flowKey = true;\n                this.flowLevel -= 1;\n                return this.flowLevel ? 'flow' : 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'flow';\n            case '\"':\n            case \"'\":\n                this.flowKey = true;\n                return yield* this.parseQuotedScalar();\n            case ':': {\n                const next = this.charAt(1);\n                if (this.flowKey || isEmpty(next) || next === ',') {\n                    this.flowKey = false;\n                    yield* this.pushCount(1);\n                    yield* this.pushSpaces(true);\n                    return 'flow';\n                }\n            }\n            // fallthrough\n            default:\n                this.flowKey = false;\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseQuotedScalar() {\n        const quote = this.charAt(0);\n        let end = this.buffer.indexOf(quote, this.pos + 1);\n        if (quote === \"'\") {\n            while (end !== -1 && this.buffer[end + 1] === \"'\")\n                end = this.buffer.indexOf(\"'\", end + 2);\n        }\n        else {\n            // double-quote\n            while (end !== -1) {\n                let n = 0;\n                while (this.buffer[end - 1 - n] === '\\\\')\n                    n += 1;\n                if (n % 2 === 0)\n                    break;\n                end = this.buffer.indexOf('\"', end + 1);\n            }\n        }\n        // Only looking for newlines within the quotes\n        const qb = this.buffer.substring(0, end);\n        let nl = qb.indexOf('\\n', this.pos);\n        if (nl !== -1) {\n            while (nl !== -1) {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = qb.indexOf('\\n', cs);\n            }\n            if (nl !== -1) {\n                // this is an error caused by an unexpected unindent\n                end = nl - (qb[nl - 1] === '\\r' ? 2 : 1);\n            }\n        }\n        if (end === -1) {\n            if (!this.atEnd)\n                return this.setNext('quoted-scalar');\n            end = this.buffer.length;\n        }\n        yield* this.pushToIndex(end + 1, false);\n        return this.flowLevel ? 'flow' : 'doc';\n    }\n    *parseBlockScalarHeader() {\n        this.blockScalarIndent = -1;\n        this.blockScalarKeep = false;\n        let i = this.pos;\n        while (true) {\n            const ch = this.buffer[++i];\n            if (ch === '+')\n                this.blockScalarKeep = true;\n            else if (ch > '0' && ch <= '9')\n                this.blockScalarIndent = Number(ch) - 1;\n            else if (ch !== '-')\n                break;\n        }\n        return yield* this.pushUntil(ch => isEmpty(ch) || ch === '#');\n    }\n    *parseBlockScalar() {\n        let nl = this.pos - 1; // may be -1 if this.pos === 0\n        let indent = 0;\n        let ch;\n        loop: for (let i = this.pos; (ch = this.buffer[i]); ++i) {\n            switch (ch) {\n                case ' ':\n                    indent += 1;\n                    break;\n                case '\\n':\n                    nl = i;\n                    indent = 0;\n                    break;\n                case '\\r': {\n                    const next = this.buffer[i + 1];\n                    if (!next && !this.atEnd)\n                        return this.setNext('block-scalar');\n                    if (next === '\\n')\n                        break;\n                } // fallthrough\n                default:\n                    break loop;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('block-scalar');\n        if (indent >= this.indentNext) {\n            if (this.blockScalarIndent === -1)\n                this.indentNext = indent;\n            else {\n                this.indentNext =\n                    this.blockScalarIndent + (this.indentNext === 0 ? 1 : this.indentNext);\n            }\n            do {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = this.buffer.indexOf('\\n', cs);\n            } while (nl !== -1);\n            if (nl === -1) {\n                if (!this.atEnd)\n                    return this.setNext('block-scalar');\n                nl = this.buffer.length;\n            }\n        }\n        // Trailing insufficiently indented tabs are invalid.\n        // To catch that during parsing, we include them in the block scalar value.\n        let i = nl + 1;\n        ch = this.buffer[i];\n        while (ch === ' ')\n            ch = this.buffer[++i];\n        if (ch === '\\t') {\n            while (ch === '\\t' || ch === ' ' || ch === '\\r' || ch === '\\n')\n                ch = this.buffer[++i];\n            nl = i - 1;\n        }\n        else if (!this.blockScalarKeep) {\n            do {\n                let i = nl - 1;\n                let ch = this.buffer[i];\n                if (ch === '\\r')\n                    ch = this.buffer[--i];\n                const lastChar = i; // Drop the line if last char not more indented\n                while (ch === ' ')\n                    ch = this.buffer[--i];\n                if (ch === '\\n' && i >= this.pos && i + 1 + indent > lastChar)\n                    nl = i;\n                else\n                    break;\n            } while (true);\n        }\n        yield cst.SCALAR;\n        yield* this.pushToIndex(nl + 1, true);\n        return yield* this.parseLineStart();\n    }\n    *parsePlainScalar() {\n        const inFlow = this.flowLevel > 0;\n        let end = this.pos - 1;\n        let i = this.pos - 1;\n        let ch;\n        while ((ch = this.buffer[++i])) {\n            if (ch === ':') {\n                const next = this.buffer[i + 1];\n                if (isEmpty(next) || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                end = i;\n            }\n            else if (isEmpty(ch)) {\n                let next = this.buffer[i + 1];\n                if (ch === '\\r') {\n                    if (next === '\\n') {\n                        i += 1;\n                        ch = '\\n';\n                        next = this.buffer[i + 1];\n                    }\n                    else\n                        end = i;\n                }\n                if (next === '#' || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                if (ch === '\\n') {\n                    const cs = this.continueScalar(i + 1);\n                    if (cs === -1)\n                        break;\n                    i = Math.max(i, cs - 2); // to advance, but still account for ' #'\n                }\n            }\n            else {\n                if (inFlow && flowIndicatorChars.has(ch))\n                    break;\n                end = i;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('plain-scalar');\n        yield cst.SCALAR;\n        yield* this.pushToIndex(end + 1, true);\n        return inFlow ? 'flow' : 'doc';\n    }\n    *pushCount(n) {\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos += n;\n            return n;\n        }\n        return 0;\n    }\n    *pushToIndex(i, allowEmpty) {\n        const s = this.buffer.slice(this.pos, i);\n        if (s) {\n            yield s;\n            this.pos += s.length;\n            return s.length;\n        }\n        else if (allowEmpty)\n            yield '';\n        return 0;\n    }\n    *pushIndicators() {\n        switch (this.charAt(0)) {\n            case '!':\n                return ((yield* this.pushTag()) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '&':\n                return ((yield* this.pushUntil(isNotAnchorChar)) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '-': // this is an error\n            case '?': // this is an error outside flow collections\n            case ':': {\n                const inFlow = this.flowLevel > 0;\n                const ch1 = this.charAt(1);\n                if (isEmpty(ch1) || (inFlow && flowIndicatorChars.has(ch1))) {\n                    if (!inFlow)\n                        this.indentNext = this.indentValue + 1;\n                    else if (this.flowKey)\n                        this.flowKey = false;\n                    return ((yield* this.pushCount(1)) +\n                        (yield* this.pushSpaces(true)) +\n                        (yield* this.pushIndicators()));\n                }\n            }\n        }\n        return 0;\n    }\n    *pushTag() {\n        if (this.charAt(1) === '<') {\n            let i = this.pos + 2;\n            let ch = this.buffer[i];\n            while (!isEmpty(ch) && ch !== '>')\n                ch = this.buffer[++i];\n            return yield* this.pushToIndex(ch === '>' ? i + 1 : i, false);\n        }\n        else {\n            let i = this.pos + 1;\n            let ch = this.buffer[i];\n            while (ch) {\n                if (tagChars.has(ch))\n                    ch = this.buffer[++i];\n                else if (ch === '%' &&\n                    hexDigits.has(this.buffer[i + 1]) &&\n                    hexDigits.has(this.buffer[i + 2])) {\n                    ch = this.buffer[(i += 3)];\n                }\n                else\n                    break;\n            }\n            return yield* this.pushToIndex(i, false);\n        }\n    }\n    *pushNewline() {\n        const ch = this.buffer[this.pos];\n        if (ch === '\\n')\n            return yield* this.pushCount(1);\n        else if (ch === '\\r' && this.charAt(1) === '\\n')\n            return yield* this.pushCount(2);\n        else\n            return 0;\n    }\n    *pushSpaces(allowTabs) {\n        let i = this.pos - 1;\n        let ch;\n        do {\n            ch = this.buffer[++i];\n        } while (ch === ' ' || (allowTabs && ch === '\\t'));\n        const n = i - this.pos;\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos = i;\n        }\n        return n;\n    }\n    *pushUntil(test) {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (!test(ch))\n            ch = this.buffer[++i];\n        return yield* this.pushToIndex(i, false);\n    }\n}\n\nexports.Lexer = Lexer;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,GACA,SAAS,QAAQ,EAAE;IACf,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,YAAY,IAAI,IAAI;AAC1B,MAAM,WAAW,IAAI,IAAI;AACzB,MAAM,qBAAqB,IAAI,IAAI;AACnC,MAAM,qBAAqB,IAAI,IAAI;AACnC,MAAM,kBAAkB,CAAC,KAAO,CAAC,MAAM,mBAAmB,GAAG,CAAC;AAC9D;;;;;;;;;;;;;;CAcC,GACD,MAAM;IACF,aAAc;QACV;;;SAGC,GACD,IAAI,CAAC,KAAK,GAAG;QACb;;;;SAIC,GACD,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B;;;;SAIC,GACD,IAAI,CAAC,eAAe,GAAG;QACvB,kBAAkB,GAClB,IAAI,CAAC,MAAM,GAAG;QACd;;;SAGC,GACD,IAAI,CAAC,OAAO,GAAG;QACf,iDAAiD,GACjD,IAAI,CAAC,SAAS,GAAG;QACjB;;;SAGC,GACD,IAAI,CAAC,UAAU,GAAG;QAClB,2CAA2C,GAC3C,IAAI,CAAC,WAAW,GAAG;QACnB,uCAAuC,GACvC,IAAI,CAAC,UAAU,GAAG;QAClB,2EAA2E,GAC3E,IAAI,CAAC,IAAI,GAAG;QACZ,8DAA8D,GAC9D,IAAI,CAAC,GAAG,GAAG;IACf;IACA;;;;;KAKC,GACD,CAAC,IAAI,MAAM,EAAE,aAAa,KAAK,EAAE;QAC7B,IAAI,QAAQ;YACR,IAAI,OAAO,WAAW,UAClB,MAAM,UAAU;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS;YACnD,IAAI,CAAC,UAAU,GAAG;QACtB;QACA,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI;QACxB,MAAO,QAAQ,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC,EAAE,EAC1C,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;IACrC;IACA,YAAY;QACR,IAAI,IAAI,IAAI,CAAC,GAAG;QAChB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;QACvB,MAAO,OAAO,OAAO,OAAO,KACxB,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI,CAAC,MAAM,OAAO,OAAO,OAAO,MAC5B,OAAO;QACX,IAAI,OAAO,MACP,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK;QAClC,OAAO;IACX;IACA,OAAO,CAAC,EAAE;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;IACpC;IACA,eAAe,MAAM,EAAE;QACnB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO;QAC5B,IAAI,IAAI,CAAC,UAAU,GAAG,GAAG;YACrB,IAAI,SAAS;YACb,MAAO,OAAO,IACV,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,SAAS,OAAO;YACvC,IAAI,OAAO,MAAM;gBACb,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,SAAS,EAAE;gBAC7C,IAAI,SAAS,QAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EACtC,OAAO,SAAS,SAAS;YACjC;YACA,OAAO,OAAO,QAAQ,UAAU,IAAI,CAAC,UAAU,IAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAChE,SAAS,SACT,CAAC;QACX;QACA,IAAI,OAAO,OAAO,OAAO,KAAK;YAC1B,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ;YACtC,IAAI,CAAC,OAAO,SAAS,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,GACjE,OAAO,CAAC;QAChB;QACA,OAAO;IACX;IACA,UAAU;QACN,IAAI,MAAM,IAAI,CAAC,UAAU;QACzB,IAAI,OAAO,QAAQ,YAAa,QAAQ,CAAC,KAAK,MAAM,IAAI,CAAC,GAAG,EAAG;YAC3D,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;YACxC,IAAI,CAAC,UAAU,GAAG;QACtB;QACA,IAAI,QAAQ,CAAC,GACT,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;QAC1D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,MACzB,OAAO;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE;IAC3C;IACA,SAAS,CAAC,EAAE;QACR,OAAO,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7C;IACA,QAAQ,KAAK,EAAE;QACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;QAC5C,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO;IACX;IACA,KAAK,CAAC,EAAE;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;IACxC;IACA,CAAC,UAAU,IAAI,EAAE;QACb,OAAQ;YACJ,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,WAAW;YAClC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,cAAc;YACrC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,eAAe;YACtC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,aAAa;YACpC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,mBAAmB;YAC1C,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,iBAAiB;YACxC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,gBAAgB;YACvC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,gBAAgB;QAC3C;IACJ;IACA,CAAC,cAAc;QACX,IAAI,OAAO,IAAI,CAAC,OAAO;QACvB,IAAI,SAAS,MACT,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,GAAG,EAAE;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC;YACtB,OAAO,KAAK,SAAS,CAAC;QAC1B;QACA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;YACjB,IAAI,SAAS,KAAK,MAAM;YACxB,IAAI,KAAK,KAAK,OAAO,CAAC;YACtB,MAAO,OAAO,CAAC,EAAG;gBACd,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE;gBACvB,IAAI,OAAO,OAAO,OAAO,MAAM;oBAC3B,SAAS,KAAK;oBACd;gBACJ,OACK;oBACD,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK;gBAChC;YACJ;YACA,MAAO,KAAM;gBACT,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;gBAC3B,IAAI,OAAO,OAAO,OAAO,MACrB,UAAU;qBAEV;YACR;YACA,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK;YACzE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG,IAAI,mBAAmB;YAC3D,IAAI,CAAC,WAAW;YAChB,OAAO;QACX;QACA,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,MAAM,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG;YACpC,OAAO,IAAI,CAAC,WAAW;YACvB,OAAO;QACX;QACA,MAAM,IAAI,QAAQ;QAClB,OAAO,OAAO,IAAI,CAAC,cAAc;IACrC;IACA,CAAC,iBAAiB;QACd,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAClB,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,OAAO,OAAO,OAAO,KAAK;YAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAC9B,OAAO,IAAI,CAAC,OAAO,CAAC;YACxB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC;YACpB,IAAI,CAAC,MAAM,SAAS,MAAM,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;gBACzD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,CAAC,UAAU,GAAG;gBAClB,OAAO,MAAM,QAAQ,QAAQ;YACjC;QACJ;QACA,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,UAAU,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;QACtC,OAAO,OAAO,IAAI,CAAC,eAAe;IACtC;IACA,CAAC,kBAAkB;QACf,MAAM,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EACnB,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,GAAG,KAAK,QAAQ,MAAM;YAC7D,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK;YACpE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG;YACrC,IAAI,CAAC,WAAW,IAAI;YACpB,OAAO,OAAO,IAAI,CAAC,eAAe;QACtC;QACA,OAAO;IACX;IACA,CAAC,gBAAgB;QACb,OAAO,IAAI,CAAC,UAAU,CAAC;QACvB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,IAAI,SAAS,MACT,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,IAAI,OAAO,IAAI,CAAC,cAAc;QAClC,OAAQ,IAAI,CAAC,EAAE;YACX,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG;YACxC,cAAc;YACd,KAAK;gBACD,OAAO,IAAI,CAAC,WAAW;gBACvB,OAAO,OAAO,IAAI,CAAC,cAAc;YACrC,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,SAAS,GAAG;gBACjB,OAAO;YACX,KAAK;YACL,KAAK;gBACD,mBAAmB;gBACnB,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,OAAO;YACX,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,iBAAiB;YACxC,KAAK;YACL,KAAK;gBACD,KAAK,OAAO,IAAI,CAAC,sBAAsB;gBACvC,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC;gBAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG;gBACpC,OAAO,IAAI,CAAC,WAAW;gBACvB,OAAO,OAAO,IAAI,CAAC,gBAAgB;YACvC;gBACI,OAAO,OAAO,IAAI,CAAC,gBAAgB;QAC3C;IACJ;IACA,CAAC,sBAAsB;QACnB,IAAI,IAAI;QACR,IAAI,SAAS,CAAC;QACd,GAAG;YACC,KAAK,OAAO,IAAI,CAAC,WAAW;YAC5B,IAAI,KAAK,GAAG;gBACR,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC;gBAC5B,IAAI,CAAC,WAAW,GAAG,SAAS;YAChC,OACK;gBACD,KAAK;YACT;YACA,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC;QACjC,QAAS,KAAK,KAAK,EAAG;QACtB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,IAAI,SAAS,MACT,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,AAAC,WAAW,CAAC,KAAK,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,EAAE,KAAK,OACzD,WAAW,KACR,CAAC,KAAK,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,MAAM,KACjD,QAAQ,IAAI,CAAC,EAAE,GAAI;YACvB,qEAAqE;YACrE,iEAAiE;YACjE,6CAA6C;YAC7C,MAAM,kBAAkB,WAAW,IAAI,CAAC,UAAU,GAAG,KACjD,IAAI,CAAC,SAAS,KAAK,KACnB,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG;YACvC,IAAI,CAAC,iBAAiB;gBAClB,mBAAmB;gBACnB,IAAI,CAAC,SAAS,GAAG;gBACjB,MAAM,IAAI,QAAQ;gBAClB,OAAO,OAAO,IAAI,CAAC,cAAc;YACrC;QACJ;QACA,IAAI,IAAI;QACR,MAAO,IAAI,CAAC,EAAE,KAAK,IAAK;YACpB,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC;YAC3B,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,OAAO,IAAI,CAAC,cAAc;QAC/B,OAAQ,IAAI,CAAC,EAAE;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG;gBACpC,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,SAAS,IAAI;gBAClB,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,SAAS,IAAI;gBAClB,OAAO,IAAI,CAAC,SAAS,GAAG,SAAS;YACrC,KAAK;gBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACtB,OAAO;YACX,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,OAAO,GAAG;gBACf,OAAO,OAAO,IAAI,CAAC,iBAAiB;YACxC,KAAK;gBAAK;oBACN,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;oBACzB,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,SAAS,SAAS,KAAK;wBAC/C,IAAI,CAAC,OAAO,GAAG;wBACf,OAAO,IAAI,CAAC,SAAS,CAAC;wBACtB,OAAO,IAAI,CAAC,UAAU,CAAC;wBACvB,OAAO;oBACX;gBACJ;YACA,cAAc;YACd;gBACI,IAAI,CAAC,OAAO,GAAG;gBACf,OAAO,OAAO,IAAI,CAAC,gBAAgB;QAC3C;IACJ;IACA,CAAC,oBAAoB;QACjB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,GAAG,GAAG;QAChD,IAAI,UAAU,KAAK;YACf,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAC1C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,MAAM;QAC7C,OACK;YACD,eAAe;YACf,MAAO,QAAQ,CAAC,EAAG;gBACf,IAAI,IAAI;gBACR,MAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,KAAK,KAChC,KAAK;gBACT,IAAI,IAAI,MAAM,GACV;gBACJ,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,MAAM;YACzC;QACJ;QACA,8CAA8C;QAC9C,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG;QACpC,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;QAClC,IAAI,OAAO,CAAC,GAAG;YACX,MAAO,OAAO,CAAC,EAAG;gBACd,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK;gBACpC,IAAI,OAAO,CAAC,GACR;gBACJ,KAAK,GAAG,OAAO,CAAC,MAAM;YAC1B;YACA,IAAI,OAAO,CAAC,GAAG;gBACX,oDAAoD;gBACpD,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,OAAO,IAAI,CAAC;YAC3C;QACJ;QACA,IAAI,QAAQ,CAAC,GAAG;YACZ,IAAI,CAAC,IAAI,CAAC,KAAK,EACX,OAAO,IAAI,CAAC,OAAO,CAAC;YACxB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;QAC5B;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QACjC,OAAO,IAAI,CAAC,SAAS,GAAG,SAAS;IACrC;IACA,CAAC,yBAAyB;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,IAAI,IAAI,CAAC,GAAG;QAChB,MAAO,KAAM;YACT,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YAC3B,IAAI,OAAO,KACP,IAAI,CAAC,eAAe,GAAG;iBACtB,IAAI,KAAK,OAAO,MAAM,KACvB,IAAI,CAAC,iBAAiB,GAAG,OAAO,MAAM;iBACrC,IAAI,OAAO,KACZ;QACR;QACA,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC,CAAA,KAAM,QAAQ,OAAO,OAAO;IAC7D;IACA,CAAC,mBAAmB;QAChB,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,8BAA8B;QACrD,IAAI,SAAS;QACb,IAAI;QACJ,MAAM,IAAK,IAAI,IAAI,IAAI,CAAC,GAAG,EAAG,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAG,EAAE,EAAG;YACrD,OAAQ;gBACJ,KAAK;oBACD,UAAU;oBACV;gBACJ,KAAK;oBACD,KAAK;oBACL,SAAS;oBACT;gBACJ,KAAK;oBAAM;wBACP,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;wBAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EACpB,OAAO,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,SAAS,MACT;oBACR;gBACA;oBACI,MAAM;YACd;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAClB,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,UAAU,IAAI,CAAC,UAAU,EAAE;YAC3B,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,GAC5B,IAAI,CAAC,UAAU,GAAG;iBACjB;gBACD,IAAI,CAAC,UAAU,GACX,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU;YAC7E;YACA,GAAG;gBACC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK;gBACpC,IAAI,OAAO,CAAC,GACR;gBACJ,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;YACnC,QAAS,OAAO,CAAC,EAAG;YACpB,IAAI,OAAO,CAAC,GAAG;gBACX,IAAI,CAAC,IAAI,CAAC,KAAK,EACX,OAAO,IAAI,CAAC,OAAO,CAAC;gBACxB,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM;YAC3B;QACJ;QACA,qDAAqD;QACrD,2EAA2E;QAC3E,IAAI,IAAI,KAAK;QACb,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;QACnB,MAAO,OAAO,IACV,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI,OAAO,MAAM;YACb,MAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO,KACtD,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACzB,KAAK,IAAI;QACb,OACK,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC5B,GAAG;gBACC,IAAI,IAAI,KAAK;gBACb,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,OAAO,MACP,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBACzB,MAAM,WAAW,GAAG,+CAA+C;gBACnE,MAAO,OAAO,IACV,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBACzB,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,SAAS,UACjD,KAAK;qBAEL;YACR,QAAS,KAAM;QACnB;QACA,MAAM,IAAI,MAAM;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;QAChC,OAAO,OAAO,IAAI,CAAC,cAAc;IACrC;IACA,CAAC,mBAAmB;QAChB,MAAM,SAAS,IAAI,CAAC,SAAS,GAAG;QAChC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG;QACrB,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG;QACnB,IAAI;QACJ,MAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAG;YAC5B,IAAI,OAAO,KAAK;gBACZ,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBAC/B,IAAI,QAAQ,SAAU,UAAU,mBAAmB,GAAG,CAAC,OACnD;gBACJ,MAAM;YACV,OACK,IAAI,QAAQ,KAAK;gBAClB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBAC7B,IAAI,OAAO,MAAM;oBACb,IAAI,SAAS,MAAM;wBACf,KAAK;wBACL,KAAK;wBACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC7B,OAEI,MAAM;gBACd;gBACA,IAAI,SAAS,OAAQ,UAAU,mBAAmB,GAAG,CAAC,OAClD;gBACJ,IAAI,OAAO,MAAM;oBACb,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI;oBACnC,IAAI,OAAO,CAAC,GACR;oBACJ,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,yCAAyC;gBACtE;YACJ,OACK;gBACD,IAAI,UAAU,mBAAmB,GAAG,CAAC,KACjC;gBACJ,MAAM;YACV;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAClB,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,MAAM,IAAI,MAAM;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QACjC,OAAO,SAAS,SAAS;IAC7B;IACA,CAAC,UAAU,CAAC,EAAE;QACV,IAAI,IAAI,GAAG;YACP,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,GAAG,IAAI;YACZ,OAAO;QACX;QACA,OAAO;IACX;IACA,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE;QACxB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;QACtC,IAAI,GAAG;YACH,MAAM;YACN,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM;YACpB,OAAO,EAAE,MAAM;QACnB,OACK,IAAI,YACL,MAAM;QACV,OAAO;IACX;IACA,CAAC,iBAAiB;QACd,OAAQ,IAAI,CAAC,MAAM,CAAC;YAChB,KAAK;gBACD,OAAQ,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,IAC1B,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,IAC7B,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;YACrC,KAAK;gBACD,OAAQ,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAC3C,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,IAC7B,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;YACrC,KAAK;YACL,KAAK;YACL,KAAK;gBAAK;oBACN,MAAM,SAAS,IAAI,CAAC,SAAS,GAAG;oBAChC,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC;oBACxB,IAAI,QAAQ,QAAS,UAAU,mBAAmB,GAAG,CAAC,MAAO;wBACzD,IAAI,CAAC,QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG;6BACpC,IAAI,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,OAAO,GAAG;wBACnB,OAAQ,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAC7B,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,IAC7B,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;oBACrC;gBACJ;QACJ;QACA,OAAO;IACX;IACA,CAAC,UAAU;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK;YACxB,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG;YACnB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;YACvB,MAAO,CAAC,QAAQ,OAAO,OAAO,IAC1B,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACzB,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,MAAM,IAAI,IAAI,GAAG;QAC3D,OACK;YACD,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG;YACnB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;YACvB,MAAO,GAAI;gBACP,IAAI,SAAS,GAAG,CAAC,KACb,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;qBACpB,IAAI,OAAO,OACZ,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAChC,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG;oBACnC,KAAK,IAAI,CAAC,MAAM,CAAE,KAAK,EAAG;gBAC9B,OAEI;YACR;YACA,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;QACtC;IACJ;IACA,CAAC,cAAc;QACX,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,IAAI,OAAO,MACP,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;aAC5B,IAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,MACvC,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;aAE7B,OAAO;IACf;IACA,CAAC,WAAW,SAAS,EAAE;QACnB,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG;QACnB,IAAI;QACJ,GAAG;YACC,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACzB,QAAS,OAAO,OAAQ,aAAa,OAAO,KAAO;QACnD,MAAM,IAAI,IAAI,IAAI,CAAC,GAAG;QACtB,IAAI,IAAI,GAAG;YACP,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,GAAG,GAAG;QACf;QACA,OAAO;IACX;IACA,CAAC,UAAU,IAAI,EAAE;QACb,IAAI,IAAI,IAAI,CAAC,GAAG;QAChB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;QACvB,MAAO,CAAC,KAAK,IACT,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACzB,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;IACtC;AACJ;AAEA,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/line-counter.js"], "sourcesContent": ["'use strict';\n\n/**\n * Tracks newlines during parsing in order to provide an efficient API for\n * determining the one-indexed `{ line, col }` position for any offset\n * within the input.\n */\nclass LineCounter {\n    constructor() {\n        this.lineStarts = [];\n        /**\n         * Should be called in ascending order. Otherwise, call\n         * `lineCounter.lineStarts.sort()` before calling `linePos()`.\n         */\n        this.addNewLine = (offset) => this.lineStarts.push(offset);\n        /**\n         * Performs a binary search and returns the 1-indexed { line, col }\n         * position of `offset`. If `line === 0`, `addNewLine` has never been\n         * called or `offset` is before the first known newline.\n         */\n        this.linePos = (offset) => {\n            let low = 0;\n            let high = this.lineStarts.length;\n            while (low < high) {\n                const mid = (low + high) >> 1; // Math.floor((low + high) / 2)\n                if (this.lineStarts[mid] < offset)\n                    low = mid + 1;\n                else\n                    high = mid;\n            }\n            if (this.lineStarts[low] === offset)\n                return { line: low + 1, col: 1 };\n            if (low === 0)\n                return { line: 0, col: offset };\n            const start = this.lineStarts[low - 1];\n            return { line: low, col: offset - start + 1 };\n        };\n    }\n}\n\nexports.LineCounter = LineCounter;\n"], "names": [], "mappings": "AAEA;;;;CAIC,GACD,MAAM;IACF,aAAc;QACV,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB;;;SAGC,GACD,IAAI,CAAC,UAAU,GAAG,CAAC,SAAW,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACnD;;;;SAIC,GACD,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,MAAM;YACV,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YACjC,MAAO,MAAM,KAAM;gBACf,MAAM,MAAM,AAAC,MAAM,QAAS,GAAG,+BAA+B;gBAC9D,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,QACvB,MAAM,MAAM;qBAEZ,OAAO;YACf;YACA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,QACzB,OAAO;gBAAE,MAAM,MAAM;gBAAG,KAAK;YAAE;YACnC,IAAI,QAAQ,GACR,OAAO;gBAAE,MAAM;gBAAG,KAAK;YAAO;YAClC,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACtC,OAAO;gBAAE,MAAM;gBAAK,KAAK,SAAS,QAAQ;YAAE;QAChD;IACJ;AACJ;AAEA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/parse/parser.js"], "sourcesContent": ["'use strict';\n\nvar node_process = require('process');\nvar cst = require('./cst.js');\nvar lexer = require('./lexer.js');\n\nfunction includesToken(list, type) {\n    for (let i = 0; i < list.length; ++i)\n        if (list[i].type === type)\n            return true;\n    return false;\n}\nfunction findNonEmptyIndex(list) {\n    for (let i = 0; i < list.length; ++i) {\n        switch (list[i].type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                break;\n            default:\n                return i;\n        }\n    }\n    return -1;\n}\nfunction isFlowToken(token) {\n    switch (token?.type) {\n        case 'alias':\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'flow-collection':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getPrevProps(parent) {\n    switch (parent.type) {\n        case 'document':\n            return parent.start;\n        case 'block-map': {\n            const it = parent.items[parent.items.length - 1];\n            return it.sep ?? it.start;\n        }\n        case 'block-seq':\n            return parent.items[parent.items.length - 1].start;\n        /* istanbul ignore next should not happen */\n        default:\n            return [];\n    }\n}\n/** Note: May modify input array */\nfunction getFirstKeyStartProps(prev) {\n    if (prev.length === 0)\n        return [];\n    let i = prev.length;\n    loop: while (--i >= 0) {\n        switch (prev[i].type) {\n            case 'doc-start':\n            case 'explicit-key-ind':\n            case 'map-value-ind':\n            case 'seq-item-ind':\n            case 'newline':\n                break loop;\n        }\n    }\n    while (prev[++i]?.type === 'space') {\n        /* loop */\n    }\n    return prev.splice(i, prev.length);\n}\nfunction fixFlowSeqItems(fc) {\n    if (fc.start.type === 'flow-seq-start') {\n        for (const it of fc.items) {\n            if (it.sep &&\n                !it.value &&\n                !includesToken(it.start, 'explicit-key-ind') &&\n                !includesToken(it.sep, 'map-value-ind')) {\n                if (it.key)\n                    it.value = it.key;\n                delete it.key;\n                if (isFlowToken(it.value)) {\n                    if (it.value.end)\n                        Array.prototype.push.apply(it.value.end, it.sep);\n                    else\n                        it.value.end = it.sep;\n                }\n                else\n                    Array.prototype.push.apply(it.start, it.sep);\n                delete it.sep;\n            }\n        }\n    }\n}\n/**\n * A YAML concrete syntax tree (CST) parser\n *\n * ```ts\n * const src: string = ...\n * for (const token of new Parser().parse(src)) {\n *   // token: Token\n * }\n * ```\n *\n * To use the parser with a user-provided lexer:\n *\n * ```ts\n * function* parse(source: string, lexer: Lexer) {\n *   const parser = new Parser()\n *   for (const lexeme of lexer.lex(source))\n *     yield* parser.next(lexeme)\n *   yield* parser.end()\n * }\n *\n * const src: string = ...\n * const lexer = new Lexer()\n * for (const token of parse(src, lexer)) {\n *   // token: Token\n * }\n * ```\n */\nclass Parser {\n    /**\n     * @param onNewLine - If defined, called separately with the start position of\n     *   each new line (in `parse()`, including the start of input).\n     */\n    constructor(onNewLine) {\n        /** If true, space and sequence indicators count as indentation */\n        this.atNewLine = true;\n        /** If true, next token is a scalar value */\n        this.atScalar = false;\n        /** Current indentation level */\n        this.indent = 0;\n        /** Current offset since the start of parsing */\n        this.offset = 0;\n        /** On the same line with a block map key */\n        this.onKeyLine = false;\n        /** Top indicates the node that's currently being built */\n        this.stack = [];\n        /** The source of the current token, set in parse() */\n        this.source = '';\n        /** The type of the current token, set in parse() */\n        this.type = '';\n        // Must be defined after `next()`\n        this.lexer = new lexer.Lexer();\n        this.onNewLine = onNewLine;\n    }\n    /**\n     * Parse `source` as a YAML stream.\n     * If `incomplete`, a part of the last line may be left as a buffer for the next call.\n     *\n     * Errors are not thrown, but yielded as `{ type: 'error', message }` tokens.\n     *\n     * @returns A generator of tokens representing each directive, document, and other structure.\n     */\n    *parse(source, incomplete = false) {\n        if (this.onNewLine && this.offset === 0)\n            this.onNewLine(0);\n        for (const lexeme of this.lexer.lex(source, incomplete))\n            yield* this.next(lexeme);\n        if (!incomplete)\n            yield* this.end();\n    }\n    /**\n     * Advance the parser by the `source` of one lexical token.\n     */\n    *next(source) {\n        this.source = source;\n        if (node_process.env.LOG_TOKENS)\n            console.log('|', cst.prettyToken(source));\n        if (this.atScalar) {\n            this.atScalar = false;\n            yield* this.step();\n            this.offset += source.length;\n            return;\n        }\n        const type = cst.tokenType(source);\n        if (!type) {\n            const message = `Not a YAML token: ${source}`;\n            yield* this.pop({ type: 'error', offset: this.offset, message, source });\n            this.offset += source.length;\n        }\n        else if (type === 'scalar') {\n            this.atNewLine = false;\n            this.atScalar = true;\n            this.type = 'scalar';\n        }\n        else {\n            this.type = type;\n            yield* this.step();\n            switch (type) {\n                case 'newline':\n                    this.atNewLine = true;\n                    this.indent = 0;\n                    if (this.onNewLine)\n                        this.onNewLine(this.offset + source.length);\n                    break;\n                case 'space':\n                    if (this.atNewLine && source[0] === ' ')\n                        this.indent += source.length;\n                    break;\n                case 'explicit-key-ind':\n                case 'map-value-ind':\n                case 'seq-item-ind':\n                    if (this.atNewLine)\n                        this.indent += source.length;\n                    break;\n                case 'doc-mode':\n                case 'flow-error-end':\n                    return;\n                default:\n                    this.atNewLine = false;\n            }\n            this.offset += source.length;\n        }\n    }\n    /** Call at end of input to push out any remaining constructions */\n    *end() {\n        while (this.stack.length > 0)\n            yield* this.pop();\n    }\n    get sourceToken() {\n        const st = {\n            type: this.type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n        return st;\n    }\n    *step() {\n        const top = this.peek(1);\n        if (this.type === 'doc-end' && (!top || top.type !== 'doc-end')) {\n            while (this.stack.length > 0)\n                yield* this.pop();\n            this.stack.push({\n                type: 'doc-end',\n                offset: this.offset,\n                source: this.source\n            });\n            return;\n        }\n        if (!top)\n            return yield* this.stream();\n        switch (top.type) {\n            case 'document':\n                return yield* this.document(top);\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return yield* this.scalar(top);\n            case 'block-scalar':\n                return yield* this.blockScalar(top);\n            case 'block-map':\n                return yield* this.blockMap(top);\n            case 'block-seq':\n                return yield* this.blockSequence(top);\n            case 'flow-collection':\n                return yield* this.flowCollection(top);\n            case 'doc-end':\n                return yield* this.documentEnd(top);\n        }\n        /* istanbul ignore next should not happen */\n        yield* this.pop();\n    }\n    peek(n) {\n        return this.stack[this.stack.length - n];\n    }\n    *pop(error) {\n        const token = error ?? this.stack.pop();\n        /* istanbul ignore if should not happen */\n        if (!token) {\n            const message = 'Tried to pop an empty stack';\n            yield { type: 'error', offset: this.offset, source: '', message };\n        }\n        else if (this.stack.length === 0) {\n            yield token;\n        }\n        else {\n            const top = this.peek(1);\n            if (token.type === 'block-scalar') {\n                // Block scalars use their parent rather than header indent\n                token.indent = 'indent' in top ? top.indent : 0;\n            }\n            else if (token.type === 'flow-collection' && top.type === 'document') {\n                // Ignore all indent for top-level flow collections\n                token.indent = 0;\n            }\n            if (token.type === 'flow-collection')\n                fixFlowSeqItems(token);\n            switch (top.type) {\n                case 'document':\n                    top.value = token;\n                    break;\n                case 'block-scalar':\n                    top.props.push(token); // error\n                    break;\n                case 'block-map': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value) {\n                        top.items.push({ start: [], key: token, sep: [] });\n                        this.onKeyLine = true;\n                        return;\n                    }\n                    else if (it.sep) {\n                        it.value = token;\n                    }\n                    else {\n                        Object.assign(it, { key: token, sep: [] });\n                        this.onKeyLine = !it.explicitKey;\n                        return;\n                    }\n                    break;\n                }\n                case 'block-seq': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value)\n                        top.items.push({ start: [], value: token });\n                    else\n                        it.value = token;\n                    break;\n                }\n                case 'flow-collection': {\n                    const it = top.items[top.items.length - 1];\n                    if (!it || it.value)\n                        top.items.push({ start: [], key: token, sep: [] });\n                    else if (it.sep)\n                        it.value = token;\n                    else\n                        Object.assign(it, { key: token, sep: [] });\n                    return;\n                }\n                /* istanbul ignore next should not happen */\n                default:\n                    yield* this.pop();\n                    yield* this.pop(token);\n            }\n            if ((top.type === 'document' ||\n                top.type === 'block-map' ||\n                top.type === 'block-seq') &&\n                (token.type === 'block-map' || token.type === 'block-seq')) {\n                const last = token.items[token.items.length - 1];\n                if (last &&\n                    !last.sep &&\n                    !last.value &&\n                    last.start.length > 0 &&\n                    findNonEmptyIndex(last.start) === -1 &&\n                    (token.indent === 0 ||\n                        last.start.every(st => st.type !== 'comment' || st.indent < token.indent))) {\n                    if (top.type === 'document')\n                        top.end = last.start;\n                    else\n                        top.items.push({ start: last.start });\n                    token.items.splice(-1, 1);\n                }\n            }\n        }\n    }\n    *stream() {\n        switch (this.type) {\n            case 'directive-line':\n                yield { type: 'directive', offset: this.offset, source: this.source };\n                return;\n            case 'byte-order-mark':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                yield this.sourceToken;\n                return;\n            case 'doc-mode':\n            case 'doc-start': {\n                const doc = {\n                    type: 'document',\n                    offset: this.offset,\n                    start: []\n                };\n                if (this.type === 'doc-start')\n                    doc.start.push(this.sourceToken);\n                this.stack.push(doc);\n                return;\n            }\n        }\n        yield {\n            type: 'error',\n            offset: this.offset,\n            message: `Unexpected ${this.type} token in YAML stream`,\n            source: this.source\n        };\n    }\n    *document(doc) {\n        if (doc.value)\n            return yield* this.lineEnd(doc);\n        switch (this.type) {\n            case 'doc-start': {\n                if (findNonEmptyIndex(doc.start) !== -1) {\n                    yield* this.pop();\n                    yield* this.step();\n                }\n                else\n                    doc.start.push(this.sourceToken);\n                return;\n            }\n            case 'anchor':\n            case 'tag':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                doc.start.push(this.sourceToken);\n                return;\n        }\n        const bv = this.startBlockValue(doc);\n        if (bv)\n            this.stack.push(bv);\n        else {\n            yield {\n                type: 'error',\n                offset: this.offset,\n                message: `Unexpected ${this.type} token in YAML document`,\n                source: this.source\n            };\n        }\n    }\n    *scalar(scalar) {\n        if (this.type === 'map-value-ind') {\n            const prev = getPrevProps(this.peek(2));\n            const start = getFirstKeyStartProps(prev);\n            let sep;\n            if (scalar.end) {\n                sep = scalar.end;\n                sep.push(this.sourceToken);\n                delete scalar.end;\n            }\n            else\n                sep = [this.sourceToken];\n            const map = {\n                type: 'block-map',\n                offset: scalar.offset,\n                indent: scalar.indent,\n                items: [{ start, key: scalar, sep }]\n            };\n            this.onKeyLine = true;\n            this.stack[this.stack.length - 1] = map;\n        }\n        else\n            yield* this.lineEnd(scalar);\n    }\n    *blockScalar(scalar) {\n        switch (this.type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                scalar.props.push(this.sourceToken);\n                return;\n            case 'scalar':\n                scalar.source = this.source;\n                // block-scalar source includes trailing newline\n                this.atNewLine = true;\n                this.indent = 0;\n                if (this.onNewLine) {\n                    let nl = this.source.indexOf('\\n') + 1;\n                    while (nl !== 0) {\n                        this.onNewLine(this.offset + nl);\n                        nl = this.source.indexOf('\\n', nl) + 1;\n                    }\n                }\n                yield* this.pop();\n                break;\n            /* istanbul ignore next should not happen */\n            default:\n                yield* this.pop();\n                yield* this.step();\n        }\n    }\n    *blockMap(map) {\n        const it = map.items[map.items.length - 1];\n        // it.sep is true-ish if pair already has key or : separator\n        switch (this.type) {\n            case 'newline':\n                this.onKeyLine = false;\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value) {\n                    map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    if (this.atIndentedComment(it.start, map.indent)) {\n                        const prev = map.items[map.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            map.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n        }\n        if (this.indent >= map.indent) {\n            const atMapIndent = !this.onKeyLine && this.indent === map.indent;\n            const atNextItem = atMapIndent &&\n                (it.sep || it.explicitKey) &&\n                this.type !== 'seq-item-ind';\n            // For empty nodes, assign newline-separated not indented empty tokens to following node\n            let start = [];\n            if (atNextItem && it.sep && !it.value) {\n                const nl = [];\n                for (let i = 0; i < it.sep.length; ++i) {\n                    const st = it.sep[i];\n                    switch (st.type) {\n                        case 'newline':\n                            nl.push(i);\n                            break;\n                        case 'space':\n                            break;\n                        case 'comment':\n                            if (st.indent > map.indent)\n                                nl.length = 0;\n                            break;\n                        default:\n                            nl.length = 0;\n                    }\n                }\n                if (nl.length >= 2)\n                    start = it.sep.splice(nl[1]);\n            }\n            switch (this.type) {\n                case 'anchor':\n                case 'tag':\n                    if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        it.sep.push(this.sourceToken);\n                    }\n                    else {\n                        it.start.push(this.sourceToken);\n                    }\n                    return;\n                case 'explicit-key-ind':\n                    if (!it.sep && !it.explicitKey) {\n                        it.start.push(this.sourceToken);\n                        it.explicitKey = true;\n                    }\n                    else if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start, explicitKey: true });\n                    }\n                    else {\n                        this.stack.push({\n                            type: 'block-map',\n                            offset: this.offset,\n                            indent: this.indent,\n                            items: [{ start: [this.sourceToken], explicitKey: true }]\n                        });\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'map-value-ind':\n                    if (it.explicitKey) {\n                        if (!it.sep) {\n                            if (includesToken(it.start, 'newline')) {\n                                Object.assign(it, { key: null, sep: [this.sourceToken] });\n                            }\n                            else {\n                                const start = getFirstKeyStartProps(it.start);\n                                this.stack.push({\n                                    type: 'block-map',\n                                    offset: this.offset,\n                                    indent: this.indent,\n                                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                                });\n                            }\n                        }\n                        else if (it.value) {\n                            map.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else if (isFlowToken(it.key) &&\n                            !includesToken(it.sep, 'newline')) {\n                            const start = getFirstKeyStartProps(it.start);\n                            const key = it.key;\n                            const sep = it.sep;\n                            sep.push(this.sourceToken);\n                            // @ts-expect-error type guard is wrong here\n                            delete it.key;\n                            // @ts-expect-error type guard is wrong here\n                            delete it.sep;\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key, sep }]\n                            });\n                        }\n                        else if (start.length > 0) {\n                            // Not actually at next item\n                            it.sep = it.sep.concat(start, this.sourceToken);\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    else {\n                        if (!it.sep) {\n                            Object.assign(it, { key: null, sep: [this.sourceToken] });\n                        }\n                        else if (it.value || atNextItem) {\n                            map.items.push({ start, key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start: [], key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (atNextItem || it.value) {\n                        map.items.push({ start, key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        this.stack.push(fs);\n                    }\n                    else {\n                        Object.assign(it, { key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    return;\n                }\n                default: {\n                    const bv = this.startBlockValue(map);\n                    if (bv) {\n                        if (bv.type === 'block-seq') {\n                            if (!it.explicitKey &&\n                                it.sep &&\n                                !includesToken(it.sep, 'newline')) {\n                                yield* this.pop({\n                                    type: 'error',\n                                    offset: this.offset,\n                                    message: 'Unexpected block-seq-ind on same line with key',\n                                    source: this.source\n                                });\n                                return;\n                            }\n                        }\n                        else if (atMapIndent) {\n                            map.items.push({ start });\n                        }\n                        this.stack.push(bv);\n                        return;\n                    }\n                }\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *blockSequence(seq) {\n        const it = seq.items[seq.items.length - 1];\n        switch (this.type) {\n            case 'newline':\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        seq.items.push({ start: [this.sourceToken] });\n                }\n                else\n                    it.start.push(this.sourceToken);\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value)\n                    seq.items.push({ start: [this.sourceToken] });\n                else {\n                    if (this.atIndentedComment(it.start, seq.indent)) {\n                        const prev = seq.items[seq.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            seq.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'anchor':\n            case 'tag':\n                if (it.value || this.indent <= seq.indent)\n                    break;\n                it.start.push(this.sourceToken);\n                return;\n            case 'seq-item-ind':\n                if (this.indent !== seq.indent)\n                    break;\n                if (it.value || includesToken(it.start, 'seq-item-ind'))\n                    seq.items.push({ start: [this.sourceToken] });\n                else\n                    it.start.push(this.sourceToken);\n                return;\n        }\n        if (this.indent > seq.indent) {\n            const bv = this.startBlockValue(seq);\n            if (bv) {\n                this.stack.push(bv);\n                return;\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *flowCollection(fc) {\n        const it = fc.items[fc.items.length - 1];\n        if (this.type === 'flow-error-end') {\n            let top;\n            do {\n                yield* this.pop();\n                top = this.peek(1);\n            } while (top && top.type === 'flow-collection');\n        }\n        else if (fc.end.length === 0) {\n            switch (this.type) {\n                case 'comma':\n                case 'explicit-key-ind':\n                    if (!it || it.sep)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'map-value-ind':\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        Object.assign(it, { key: null, sep: [this.sourceToken] });\n                    return;\n                case 'space':\n                case 'comment':\n                case 'newline':\n                case 'anchor':\n                case 'tag':\n                    if (!it || it.value)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: fs, sep: [] });\n                    else if (it.sep)\n                        this.stack.push(fs);\n                    else\n                        Object.assign(it, { key: fs, sep: [] });\n                    return;\n                }\n                case 'flow-map-end':\n                case 'flow-seq-end':\n                    fc.end.push(this.sourceToken);\n                    return;\n            }\n            const bv = this.startBlockValue(fc);\n            /* istanbul ignore else should not happen */\n            if (bv)\n                this.stack.push(bv);\n            else {\n                yield* this.pop();\n                yield* this.step();\n            }\n        }\n        else {\n            const parent = this.peek(2);\n            if (parent.type === 'block-map' &&\n                ((this.type === 'map-value-ind' && parent.indent === fc.indent) ||\n                    (this.type === 'newline' &&\n                        !parent.items[parent.items.length - 1].sep))) {\n                yield* this.pop();\n                yield* this.step();\n            }\n            else if (this.type === 'map-value-ind' &&\n                parent.type !== 'flow-collection') {\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                fixFlowSeqItems(fc);\n                const sep = fc.end.splice(1, fc.end.length);\n                sep.push(this.sourceToken);\n                const map = {\n                    type: 'block-map',\n                    offset: fc.offset,\n                    indent: fc.indent,\n                    items: [{ start, key: fc, sep }]\n                };\n                this.onKeyLine = true;\n                this.stack[this.stack.length - 1] = map;\n            }\n            else {\n                yield* this.lineEnd(fc);\n            }\n        }\n    }\n    flowScalar(type) {\n        if (this.onNewLine) {\n            let nl = this.source.indexOf('\\n') + 1;\n            while (nl !== 0) {\n                this.onNewLine(this.offset + nl);\n                nl = this.source.indexOf('\\n', nl) + 1;\n            }\n        }\n        return {\n            type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n    }\n    startBlockValue(parent) {\n        switch (this.type) {\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return this.flowScalar(this.type);\n            case 'block-scalar-header':\n                return {\n                    type: 'block-scalar',\n                    offset: this.offset,\n                    indent: this.indent,\n                    props: [this.sourceToken],\n                    source: ''\n                };\n            case 'flow-map-start':\n            case 'flow-seq-start':\n                return {\n                    type: 'flow-collection',\n                    offset: this.offset,\n                    indent: this.indent,\n                    start: this.sourceToken,\n                    items: [],\n                    end: []\n                };\n            case 'seq-item-ind':\n                return {\n                    type: 'block-seq',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start: [this.sourceToken] }]\n                };\n            case 'explicit-key-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                start.push(this.sourceToken);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, explicitKey: true }]\n                };\n            }\n            case 'map-value-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                };\n            }\n        }\n        return null;\n    }\n    atIndentedComment(start, indent) {\n        if (this.type !== 'comment')\n            return false;\n        if (this.indent <= indent)\n            return false;\n        return start.every(st => st.type === 'newline' || st.type === 'space');\n    }\n    *documentEnd(docEnd) {\n        if (this.type !== 'doc-mode') {\n            if (docEnd.end)\n                docEnd.end.push(this.sourceToken);\n            else\n                docEnd.end = [this.sourceToken];\n            if (this.type === 'newline')\n                yield* this.pop();\n        }\n    }\n    *lineEnd(token) {\n        switch (this.type) {\n            case 'comma':\n            case 'doc-start':\n            case 'doc-end':\n            case 'flow-seq-end':\n            case 'flow-map-end':\n            case 'map-value-ind':\n                yield* this.pop();\n                yield* this.step();\n                break;\n            case 'newline':\n                this.onKeyLine = false;\n            // fallthrough\n            case 'space':\n            case 'comment':\n            default:\n                // all other values are errors\n                if (token.end)\n                    token.end.push(this.sourceToken);\n                else\n                    token.end = [this.sourceToken];\n                if (this.type === 'newline')\n                    yield* this.pop();\n        }\n    }\n}\n\nexports.Parser = Parser;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,cAAc,IAAI,EAAE,IAAI;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,MACjB,OAAO;IACf,OAAO;AACX;AACA,SAAS,kBAAkB,IAAI;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAClC,OAAQ,IAAI,CAAC,EAAE,CAAC,IAAI;YAChB,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ;gBACI,OAAO;QACf;IACJ;IACA,OAAO,CAAC;AACZ;AACA,SAAS,YAAY,KAAK;IACtB,OAAQ,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,SAAS,aAAa,MAAM;IACxB,OAAQ,OAAO,IAAI;QACf,KAAK;YACD,OAAO,OAAO,KAAK;QACvB,KAAK;YAAa;gBACd,MAAM,KAAK,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,EAAE;gBAChD,OAAO,GAAG,GAAG,IAAI,GAAG,KAAK;YAC7B;QACA,KAAK;YACD,OAAO,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK;QACtD,0CAA0C,GAC1C;YACI,OAAO,EAAE;IACjB;AACJ;AACA,iCAAiC,GACjC,SAAS,sBAAsB,IAAI;IAC/B,IAAI,KAAK,MAAM,KAAK,GAChB,OAAO,EAAE;IACb,IAAI,IAAI,KAAK,MAAM;IACnB,MAAM,MAAO,EAAE,KAAK,EAAG;QACnB,OAAQ,IAAI,CAAC,EAAE,CAAC,IAAI;YAChB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM;QACd;IACJ;IACA,MAAO,IAAI,CAAC,EAAE,EAAE,EAAE,SAAS,QAAS;IAChC,QAAQ,GACZ;IACA,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM;AACrC;AACA,SAAS,gBAAgB,EAAE;IACvB,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,kBAAkB;QACpC,KAAK,MAAM,MAAM,GAAG,KAAK,CAAE;YACvB,IAAI,GAAG,GAAG,IACN,CAAC,GAAG,KAAK,IACT,CAAC,cAAc,GAAG,KAAK,EAAE,uBACzB,CAAC,cAAc,GAAG,GAAG,EAAE,kBAAkB;gBACzC,IAAI,GAAG,GAAG,EACN,GAAG,KAAK,GAAG,GAAG,GAAG;gBACrB,OAAO,GAAG,GAAG;gBACb,IAAI,YAAY,GAAG,KAAK,GAAG;oBACvB,IAAI,GAAG,KAAK,CAAC,GAAG,EACZ,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG;yBAE/C,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;gBAC7B,OAEI,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,GAAG,GAAG;gBAC/C,OAAO,GAAG,GAAG;YACjB;QACJ;IACJ;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,MAAM;IACF;;;KAGC,GACD,YAAY,SAAS,CAAE;QACnB,gEAAgE,GAChE,IAAI,CAAC,SAAS,GAAG;QACjB,0CAA0C,GAC1C,IAAI,CAAC,QAAQ,GAAG;QAChB,8BAA8B,GAC9B,IAAI,CAAC,MAAM,GAAG;QACd,8CAA8C,GAC9C,IAAI,CAAC,MAAM,GAAG;QACd,0CAA0C,GAC1C,IAAI,CAAC,SAAS,GAAG;QACjB,wDAAwD,GACxD,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,oDAAoD,GACpD,IAAI,CAAC,MAAM,GAAG;QACd,kDAAkD,GAClD,IAAI,CAAC,IAAI,GAAG;QACZ,iCAAiC;QACjC,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,KAAK;QAC5B,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;KAOC,GACD,CAAC,MAAM,MAAM,EAAE,aAAa,KAAK,EAAE;QAC/B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,GAClC,IAAI,CAAC,SAAS,CAAC;QACnB,KAAK,MAAM,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,YACxC,OAAO,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,YACD,OAAO,IAAI,CAAC,GAAG;IACvB;IACA;;KAEC,GACD,CAAC,KAAK,MAAM,EAAE;QACV,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,aAAa,GAAG,CAAC,UAAU,EAC3B,QAAQ,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG;YAChB,OAAO,IAAI,CAAC,IAAI;YAChB,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;YAC5B;QACJ;QACA,MAAM,OAAO,IAAI,SAAS,CAAC;QAC3B,IAAI,CAAC,MAAM;YACP,MAAM,UAAU,CAAC,kBAAkB,EAAE,QAAQ;YAC7C,OAAO,IAAI,CAAC,GAAG,CAAC;gBAAE,MAAM;gBAAS,QAAQ,IAAI,CAAC,MAAM;gBAAE;gBAAS;YAAO;YACtE,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;QAChC,OACK,IAAI,SAAS,UAAU;YACxB,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,IAAI,GAAG;QAChB,OACK;YACD,IAAI,CAAC,IAAI,GAAG;YACZ,OAAO,IAAI,CAAC,IAAI;YAChB,OAAQ;gBACJ,KAAK;oBACD,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;oBAC9C;gBACJ,KAAK;oBACD,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,KAChC,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;oBAChC;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,IAAI,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;oBAChC;gBACJ,KAAK;gBACL,KAAK;oBACD;gBACJ;oBACI,IAAI,CAAC,SAAS,GAAG;YACzB;YACA,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;QAChC;IACJ;IACA,iEAAiE,GACjE,CAAC,MAAM;QACH,MAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EACvB,OAAO,IAAI,CAAC,GAAG;IACvB;IACA,IAAI,cAAc;QACd,MAAM,KAAK;YACP,MAAM,IAAI,CAAC,IAAI;YACf,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,MAAM;QACvB;QACA,OAAO;IACX;IACA,CAAC,OAAO;QACJ,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,OAAO,IAAI,IAAI,KAAK,SAAS,GAAG;YAC7D,MAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EACvB,OAAO,IAAI,CAAC,GAAG;YACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACZ,MAAM;gBACN,QAAQ,IAAI,CAAC,MAAM;gBACnB,QAAQ,IAAI,CAAC,MAAM;YACvB;YACA;QACJ;QACA,IAAI,CAAC,KACD,OAAO,OAAO,IAAI,CAAC,MAAM;QAC7B,OAAQ,IAAI,IAAI;YACZ,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,QAAQ,CAAC;YAChC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC;YAC9B,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC;YACnC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,QAAQ,CAAC;YAChC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,aAAa,CAAC;YACrC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC;YACtC,KAAK;gBACD,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC;QACvC;QACA,0CAA0C,GAC1C,OAAO,IAAI,CAAC,GAAG;IACnB;IACA,KAAK,CAAC,EAAE;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC5C;IACA,CAAC,IAAI,KAAK,EAAE;QACR,MAAM,QAAQ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG;QACrC,wCAAwC,GACxC,IAAI,CAAC,OAAO;YACR,MAAM,UAAU;YAChB,MAAM;gBAAE,MAAM;gBAAS,QAAQ,IAAI,CAAC,MAAM;gBAAE,QAAQ;gBAAI;YAAQ;QACpE,OACK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAC9B,MAAM;QACV,OACK;YACD,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC;YACtB,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBAC/B,2DAA2D;gBAC3D,MAAM,MAAM,GAAG,YAAY,MAAM,IAAI,MAAM,GAAG;YAClD,OACK,IAAI,MAAM,IAAI,KAAK,qBAAqB,IAAI,IAAI,KAAK,YAAY;gBAClE,mDAAmD;gBACnD,MAAM,MAAM,GAAG;YACnB;YACA,IAAI,MAAM,IAAI,KAAK,mBACf,gBAAgB;YACpB,OAAQ,IAAI,IAAI;gBACZ,KAAK;oBACD,IAAI,KAAK,GAAG;oBACZ;gBACJ,KAAK;oBACD,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,QAAQ;oBAC/B;gBACJ,KAAK;oBAAa;wBACd,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;wBAC1C,IAAI,GAAG,KAAK,EAAE;4BACV,IAAI,KAAK,CAAC,IAAI,CAAC;gCAAE,OAAO,EAAE;gCAAE,KAAK;gCAAO,KAAK,EAAE;4BAAC;4BAChD,IAAI,CAAC,SAAS,GAAG;4BACjB;wBACJ,OACK,IAAI,GAAG,GAAG,EAAE;4BACb,GAAG,KAAK,GAAG;wBACf,OACK;4BACD,OAAO,MAAM,CAAC,IAAI;gCAAE,KAAK;gCAAO,KAAK,EAAE;4BAAC;4BACxC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,WAAW;4BAChC;wBACJ;wBACA;oBACJ;gBACA,KAAK;oBAAa;wBACd,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;wBAC1C,IAAI,GAAG,KAAK,EACR,IAAI,KAAK,CAAC,IAAI,CAAC;4BAAE,OAAO,EAAE;4BAAE,OAAO;wBAAM;6BAEzC,GAAG,KAAK,GAAG;wBACf;oBACJ;gBACA,KAAK;oBAAmB;wBACpB,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;wBAC1C,IAAI,CAAC,MAAM,GAAG,KAAK,EACf,IAAI,KAAK,CAAC,IAAI,CAAC;4BAAE,OAAO,EAAE;4BAAE,KAAK;4BAAO,KAAK,EAAE;wBAAC;6BAC/C,IAAI,GAAG,GAAG,EACX,GAAG,KAAK,GAAG;6BAEX,OAAO,MAAM,CAAC,IAAI;4BAAE,KAAK;4BAAO,KAAK,EAAE;wBAAC;wBAC5C;oBACJ;gBACA,0CAA0C,GAC1C;oBACI,OAAO,IAAI,CAAC,GAAG;oBACf,OAAO,IAAI,CAAC,GAAG,CAAC;YACxB;YACA,IAAI,CAAC,IAAI,IAAI,KAAK,cACd,IAAI,IAAI,KAAK,eACb,IAAI,IAAI,KAAK,WAAW,KACxB,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,WAAW,GAAG;gBAC5D,MAAM,OAAO,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE;gBAChD,IAAI,QACA,CAAC,KAAK,GAAG,IACT,CAAC,KAAK,KAAK,IACX,KAAK,KAAK,CAAC,MAAM,GAAG,KACpB,kBAAkB,KAAK,KAAK,MAAM,CAAC,KACnC,CAAC,MAAM,MAAM,KAAK,KACd,KAAK,KAAK,CAAC,KAAK,CAAC,CAAA,KAAM,GAAG,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG;oBAChF,IAAI,IAAI,IAAI,KAAK,YACb,IAAI,GAAG,GAAG,KAAK,KAAK;yBAEpB,IAAI,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO,KAAK,KAAK;oBAAC;oBACvC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG;gBAC3B;YACJ;QACJ;IACJ;IACA,CAAC,SAAS;QACN,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;gBACD,MAAM;oBAAE,MAAM;oBAAa,QAAQ,IAAI,CAAC,MAAM;oBAAE,QAAQ,IAAI,CAAC,MAAM;gBAAC;gBACpE;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,CAAC,WAAW;gBACtB;YACJ,KAAK;YACL,KAAK;gBAAa;oBACd,MAAM,MAAM;wBACR,MAAM;wBACN,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO,EAAE;oBACb;oBACA,IAAI,IAAI,CAAC,IAAI,KAAK,aACd,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBACnC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAChB;gBACJ;QACJ;QACA,MAAM;YACF,MAAM;YACN,QAAQ,IAAI,CAAC,MAAM;YACnB,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;YACvD,QAAQ,IAAI,CAAC,MAAM;QACvB;IACJ;IACA,CAAC,SAAS,GAAG,EAAE;QACX,IAAI,IAAI,KAAK,EACT,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC;QAC/B,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;gBAAa;oBACd,IAAI,kBAAkB,IAAI,KAAK,MAAM,CAAC,GAAG;wBACrC,OAAO,IAAI,CAAC,GAAG;wBACf,OAAO,IAAI,CAAC,IAAI;oBACpB,OAEI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBACnC;gBACJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAC/B;QACR;QACA,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC;QAChC,IAAI,IACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;aACf;YACD,MAAM;gBACF,MAAM;gBACN,QAAQ,IAAI,CAAC,MAAM;gBACnB,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;gBACzD,QAAQ,IAAI,CAAC,MAAM;YACvB;QACJ;IACJ;IACA,CAAC,OAAO,MAAM,EAAE;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB;YAC/B,MAAM,OAAO,aAAa,IAAI,CAAC,IAAI,CAAC;YACpC,MAAM,QAAQ,sBAAsB;YACpC,IAAI;YACJ,IAAI,OAAO,GAAG,EAAE;gBACZ,MAAM,OAAO,GAAG;gBAChB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;gBACzB,OAAO,OAAO,GAAG;YACrB,OAEI,MAAM;gBAAC,IAAI,CAAC,WAAW;aAAC;YAC5B,MAAM,MAAM;gBACR,MAAM;gBACN,QAAQ,OAAO,MAAM;gBACrB,QAAQ,OAAO,MAAM;gBACrB,OAAO;oBAAC;wBAAE;wBAAO,KAAK;wBAAQ;oBAAI;iBAAE;YACxC;YACA,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;QACxC,OAEI,OAAO,IAAI,CAAC,OAAO,CAAC;IAC5B;IACA,CAAC,YAAY,MAAM,EAAE;QACjB,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;YACJ,KAAK;gBACD,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM;gBAC3B,gDAAgD;gBAChD,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;oBACrC,MAAO,OAAO,EAAG;wBACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;wBAC7B,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,MAAM;oBACzC;gBACJ;gBACA,OAAO,IAAI,CAAC,GAAG;gBACf;YACJ,0CAA0C,GAC1C;gBACI,OAAO,IAAI,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,IAAI;QACxB;IACJ;IACA,CAAC,SAAS,GAAG,EAAE;QACX,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;QAC1C,4DAA4D;QAC5D,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;gBACD,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,GAAG,KAAK,EAAE;oBACV,MAAM,MAAM,SAAS,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG;oBAC/C,MAAM,OAAO,MAAM,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG;oBACxD,IAAI,MAAM,SAAS,WACf,KAAK,KAAK,IAAI,CAAC,WAAW;yBAE1B,IAAI,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;gBACnD,OACK,IAAI,GAAG,GAAG,EAAE;oBACb,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAChC,OACK;oBACD,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;gBACA;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,GAAG,KAAK,EAAE;oBACV,IAAI,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;gBAC/C,OACK,IAAI,GAAG,GAAG,EAAE;oBACb,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAChC,OACK;oBACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,KAAK,EAAE,IAAI,MAAM,GAAG;wBAC9C,MAAM,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;wBAC5C,MAAM,MAAM,MAAM,OAAO;wBACzB,IAAI,MAAM,OAAO,CAAC,MAAM;4BACpB,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;4BACxC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;4BACzB,IAAI,KAAK,CAAC,GAAG;4BACb;wBACJ;oBACJ;oBACA,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;gBACA;QACR;QACA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,EAAE;YAC3B,MAAM,cAAc,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM;YACjE,MAAM,aAAa,eACf,CAAC,GAAG,GAAG,IAAI,GAAG,WAAW,KACzB,IAAI,CAAC,IAAI,KAAK;YAClB,wFAAwF;YACxF,IAAI,QAAQ,EAAE;YACd,IAAI,cAAc,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE;gBACnC,MAAM,KAAK,EAAE;gBACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,EAAG;oBACpC,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE;oBACpB,OAAQ,GAAG,IAAI;wBACX,KAAK;4BACD,GAAG,IAAI,CAAC;4BACR;wBACJ,KAAK;4BACD;wBACJ,KAAK;4BACD,IAAI,GAAG,MAAM,GAAG,IAAI,MAAM,EACtB,GAAG,MAAM,GAAG;4BAChB;wBACJ;4BACI,GAAG,MAAM,GAAG;oBACpB;gBACJ;gBACA,IAAI,GAAG,MAAM,IAAI,GACb,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACnC;YACA,OAAQ,IAAI,CAAC,IAAI;gBACb,KAAK;gBACL,KAAK;oBACD,IAAI,cAAc,GAAG,KAAK,EAAE;wBACxB,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW;wBAC3B,IAAI,KAAK,CAAC,IAAI,CAAC;4BAAE;wBAAM;wBACvB,IAAI,CAAC,SAAS,GAAG;oBACrB,OACK,IAAI,GAAG,GAAG,EAAE;wBACb,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBAChC,OACK;wBACD,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBAClC;oBACA;gBACJ,KAAK;oBACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,WAAW,EAAE;wBAC5B,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;wBAC9B,GAAG,WAAW,GAAG;oBACrB,OACK,IAAI,cAAc,GAAG,KAAK,EAAE;wBAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW;wBAC3B,IAAI,KAAK,CAAC,IAAI,CAAC;4BAAE;4BAAO,aAAa;wBAAK;oBAC9C,OACK;wBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BACZ,MAAM;4BACN,QAAQ,IAAI,CAAC,MAAM;4BACnB,QAAQ,IAAI,CAAC,MAAM;4BACnB,OAAO;gCAAC;oCAAE,OAAO;wCAAC,IAAI,CAAC,WAAW;qCAAC;oCAAE,aAAa;gCAAK;6BAAE;wBAC7D;oBACJ;oBACA,IAAI,CAAC,SAAS,GAAG;oBACjB;gBACJ,KAAK;oBACD,IAAI,GAAG,WAAW,EAAE;wBAChB,IAAI,CAAC,GAAG,GAAG,EAAE;4BACT,IAAI,cAAc,GAAG,KAAK,EAAE,YAAY;gCACpC,OAAO,MAAM,CAAC,IAAI;oCAAE,KAAK;oCAAM,KAAK;wCAAC,IAAI,CAAC,WAAW;qCAAC;gCAAC;4BAC3D,OACK;gCACD,MAAM,QAAQ,sBAAsB,GAAG,KAAK;gCAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oCACZ,MAAM;oCACN,QAAQ,IAAI,CAAC,MAAM;oCACnB,QAAQ,IAAI,CAAC,MAAM;oCACnB,OAAO;wCAAC;4CAAE;4CAAO,KAAK;4CAAM,KAAK;gDAAC,IAAI,CAAC,WAAW;6CAAC;wCAAC;qCAAE;gCAC1D;4BACJ;wBACJ,OACK,IAAI,GAAG,KAAK,EAAE;4BACf,IAAI,KAAK,CAAC,IAAI,CAAC;gCAAE,OAAO,EAAE;gCAAE,KAAK;gCAAM,KAAK;oCAAC,IAAI,CAAC,WAAW;iCAAC;4BAAC;wBACnE,OACK,IAAI,cAAc,GAAG,GAAG,EAAE,kBAAkB;4BAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gCACZ,MAAM;gCACN,QAAQ,IAAI,CAAC,MAAM;gCACnB,QAAQ,IAAI,CAAC,MAAM;gCACnB,OAAO;oCAAC;wCAAE;wCAAO,KAAK;wCAAM,KAAK;4CAAC,IAAI,CAAC,WAAW;yCAAC;oCAAC;iCAAE;4BAC1D;wBACJ,OACK,IAAI,YAAY,GAAG,GAAG,KACvB,CAAC,cAAc,GAAG,GAAG,EAAE,YAAY;4BACnC,MAAM,QAAQ,sBAAsB,GAAG,KAAK;4BAC5C,MAAM,MAAM,GAAG,GAAG;4BAClB,MAAM,MAAM,GAAG,GAAG;4BAClB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;4BACzB,4CAA4C;4BAC5C,OAAO,GAAG,GAAG;4BACb,4CAA4C;4BAC5C,OAAO,GAAG,GAAG;4BACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gCACZ,MAAM;gCACN,QAAQ,IAAI,CAAC,MAAM;gCACnB,QAAQ,IAAI,CAAC,MAAM;gCACnB,OAAO;oCAAC;wCAAE;wCAAO;wCAAK;oCAAI;iCAAE;4BAChC;wBACJ,OACK,IAAI,MAAM,MAAM,GAAG,GAAG;4BACvB,4BAA4B;4BAC5B,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,WAAW;wBAClD,OACK;4BACD,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;wBAChC;oBACJ,OACK;wBACD,IAAI,CAAC,GAAG,GAAG,EAAE;4BACT,OAAO,MAAM,CAAC,IAAI;gCAAE,KAAK;gCAAM,KAAK;oCAAC,IAAI,CAAC,WAAW;iCAAC;4BAAC;wBAC3D,OACK,IAAI,GAAG,KAAK,IAAI,YAAY;4BAC7B,IAAI,KAAK,CAAC,IAAI,CAAC;gCAAE;gCAAO,KAAK;gCAAM,KAAK;oCAAC,IAAI,CAAC,WAAW;iCAAC;4BAAC;wBAC/D,OACK,IAAI,cAAc,GAAG,GAAG,EAAE,kBAAkB;4BAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gCACZ,MAAM;gCACN,QAAQ,IAAI,CAAC,MAAM;gCACnB,QAAQ,IAAI,CAAC,MAAM;gCACnB,OAAO;oCAAC;wCAAE,OAAO,EAAE;wCAAE,KAAK;wCAAM,KAAK;4CAAC,IAAI,CAAC,WAAW;yCAAC;oCAAC;iCAAE;4BAC9D;wBACJ,OACK;4BACD,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;wBAChC;oBACJ;oBACA,IAAI,CAAC,SAAS,GAAG;oBACjB;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAwB;wBACzB,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;wBACpC,IAAI,cAAc,GAAG,KAAK,EAAE;4BACxB,IAAI,KAAK,CAAC,IAAI,CAAC;gCAAE;gCAAO,KAAK;gCAAI,KAAK,EAAE;4BAAC;4BACzC,IAAI,CAAC,SAAS,GAAG;wBACrB,OACK,IAAI,GAAG,GAAG,EAAE;4BACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;wBACpB,OACK;4BACD,OAAO,MAAM,CAAC,IAAI;gCAAE,KAAK;gCAAI,KAAK,EAAE;4BAAC;4BACrC,IAAI,CAAC,SAAS,GAAG;wBACrB;wBACA;oBACJ;gBACA;oBAAS;wBACL,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC;wBAChC,IAAI,IAAI;4BACJ,IAAI,GAAG,IAAI,KAAK,aAAa;gCACzB,IAAI,CAAC,GAAG,WAAW,IACf,GAAG,GAAG,IACN,CAAC,cAAc,GAAG,GAAG,EAAE,YAAY;oCACnC,OAAO,IAAI,CAAC,GAAG,CAAC;wCACZ,MAAM;wCACN,QAAQ,IAAI,CAAC,MAAM;wCACnB,SAAS;wCACT,QAAQ,IAAI,CAAC,MAAM;oCACvB;oCACA;gCACJ;4BACJ,OACK,IAAI,aAAa;gCAClB,IAAI,KAAK,CAAC,IAAI,CAAC;oCAAE;gCAAM;4BAC3B;4BACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BAChB;wBACJ;oBACJ;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,GAAG;QACf,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,CAAC,cAAc,GAAG,EAAE;QAChB,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;QAC1C,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;gBACD,IAAI,GAAG,KAAK,EAAE;oBACV,MAAM,MAAM,SAAS,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG;oBAC/C,MAAM,OAAO,MAAM,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG;oBACxD,IAAI,MAAM,SAAS,WACf,KAAK,KAAK,IAAI,CAAC,WAAW;yBAE1B,IAAI,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;gBACnD,OAEI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,GAAG,KAAK,EACR,IAAI,KAAK,CAAC,IAAI,CAAC;oBAAE,OAAO;wBAAC,IAAI,CAAC,WAAW;qBAAC;gBAAC;qBAC1C;oBACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,KAAK,EAAE,IAAI,MAAM,GAAG;wBAC9C,MAAM,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE;wBAC5C,MAAM,MAAM,MAAM,OAAO;wBACzB,IAAI,MAAM,OAAO,CAAC,MAAM;4BACpB,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;4BACxC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;4BACzB,IAAI,KAAK,CAAC,GAAG;4BACb;wBACJ;oBACJ;oBACA,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;gBACA;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,EACrC;gBACJ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAC9B;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,EAC1B;gBACJ,IAAI,GAAG,KAAK,IAAI,cAAc,GAAG,KAAK,EAAE,iBACpC,IAAI,KAAK,CAAC,IAAI,CAAC;oBAAE,OAAO;wBAAC,IAAI,CAAC,WAAW;qBAAC;gBAAC;qBAE3C,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC;QACR;QACA,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE;YAC1B,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC;YAChC,IAAI,IAAI;gBACJ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAChB;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,GAAG;QACf,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,CAAC,eAAe,EAAE,EAAE;QAChB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,EAAE;QACxC,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB;YAChC,IAAI;YACJ,GAAG;gBACC,OAAO,IAAI,CAAC,GAAG;gBACf,MAAM,IAAI,CAAC,IAAI,CAAC;YACpB,QAAS,OAAO,IAAI,IAAI,KAAK,kBAAmB;QACpD,OACK,IAAI,GAAG,GAAG,CAAC,MAAM,KAAK,GAAG;YAC1B,OAAQ,IAAI,CAAC,IAAI;gBACb,KAAK;gBACL,KAAK;oBACD,IAAI,CAAC,MAAM,GAAG,GAAG,EACb,GAAG,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;yBAE1C,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBAClC;gBACJ,KAAK;oBACD,IAAI,CAAC,MAAM,GAAG,KAAK,EACf,GAAG,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO,EAAE;wBAAE,KAAK;wBAAM,KAAK;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;yBAC7D,IAAI,GAAG,GAAG,EACX,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;yBAE5B,OAAO,MAAM,CAAC,IAAI;wBAAE,KAAK;wBAAM,KAAK;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;oBAC3D;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,IAAI,CAAC,MAAM,GAAG,KAAK,EACf,GAAG,KAAK,CAAC,IAAI,CAAC;wBAAE,OAAO;4BAAC,IAAI,CAAC,WAAW;yBAAC;oBAAC;yBACzC,IAAI,GAAG,GAAG,EACX,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;yBAE5B,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBAClC;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAwB;wBACzB,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;wBACpC,IAAI,CAAC,MAAM,GAAG,KAAK,EACf,GAAG,KAAK,CAAC,IAAI,CAAC;4BAAE,OAAO,EAAE;4BAAE,KAAK;4BAAI,KAAK,EAAE;wBAAC;6BAC3C,IAAI,GAAG,GAAG,EACX,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;6BAEhB,OAAO,MAAM,CAAC,IAAI;4BAAE,KAAK;4BAAI,KAAK,EAAE;wBAAC;wBACzC;oBACJ;gBACA,KAAK;gBACL,KAAK;oBACD,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;oBAC5B;YACR;YACA,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC;YAChC,0CAA0C,GAC1C,IAAI,IACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;iBACf;gBACD,OAAO,IAAI,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,IAAI;YACpB;QACJ,OACK;YACD,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,KAAK,eAChB,CAAC,AAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB,OAAO,MAAM,KAAK,GAAG,MAAM,IACzD,IAAI,CAAC,IAAI,KAAK,aACX,CAAC,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,AAAC,GAAG;gBACtD,OAAO,IAAI,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,IAAI;YACpB,OACK,IAAI,IAAI,CAAC,IAAI,KAAK,mBACnB,OAAO,IAAI,KAAK,mBAAmB;gBACnC,MAAM,OAAO,aAAa;gBAC1B,MAAM,QAAQ,sBAAsB;gBACpC,gBAAgB;gBAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM;gBAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;gBACzB,MAAM,MAAM;oBACR,MAAM;oBACN,QAAQ,GAAG,MAAM;oBACjB,QAAQ,GAAG,MAAM;oBACjB,OAAO;wBAAC;4BAAE;4BAAO,KAAK;4BAAI;wBAAI;qBAAE;gBACpC;gBACA,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;YACxC,OACK;gBACD,OAAO,IAAI,CAAC,OAAO,CAAC;YACxB;QACJ;IACJ;IACA,WAAW,IAAI,EAAE;QACb,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;YACrC,MAAO,OAAO,EAAG;gBACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;gBAC7B,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,MAAM;YACzC;QACJ;QACA,OAAO;YACH;YACA,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,MAAM;QACvB;IACJ;IACA,gBAAgB,MAAM,EAAE;QACpB,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;YACpC,KAAK;gBACD,OAAO;oBACH,MAAM;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,QAAQ,IAAI,CAAC,MAAM;oBACnB,OAAO;wBAAC,IAAI,CAAC,WAAW;qBAAC;oBACzB,QAAQ;gBACZ;YACJ,KAAK;YACL,KAAK;gBACD,OAAO;oBACH,MAAM;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,QAAQ,IAAI,CAAC,MAAM;oBACnB,OAAO,IAAI,CAAC,WAAW;oBACvB,OAAO,EAAE;oBACT,KAAK,EAAE;gBACX;YACJ,KAAK;gBACD,OAAO;oBACH,MAAM;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,QAAQ,IAAI,CAAC,MAAM;oBACnB,OAAO;wBAAC;4BAAE,OAAO;gCAAC,IAAI,CAAC,WAAW;6BAAC;wBAAC;qBAAE;gBAC1C;YACJ,KAAK;gBAAoB;oBACrB,IAAI,CAAC,SAAS,GAAG;oBACjB,MAAM,OAAO,aAAa;oBAC1B,MAAM,QAAQ,sBAAsB;oBACpC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW;oBAC3B,OAAO;wBACH,MAAM;wBACN,QAAQ,IAAI,CAAC,MAAM;wBACnB,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO;4BAAC;gCAAE;gCAAO,aAAa;4BAAK;yBAAE;oBACzC;gBACJ;YACA,KAAK;gBAAiB;oBAClB,IAAI,CAAC,SAAS,GAAG;oBACjB,MAAM,OAAO,aAAa;oBAC1B,MAAM,QAAQ,sBAAsB;oBACpC,OAAO;wBACH,MAAM;wBACN,QAAQ,IAAI,CAAC,MAAM;wBACnB,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO;4BAAC;gCAAE;gCAAO,KAAK;gCAAM,KAAK;oCAAC,IAAI,CAAC,WAAW;iCAAC;4BAAC;yBAAE;oBAC1D;gBACJ;QACJ;QACA,OAAO;IACX;IACA,kBAAkB,KAAK,EAAE,MAAM,EAAE;QAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,WACd,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,IAAI,QACf,OAAO;QACX,OAAO,MAAM,KAAK,CAAC,CAAA,KAAM,GAAG,IAAI,KAAK,aAAa,GAAG,IAAI,KAAK;IAClE;IACA,CAAC,YAAY,MAAM,EAAE;QACjB,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY;YAC1B,IAAI,OAAO,GAAG,EACV,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;iBAEhC,OAAO,GAAG,GAAG;gBAAC,IAAI,CAAC,WAAW;aAAC;YACnC,IAAI,IAAI,CAAC,IAAI,KAAK,WACd,OAAO,IAAI,CAAC,GAAG;QACvB;IACJ;IACA,CAAC,QAAQ,KAAK,EAAE;QACZ,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,IAAI;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,SAAS,GAAG;YACrB,cAAc;YACd,KAAK;YACL,KAAK;YACL;gBACI,8BAA8B;gBAC9B,IAAI,MAAM,GAAG,EACT,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;qBAE/B,MAAM,GAAG,GAAG;oBAAC,IAAI,CAAC,WAAW;iBAAC;gBAClC,IAAI,IAAI,CAAC,IAAI,KAAK,WACd,OAAO,IAAI,CAAC,GAAG;QAC3B;IACJ;AACJ;AAEA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/public-api.js"], "sourcesContent": ["'use strict';\n\nvar composer = require('./compose/composer.js');\nvar Document = require('./doc/Document.js');\nvar errors = require('./errors.js');\nvar log = require('./log.js');\nvar identity = require('./nodes/identity.js');\nvar lineCounter = require('./parse/line-counter.js');\nvar parser = require('./parse/parser.js');\n\nfunction parseOptions(options) {\n    const prettyErrors = options.prettyErrors !== false;\n    const lineCounter$1 = options.lineCounter || (prettyErrors && new lineCounter.LineCounter()) || null;\n    return { lineCounter: lineCounter$1, prettyErrors };\n}\n/**\n * Parse the input as a stream of YAML documents.\n *\n * Documents should be separated from each other by `...` or `---` marker lines.\n *\n * @returns If an empty `docs` array is returned, it will be of type\n *   EmptyStream and contain additional stream information. In\n *   TypeScript, you should use `'empty' in docs` as a type guard for it.\n */\nfunction parseAllDocuments(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser$1 = new parser.Parser(lineCounter?.addNewLine);\n    const composer$1 = new composer.Composer(options);\n    const docs = Array.from(composer$1.compose(parser$1.parse(source)));\n    if (prettyErrors && lineCounter)\n        for (const doc of docs) {\n            doc.errors.forEach(errors.prettifyError(source, lineCounter));\n            doc.warnings.forEach(errors.prettifyError(source, lineCounter));\n        }\n    if (docs.length > 0)\n        return docs;\n    return Object.assign([], { empty: true }, composer$1.streamInfo());\n}\n/** Parse an input string into a single YAML.Document */\nfunction parseDocument(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser$1 = new parser.Parser(lineCounter?.addNewLine);\n    const composer$1 = new composer.Composer(options);\n    // `doc` is always set by compose.end(true) at the very latest\n    let doc = null;\n    for (const _doc of composer$1.compose(parser$1.parse(source), true, source.length)) {\n        if (!doc)\n            doc = _doc;\n        else if (doc.options.logLevel !== 'silent') {\n            doc.errors.push(new errors.YAMLParseError(_doc.range.slice(0, 2), 'MULTIPLE_DOCS', 'Source contains multiple documents; please use YAML.parseAllDocuments()'));\n            break;\n        }\n    }\n    if (prettyErrors && lineCounter) {\n        doc.errors.forEach(errors.prettifyError(source, lineCounter));\n        doc.warnings.forEach(errors.prettifyError(source, lineCounter));\n    }\n    return doc;\n}\nfunction parse(src, reviver, options) {\n    let _reviver = undefined;\n    if (typeof reviver === 'function') {\n        _reviver = reviver;\n    }\n    else if (options === undefined && reviver && typeof reviver === 'object') {\n        options = reviver;\n    }\n    const doc = parseDocument(src, options);\n    if (!doc)\n        return null;\n    doc.warnings.forEach(warning => log.warn(doc.options.logLevel, warning));\n    if (doc.errors.length > 0) {\n        if (doc.options.logLevel !== 'silent')\n            throw doc.errors[0];\n        else\n            doc.errors = [];\n    }\n    return doc.toJS(Object.assign({ reviver: _reviver }, options));\n}\nfunction stringify(value, replacer, options) {\n    let _replacer = null;\n    if (typeof replacer === 'function' || Array.isArray(replacer)) {\n        _replacer = replacer;\n    }\n    else if (options === undefined && replacer) {\n        options = replacer;\n    }\n    if (typeof options === 'string')\n        options = options.length;\n    if (typeof options === 'number') {\n        const indent = Math.round(options);\n        options = indent < 1 ? undefined : indent > 8 ? { indent: 8 } : { indent };\n    }\n    if (value === undefined) {\n        const { keepUndefined } = options ?? replacer ?? {};\n        if (!keepUndefined)\n            return undefined;\n    }\n    if (identity.isDocument(value) && !_replacer)\n        return value.toString(options);\n    return new Document.Document(value, _replacer, options).toString(options);\n}\n\nexports.parse = parse;\nexports.parseAllDocuments = parseAllDocuments;\nexports.parseDocument = parseDocument;\nexports.stringify = stringify;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAa,OAAO;IACzB,MAAM,eAAe,QAAQ,YAAY,KAAK;IAC9C,MAAM,gBAAgB,QAAQ,WAAW,IAAK,gBAAgB,IAAI,YAAY,WAAW,MAAO;IAChG,OAAO;QAAE,aAAa;QAAe;IAAa;AACtD;AACA;;;;;;;;CAQC,GACD,SAAS,kBAAkB,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3C,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,aAAa;IACnD,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,aAAa;IAChD,MAAM,aAAa,IAAI,SAAS,QAAQ,CAAC;IACzC,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,OAAO,CAAC,SAAS,KAAK,CAAC;IAC1D,IAAI,gBAAgB,aAChB,KAAK,MAAM,OAAO,KAAM;QACpB,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,aAAa,CAAC,QAAQ;QAChD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,aAAa,CAAC,QAAQ;IACtD;IACJ,IAAI,KAAK,MAAM,GAAG,GACd,OAAO;IACX,OAAO,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,OAAO;IAAK,GAAG,WAAW,UAAU;AACnE;AACA,sDAAsD,GACtD,SAAS,cAAc,MAAM,EAAE,UAAU,CAAC,CAAC;IACvC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,aAAa;IACnD,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,aAAa;IAChD,MAAM,aAAa,IAAI,SAAS,QAAQ,CAAC;IACzC,8DAA8D;IAC9D,IAAI,MAAM;IACV,KAAK,MAAM,QAAQ,WAAW,OAAO,CAAC,SAAS,KAAK,CAAC,SAAS,MAAM,OAAO,MAAM,EAAG;QAChF,IAAI,CAAC,KACD,MAAM;aACL,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU;YACxC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,iBAAiB;YACnF;QACJ;IACJ;IACA,IAAI,gBAAgB,aAAa;QAC7B,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,aAAa,CAAC,QAAQ;QAChD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,aAAa,CAAC,QAAQ;IACtD;IACA,OAAO;AACX;AACA,SAAS,MAAM,GAAG,EAAE,OAAO,EAAE,OAAO;IAChC,IAAI,WAAW;IACf,IAAI,OAAO,YAAY,YAAY;QAC/B,WAAW;IACf,OACK,IAAI,YAAY,aAAa,WAAW,OAAO,YAAY,UAAU;QACtE,UAAU;IACd;IACA,MAAM,MAAM,cAAc,KAAK;IAC/B,IAAI,CAAC,KACD,OAAO;IACX,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAA,UAAW,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;IAC/D,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACvB,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,UACzB,MAAM,IAAI,MAAM,CAAC,EAAE;aAEnB,IAAI,MAAM,GAAG,EAAE;IACvB;IACA,OAAO,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAAE,SAAS;IAAS,GAAG;AACzD;AACA,SAAS,UAAU,KAAK,EAAE,QAAQ,EAAE,OAAO;IACvC,IAAI,YAAY;IAChB,IAAI,OAAO,aAAa,cAAc,MAAM,OAAO,CAAC,WAAW;QAC3D,YAAY;IAChB,OACK,IAAI,YAAY,aAAa,UAAU;QACxC,UAAU;IACd;IACA,IAAI,OAAO,YAAY,UACnB,UAAU,QAAQ,MAAM;IAC5B,IAAI,OAAO,YAAY,UAAU;QAC7B,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,UAAU,SAAS,IAAI,YAAY,SAAS,IAAI;YAAE,QAAQ;QAAE,IAAI;YAAE;QAAO;IAC7E;IACA,IAAI,UAAU,WAAW;QACrB,MAAM,EAAE,aAAa,EAAE,GAAG,WAAW,YAAY,CAAC;QAClD,IAAI,CAAC,eACD,OAAO;IACf;IACA,IAAI,SAAS,UAAU,CAAC,UAAU,CAAC,WAC/B,OAAO,MAAM,QAAQ,CAAC;IAC1B,OAAO,IAAI,SAAS,QAAQ,CAAC,OAAO,WAAW,SAAS,QAAQ,CAAC;AACrE;AAEA,QAAQ,KAAK,GAAG;AAChB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,aAAa,GAAG;AACxB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/%40scalar/openapi-parser/node_modules/yaml/dist/index.js"], "sourcesContent": ["'use strict';\n\nvar composer = require('./compose/composer.js');\nvar Document = require('./doc/Document.js');\nvar Schema = require('./schema/Schema.js');\nvar errors = require('./errors.js');\nvar Alias = require('./nodes/Alias.js');\nvar identity = require('./nodes/identity.js');\nvar Pair = require('./nodes/Pair.js');\nvar Scalar = require('./nodes/Scalar.js');\nvar YAMLMap = require('./nodes/YAMLMap.js');\nvar YAMLSeq = require('./nodes/YAMLSeq.js');\nvar cst = require('./parse/cst.js');\nvar lexer = require('./parse/lexer.js');\nvar lineCounter = require('./parse/line-counter.js');\nvar parser = require('./parse/parser.js');\nvar publicApi = require('./public-api.js');\nvar visit = require('./visit.js');\n\n\n\nexports.Composer = composer.Composer;\nexports.Document = Document.Document;\nexports.Schema = Schema.Schema;\nexports.YAMLError = errors.YAMLError;\nexports.YAMLParseError = errors.YAMLParseError;\nexports.YAMLWarning = errors.YAMLWarning;\nexports.Alias = Alias.Alias;\nexports.isAlias = identity.isAlias;\nexports.isCollection = identity.isCollection;\nexports.isDocument = identity.isDocument;\nexports.isMap = identity.isMap;\nexports.isNode = identity.isNode;\nexports.isPair = identity.isPair;\nexports.isScalar = identity.isScalar;\nexports.isSeq = identity.isSeq;\nexports.Pair = Pair.Pair;\nexports.Scalar = Scalar.Scalar;\nexports.YAMLMap = YAMLMap.YAMLMap;\nexports.YAMLSeq = YAMLSeq.YAMLSeq;\nexports.CST = cst;\nexports.Lexer = lexer.Lexer;\nexports.LineCounter = lineCounter.LineCounter;\nexports.Parser = parser.Parser;\nexports.parse = publicApi.parse;\nexports.parseAllDocuments = publicApi.parseAllDocuments;\nexports.parseDocument = publicApi.parseDocument;\nexports.stringify = publicApi.stringify;\nexports.visit = visit.visit;\nexports.visitAsync = visit.visitAsync;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAIJ,QAAQ,QAAQ,GAAG,SAAS,QAAQ;AACpC,QAAQ,QAAQ,GAAG,SAAS,QAAQ;AACpC,QAAQ,MAAM,GAAG,OAAO,MAAM;AAC9B,QAAQ,SAAS,GAAG,OAAO,SAAS;AACpC,QAAQ,cAAc,GAAG,OAAO,cAAc;AAC9C,QAAQ,WAAW,GAAG,OAAO,WAAW;AACxC,QAAQ,KAAK,GAAG,MAAM,KAAK;AAC3B,QAAQ,OAAO,GAAG,SAAS,OAAO;AAClC,QAAQ,YAAY,GAAG,SAAS,YAAY;AAC5C,QAAQ,UAAU,GAAG,SAAS,UAAU;AACxC,QAAQ,KAAK,GAAG,SAAS,KAAK;AAC9B,QAAQ,MAAM,GAAG,SAAS,MAAM;AAChC,QAAQ,MAAM,GAAG,SAAS,MAAM;AAChC,QAAQ,QAAQ,GAAG,SAAS,QAAQ;AACpC,QAAQ,KAAK,GAAG,SAAS,KAAK;AAC9B,QAAQ,IAAI,GAAG,KAAK,IAAI;AACxB,QAAQ,MAAM,GAAG,OAAO,MAAM;AAC9B,QAAQ,OAAO,GAAG,QAAQ,OAAO;AACjC,QAAQ,OAAO,GAAG,QAAQ,OAAO;AACjC,QAAQ,GAAG,GAAG;AACd,QAAQ,KAAK,GAAG,MAAM,KAAK;AAC3B,QAAQ,WAAW,GAAG,YAAY,WAAW;AAC7C,QAAQ,MAAM,GAAG,OAAO,MAAM;AAC9B,QAAQ,KAAK,GAAG,UAAU,KAAK;AAC/B,QAAQ,iBAAiB,GAAG,UAAU,iBAAiB;AACvD,QAAQ,aAAa,GAAG,UAAU,aAAa;AAC/C,QAAQ,SAAS,GAAG,UAAU,SAAS;AACvC,QAAQ,KAAK,GAAG,MAAM,KAAK;AAC3B,QAAQ,UAAU,GAAG,MAAM,UAAU", "ignoreList": [0], "debugId": null}}]}