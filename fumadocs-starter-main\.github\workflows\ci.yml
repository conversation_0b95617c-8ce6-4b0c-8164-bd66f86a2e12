name: CI

on:
  pull_request:
    branches:
      - main

jobs:
  types:
    name: TypeScript
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run type check
        run: bun typecheck

  biome:
    name: Biome
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run Biome
        run: bun check

  spelling:
    name: Spelling
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run spelling check
        run: bun check:spelling