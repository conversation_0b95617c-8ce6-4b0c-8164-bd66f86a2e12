var Regex;(Regex||={}).plugins=(()=>{var N=Object.defineProperty;var q=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var J=Object.prototype.hasOwnProperty;var K=(e,t)=>{for(var n in t)N(e,n,{get:t[n],enumerable:!0})},Q=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of y(t))!J.call(e,o)&&o!==n&&N(e,o,{get:()=>t[o],enumerable:!(r=q(t,o))||r.enumerable});return e};var V=e=>Q(N({},"__esModule",{value:!0}),e);var ne={};K(ne,{recursion:()=>Z});var m=Object.freeze({DEFAULT:"DEFAULT",CHAR_CLASS:"CHAR_CLASS"});function T(e,t,n,r){let o=new RegExp(String.raw`${t}|(?<$skip>\[\^?|\\?.)`,"gsu"),u=[!1],s=0,c="";for(let i of e.matchAll(o)){let{0:p,groups:{$skip:f}}=i;if(!f&&(!r||r===m.DEFAULT==!s)){n instanceof Function?c+=n(i,{context:s?m.CHAR_CLASS:m.DEFAULT,negated:u[u.length-1]}):c+=n;continue}p[0]==="["?(s++,u.push(p[1]==="^")):p==="]"&&s&&(s--,u.pop()),c+=p}return c}function F(e,t,n,r){T(e,t,n,r)}function X(e,t,n=0,r){if(!new RegExp(t,"su").test(e))return null;let o=new RegExp(`${t}|(?<$skip>\\\\?.)`,"gsu");o.lastIndex=n;let u=0,s;for(;s=o.exec(e);){let{0:c,groups:{$skip:i}}=s;if(!i&&(!r||r===m.DEFAULT==!u))return s;c==="["?u++:c==="]"&&u&&u--,o.lastIndex==s.index&&o.lastIndex++}return null}function k(e,t,n){return!!X(e,t,0,n)}function G(e,t){let n=/\\?./gsu;n.lastIndex=t;let r=e.length,o=0,u=1,s;for(;s=n.exec(e);){let[c]=s;if(c==="[")o++;else if(o)c==="]"&&o--;else if(c==="(")u++;else if(c===")"&&(u--,!u)){r=s.index;break}}return e.slice(t,r)}var w=String.raw,Y=w`\\g<(?<gRNameOrNum>[^>&]+)&R=(?<gRDepth>[^>]+)>`,I=w`\(\?R=(?<rDepth>[^\)]+)\)|${Y}`,L=w`\(\?<(?![=!])(?<captureName>[^>]+)>`,_=w`${L}|(?<unnamed>\()(?!\?)`,x=new RegExp(w`${L}|${I}|\(\?|\\?.`,"gsu"),b="Cannot use multiple overlapping recursions";function Z(e,t){let{hiddenCaptures:n,mode:r}={hiddenCaptures:[],mode:"plugin",...t},o=t?.captureTransfers??new Map;if(!new RegExp(I,"su").test(e))return{pattern:e,captureTransfers:o,hiddenCaptures:n};if(r==="plugin"&&k(e,w`\(\?\(DEFINE\)`,m.DEFAULT))throw new Error("DEFINE groups cannot be used with recursion");let u=[],s=k(e,w`\\[1-9]`,m.DEFAULT),c=new Map,i=[],p=!1,f=0,a=0,$;for(x.lastIndex=0;$=x.exec(e);){let{0:g,groups:{captureName:d,rDepth:h,gRNameOrNum:l,gRDepth:R}}=$;if(g==="[")f++;else if(f)g==="]"&&f--;else if(h){if(B(h),p)throw new Error(b);if(s)throw new Error(`${r==="external"?"Backrefs":"Numbered backrefs"} cannot be used with global recursion`);let C=e.slice(0,$.index),E=e.slice(x.lastIndex);if(k(E,I,m.DEFAULT))throw new Error(b);let D=+h-1;e=H(C,E,D,!1,n,u,a),o=W(o,C,D,u.length,0,a);break}else if(l){B(R);let C=!1;for(let U of i)if(U.name===l||U.num===+l){if(C=!0,U.hasRecursedWithin)throw new Error(b);break}if(!C)throw new Error(w`Recursive \g cannot be used outside the referenced group "${r==="external"?l:w`\g<${l}&R=${R}>`}"`);let E=c.get(l),D=G(e,E);if(s&&k(D,w`${L}|\((?!\?)`,m.DEFAULT))throw new Error(`${r==="external"?"Backrefs":"Numbered backrefs"} cannot be used with recursion of capturing groups`);let A=e.slice(E,$.index),S=D.slice(A.length+g.length),O=u.length,M=+R-1,v=H(A,S,M,!0,n,u,a);o=W(o,A,M,u.length-O,O,a);let z=e.slice(0,E),j=e.slice(E+D.length);e=`${z}${v}${j}`,x.lastIndex+=v.length-g.length-A.length-S.length,i.forEach(U=>U.hasRecursedWithin=!0),p=!0}else if(d)a++,c.set(String(a),x.lastIndex),c.set(d,x.lastIndex),i.push({num:a,name:d});else if(g[0]==="("){let C=g==="(";C&&(a++,c.set(String(a),x.lastIndex)),i.push(C?{num:a}:{})}else g===")"&&i.pop()}return n.push(...u),{pattern:e,captureTransfers:o,hiddenCaptures:n}}function B(e){let t=`Max depth must be integer between 2 and 100; used ${e}`;if(!/^[1-9]\d*$/.test(e))throw new Error(t);if(e=+e,e<2||e>100)throw new Error(t)}function H(e,t,n,r,o,u,s){let c=new Set;r&&F(e+t,L,({groups:{captureName:p}})=>{c.add(p)},m.DEFAULT);let i=[n,r?c:null,o,u,s];return`${e}${P(`(?:${e}`,"forward",...i)}(?:)${P(`${t})`,"backward",...i)}${t}`}function P(e,t,n,r,o,u,s){let i=f=>t==="forward"?f+2:n-f+2-1,p="";for(let f=0;f<n;f++){let a=i(f);p+=T(e,w`${_}|\\k<(?<backref>[^>]+)>`,({0:$,groups:{captureName:g,unnamed:d,backref:h}})=>{if(h&&r&&!r.has(h))return $;let l=`_$${a}`;if(d||g){let R=s+u.length+1;return u.push(R),ee(o,R),d?$:`(?<${g}${l}>`}return w`\k<${h}${l}>`},m.DEFAULT)}return p}function ee(e,t){for(let n=0;n<e.length;n++)e[n]>=t&&e[n]++}function W(e,t,n,r,o,u){if(e.size&&r){let s=0;F(t,_,()=>s++,m.DEFAULT);let c=u-s+o,i=new Map;return e.forEach((p,f)=>{let a=(r-s*n)/n,$=s*n,g=f>c+s?f+r:f,d=[];for(let h of p)if(h<=c)d.push(h);else if(h>c+s+a)d.push(h+r);else if(h<=c+s)for(let l=0;l<=n;l++)d.push(h+s*l);else for(let l=0;l<=n;l++)d.push(h+$+a*l);i.set(g,d)}),i}return e}return V(ne);})();
//# sourceMappingURL=regex-recursion.min.js.map
