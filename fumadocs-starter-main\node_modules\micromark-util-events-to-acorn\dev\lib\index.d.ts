/**
 * Parse a list of micromark events with acorn.
 *
 * @param {Array<Event>} events
 *   Events.
 * @param {Options} options
 *   Configuration (required).
 * @returns {Result}
 *   Result.
 */
export function eventsToAcorn(events: Array<Event>, options: Options): Result;
import type { Event } from 'micromark-util-types';
import type { Options } from 'micromark-util-events-to-acorn';
import type { Result } from './types.js';
//# sourceMappingURL=index.d.ts.map