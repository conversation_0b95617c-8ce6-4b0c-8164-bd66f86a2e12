"use strict";import{isAlternativeContainer as i}from"../../parser/node-utils.js";import{createAlternative as n,createCharacterClass as c}from"../../parser/parse.js";const h={"*"({node:t}){if(!i(t)||t.body.length<2)return;const r=[];let e=[];for(const o of t.body){const a=o.body[0];o.body.length===1&&(a.type==="Character"||a.type==="CharacterClass"||a.type==="CharacterSet"&&d.has(a.kind))?e.push(a):(e.length&&(r.push(s(e)),e=[]),r.push(o))}e.length&&r.push(s(e)),t.body=r}};function s(t){const r=n(),e=t.length>1?c({body:t}):t[0];return e&&r.body.push(e),r}const d=new Set(["digit","hex","posix","property","space","word"]);export{h as alternationToClass};
//# sourceMappingURL=alternation-to-class.js.map
