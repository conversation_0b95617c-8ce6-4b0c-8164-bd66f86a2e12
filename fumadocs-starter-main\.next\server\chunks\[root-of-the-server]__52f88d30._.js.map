{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/source.config.ts"], "sourcesContent": ["import {\n  rehypeCodeDefaultOptions,\n  remarkSteps,\n} from 'fumadocs-core/mdx-plugins'\nimport {\n  defineConfig,\n  defineDocs,\n  frontmatterSchema,\n  metaSchema,\n} from 'fumadocs-mdx/config'\nimport { transformerTwoslash } from 'fumadocs-twoslash'\nimport { createFileSystemTypesCache } from 'fumadocs-twoslash/cache-fs'\nimport { remarkAutoTypeTable } from 'fumadocs-typescript'\nimport type { ElementContent } from 'hast'\nimport rehypeKatex from 'rehype-katex'\nimport remarkMath from 'remark-math'\nimport { z } from 'zod'\n\nexport const docs = defineDocs({\n  docs: {\n    async: true,\n    schema: frontmatterSchema.extend({\n      preview: z.string().optional(),\n      index: z.boolean().default(false),\n      /**\n       * API routes only\n       */\n      method: z.string().optional(),\n    }),\n  },\n  meta: {\n    schema: metaSchema.extend({\n      description: z.string().optional(),\n    }),\n  },\n})\n\nexport default defineConfig({\n  lastModifiedTime: 'git',\n  mdxOptions: {\n    rehypeCodeOptions: {\n      lazy: true,\n      experimentalJSEngine: true,\n      langs: ['ts', 'js', 'html', 'tsx', 'mdx'],\n      inline: 'tailing-curly-colon',\n      themes: {\n        light: 'catppuccin-latte',\n        dark: 'catppuccin-mocha',\n      },\n      transformers: [\n        ...(rehypeCodeDefaultOptions.transformers ?? []),\n        transformerTwoslash({\n          typesCache: createFileSystemTypesCache(),\n        }),\n        {\n          name: '@shikijs/transformers:remove-notation-escape',\n          code(hast) {\n            function replace(node: ElementContent): void {\n              if (node.type === 'text') {\n                node.value = node.value.replace('[\\\\!code', '[!code')\n              } else if ('children' in node) {\n                for (const child of node.children) {\n                  replace(child)\n                }\n              }\n            }\n\n            replace(hast)\n            return hast\n          },\n        },\n      ],\n    },\n    remarkCodeTabOptions: {\n      parseMdx: true,\n    },\n\n    remarkNpmOptions: {\n      persist: {\n        id: 'package-manager',\n      },\n    },\n    remarkPlugins: [remarkSteps, remarkMath, remarkAutoTypeTable],\n    rehypePlugins: (v) => [rehypeKatex, ...v],\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AAIA;AAAA;AAMA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;AAEO,MAAM,OAAO,IAAA,4KAAU,EAAC;IAC7B,MAAM;QACJ,OAAO;QACP,QAAQ,mLAAiB,CAAC,MAAM,CAAC;YAC/B,SAAS,oLAAC,CAAC,MAAM,GAAG,QAAQ;YAC5B,OAAO,oLAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B;;OAEC,GACD,QAAQ,oLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B;IACF;IACA,MAAM;QACJ,QAAQ,4KAAU,CAAC,MAAM,CAAC;YACxB,aAAa,oLAAC,CAAC,MAAM,GAAG,QAAQ;QAClC;IACF;AACF;uCAEe,IAAA,8KAAY,EAAC;IAC1B,kBAAkB;IAClB,YAAY;QACV,mBAAmB;YACjB,MAAM;YACN,sBAAsB;YACtB,OAAO;gBAAC;gBAAM;gBAAM;gBAAQ;gBAAO;aAAM;YACzC,QAAQ;YACR,QAAQ;gBACN,OAAO;gBACP,MAAM;YACR;YACA,cAAc;mBACR,iNAAwB,CAAC,YAAY,IAAI,EAAE;gBAC/C,IAAA,8KAAmB,EAAC;oBAClB,YAAY,IAAA,2LAA0B;gBACxC;gBACA;oBACE,MAAM;oBACN,MAAK,IAAI;wBACP,SAAS,QAAQ,IAAoB;4BACnC,IAAI,KAAK,IAAI,KAAK,QAAQ;gCACxB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,YAAY;4BAC9C,OAAO,IAAI,cAAc,MAAM;gCAC7B,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;oCACjC,QAAQ;gCACV;4BACF;wBACF;wBAEA,QAAQ;wBACR,OAAO;oBACT;gBACF;aACD;QACH;QACA,sBAAsB;YACpB,UAAU;QACZ;QAEA,kBAAkB;YAChB,SAAS;gBACP,IAAI;YACN;QACF;QACA,eAAe;YAAC,oMAAW;YAAE,2JAAU;YAAE,gMAAmB;SAAC;QAC7D,eAAe,CAAC,IAAM;gBAAC,4JAAW;mBAAK;aAAE;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/.source/index.ts"], "sourcesContent": ["// @ts-nocheck -- skip type checking\nimport { _runtimeAsync, buildConfig } from \"fumadocs-mdx/runtime/async\"\nconst _sourceConfig = buildConfig(_source)\nimport { _runtime } from \"fumadocs-mdx\"\nimport * as _source from \"../source.config\"\nexport const docs = _runtimeAsync.docs<typeof _source.docs>([{\"info\":{\"path\":\"api-reference\\\\index.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/index.mdx\"},\"data\":{\"title\":\"Introduction\",\"description\":\"This is a page to check fumadocs's OpenAPI example.\",\"icon\":\"Book\",\"index\":false},\"content\":\"\\nWelcome to the OpenAPI example! You can update the openapi in the 'openapi.yml' file.\\n\"}, {\"info\":{\"path\":\"app\\\\index.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/index.mdx\"},\"data\":{\"title\":\"Introduction\",\"description\":\"Welcome to your new documentation\",\"icon\":\"Book\",\"index\":false},\"content\":\"\\n## Getting Started\\n\\nThe first step to creating amazing documentation is setting up your editing environment.\\n\\n<Cards cols={2}>\\n  <Card title=\\\"Quickstart\\\" icon={<PlayIcon />} href=\\\"/docs/app/quickstart\\\">\\n    Learn how to set up your docs for easy local development.\\n  </Card>\\n  <Card title=\\\"Routing\\\" icon={<FilesIcon />} href=\\\"/docs/app/essentials/routing\\\">\\n    Learn how to structure your .mdx files and folders to define the sidebar\\n    layout in Fumadocs\\n  </Card>\\n</Cards>\\n\\n## Make it yours\\n\\nCustomize your documentation to reflect your brand and include meaningful content to maximize user engagement and conversions.\\n\\n<Cards cols={2}>\\n  <Card\\n    title=\\\"Customize Style\\\"\\n    icon={<PaletteIcon />}\\n    href=\\\"https://fumadocs.dev/docs/ui/theme\\\"\\n  >\\n    Customize your documentation by applying your brand colors and styles.\\n  </Card>\\n  <Card\\n    title=\\\"Reference APIs\\\"\\n    icon={<WebhookIcon />}\\n    href=\\\"/docs/app/features/openapi\\\"\\n  >\\n    Auto-generate interactive endpoint docs straight from your OpenAPI spec.\\n  </Card>\\n  <Card\\n    title=\\\"Add Components\\\"\\n    icon={<PuzzleIcon />}\\n    href=\\\"https://fumadocs.dev/docs/ui/components\\\"\\n  >\\n    Embed interactive elements and guides to improve user engagement.\\n  </Card>\\n  <Card\\n    title=\\\"Get Inspiration\\\"\\n    icon={<SparklesIcon />}\\n    href=\\\"https://fumadocs.dev/showcase\\\"\\n  >\\n    Browse our showcase for creative ideas and best practices.\\n  </Card>\\n</Cards>\\n\"}, {\"info\":{\"path\":\"app\\\\quickstart.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/quickstart.mdx\"},\"data\":{\"title\":\"Quickstart\",\"description\":\"Start building awesome documentation in under 5 minutes\",\"icon\":\"Play\",\"index\":false},\"content\":\"\\n## Setup your development environment\\n\\nA minimum version of Node.js 18 is required. Note that Node.js 23.1 might have issues with the Next.js production build.\\n\\n<Tabs groupId=\\\"package-manager\\\" persist items={['npm', 'pnpm', 'yarn', 'bun']}>\\n  ```bash tab=\\\"npm\\\"\\n  npx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter\\n  ```\\n\\n```bash tab=\\\"pnpm\\\"\\nnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-pnpm\\n```\\n\\n```bash tab=\\\"yarn\\\"\\nnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-yarn\\n```\\n\\n```bash tab=\\\"bun\\\"\\nnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-bun\\n```\\n\\n</Tabs>\\n\\nThe Fumadocs template should now be initialized. You can start development!\\n\\n### Start the development server\\n\\nRun the app in development mode and open [http://localhost:3000/docs/app](http://localhost:3000/docs/app) in your browser.\\n\\n<Tabs groupId=\\\"package-manager\\\" persist items={['npm', 'pnpm', 'yarn', 'bun']}>\\n    ```bash tab=\\\"npm\\\"\\n    npm run dev\\n    ```\\n\\n    ```bash tab=\\\"pnpm\\\"\\n    pnpm run dev\\n    ```\\n\\n    ```bash tab=\\\"yarn\\\"\\n    yarn dev\\n    ```\\n\\n    ```bash tab=\\\"bun\\\"\\n    bun run dev\\n    ```\\n\\n</Tabs>\\n\\n### Add your content\\n\\nFumadocs uses MDX for documentation, allowing you to write Markdown combined with React components. Add your content inside the `content/docs` directory. The structure is similar to a standard Next.js app, making it easy to navigate and manage your files. To learn more about MDX, check out the [MDX page](/docs/app/essentials/markdown).\\n\\nCreate your first MDX file in the `docs` folder:\\n\\n```mdx title=\\\"content/docs/app/index.mdx\\\"\\n---\\ntitle: Hello World\\n---\\n\\n## Yo, what's up?\\n```\\n\\nThe app organizes content by concerns, e.g., `content/docs/api-reference`, `content/docs/app`, `content/docs/changelog`, etc.\\n\\n## Deploy your changes\\n\\nThis template works out-of-the-box with Vercel or Netlify. You can deploy your documentation site with a single click.\\n\\n### Docker Deployment\\n\\nIf you want to deploy your Fumadocs app using Docker with **Fumadocs MDX configured**, make sure to add the `source.config.ts` file to the `WORKDIR` in the Dockerfile.\\nThe following snippet is taken from the official [Next.js Dockerfile Example](https://github.com/vercel/next.js/blob/canary/examples/with-docker/Dockerfile):\\n\\n```zsh title=\\\"Dockerfile\\\"\\nWORKDIR /app\\n\\n# Install dependencies based on the preferred package manager\\nCOPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc* source.config.ts ./  # [!code highlight]\\n```\\n\\nThis ensures Fumadocs MDX can access your configuration file during builds.\\n\\n## Update your docs\\n\\nAdd content directly in your files using MDX syntax and React components. You can use built-in components or create your own.\\n\\n<Cards>\\n\\n<Card\\n  title=\\\"Add Content With MDX\\\"\\n  icon={<FileIcon />}\\n  href=\\\"/docs/app/essentials/markdown\\\"\\n>\\n  Add content to your docs with MDX syntax.\\n</Card>\\n\\n<Card\\n  title=\\\"Add Code Blocks\\\"\\n  icon={<CodeIcon />}\\n  href=\\\"/docs/app/essentials/code\\\"\\n>\\n  Add code directly to your docs with syntax highlighting.\\n</Card>\\n\\n</Cards>\\n\"}, {\"info\":{\"path\":\"changelog\\\\index.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/changelog/index.mdx\"},\"data\":{\"title\":\"Product Updates\",\"description\":\"New updates and improvements\",\"icon\":\"List\",\"index\":false},\"content\":\"\\n<Updates>\\n  <Update label=\\\"May 2025\\\">\\n    ### General Improvements\\n\\n    * Redesigned notification settings for better control over email and in-app alerts.\\n    * Fixed issue where some users were not receiving 2FA codes via SMS.\\n    * Activity log now includes more granular details for API usage.\\n    * Added keyboard shortcuts to the dashboard for quicker navigation.\\n\\n  </Update>\\n\\n  <Update label=\\\"April 2025\\\">\\n    ### Mobile Enhancements\\n\\n    * Major performance optimizations for older Android devices.\\n    * Added gesture support for calendar rescheduling.\\n    * Resolved an issue causing data sync delays when switching between networks.\\n    * Push notification reliability improved for background updates.\\n\\n  </Update>\\n\\n  <Update label=\\\"March 2025\\\">\\n    ### Integration Upgrades\\n\\n    * Microsoft Teams integration now supports deep-linking to projects.\\n    * Added webhook event for task reassignment.\\n    * Jira integration now includes sprint sync and story point mapping.\\n    * New Zapier triggers for completed tasks and file uploads.\\n\\n  </Update>\\n\\n  <Update label=\\\"February 2025\\\">\\n    ### Analytics & Reporting\\n\\n    * Custom reports can now be scheduled and emailed automatically.\\n    * Dashboard now supports drill-down charts for task status and team performance.\\n    * Added time tracking summary by user and project.\\n    * Export options now include PDF format with improved styling.\\n\\n  </Update>\\n\\n  <Update label=\\\"January 2025\\\">\\n    ### UX & Accessibility\\n\\n    * Redesigned sidebar with collapsible sections for cleaner navigation.\\n    * Improved screen reader support for form fields and buttons.\\n    * Fixed inconsistent behavior of dropdowns in Safari.\\n    * New color contrast settings for better visibility in dark mode.\\n\\n  </Update>\\n\\n  <Update label=\\\"December 2024\\\">\\n    ### Year-End Stability Release\\n\\n    * Fixed edge-case bugs reported during November release.\\n    * Reduced server response latency during peak hours by 15%.\\n    * Backend job processing now has retry logic with alerting on failure.\\n    * Cleaned up deprecated API endpoints — refer to migration guide if needed.\\n\\n  </Update>\\n\\n  <Update label=\\\"November 2024\\\">\\n    ### Feature Updates\\n\\n    * New “Smart Labels” automatically categorize tasks based on content.\\n    * Added batch actions for checklists and subtasks.\\n    * Admins can now view login history per user.\\n    * Kanban board now remembers last viewed filters and column order.\\n\\n  </Update>\\n\\n  <Update label=\\\"October 2024\\\">\\n    ### User Management\\n\\n    * Invite flow redesigned to allow bulk user imports from CSV.\\n    * Added user deactivation audit events.\\n    * Users can now request access to private projects (pending approval).\\n    * SSO login time reduced by ~40% with token reuse.\\n\\n  </Update>\\n\\n  <Update label=\\\"September 2024\\\">\\n    ### Productivity Tools\\n\\n    * Introduced “Focus Mode” – hides sidebar and notifications for distraction-free work.\\n    * Improved calendar drag-and-drop accuracy for overlapping events.\\n    * Sticky notes now support markdown formatting.\\n    * Weekly summary emails now include completed tasks and pending reviews.\\n\\n  </Update>\\n\\n  <Update label=\\\"August 2024\\\">\\n    ### Search & Navigation\\n\\n    * Global search now supports filters for date ranges, users, and tags.\\n    * Improved relevance ranking for file/document results.\\n    * Quick navigation panel added (press `/` to activate).\\n    * Recently viewed items show up in search suggestions.\\n\\n  </Update>\\n\\n  <Update label=\\\"July 2024\\\">\\n    ### Security & Compliance\\n\\n    * GDPR compliance updates: added data export + delete requests UI.\\n    * New admin-level controls for session expiration and login limits.\\n    * Enforced minimum password strength requirements across all users.\\n    * Bug fix: File links no longer accessible after permission removal.\\n\\n  </Update>\\n\\n  <Update label=\\\"June 2024\\\">\\n    ### Performance & Quality Updates\\n\\n    * Improved load times on dashboard by 25% through API response caching.\\n    * Fixed a bug where recurring events were duplicated when edited in bulk.\\n    * Added support for exporting user activity logs as CSV.\\n    * Updated UI components to match new branding guidelines.\\n    * Reduced idle memory usage in background sync tasks.\\n\\n  </Update>\\n\\n  <Update label=\\\"May 2024\\\">\\n    ### New Integrations\\n\\n    * Slack integration now supports direct replies to alerts.\\n    * Added native support for Notion links in task descriptions.\\n    * Webhooks now include retry logic with exponential backoff.\\n    * OAuth flow improved for Google and Microsoft accounts.\\n\\n  </Update>\\n</Updates>\\n\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events.mdx\"},\"data\":{\"title\":\"New special event added\",\"full\":true,\"_openapi\":{\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nPublish details of a new or updated event.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[]} webhooks={[{\\\"name\\\":\\\"publishNewEvent\\\",\\\"method\\\":\\\"post\\\"}]} hasHead={false} />\"}, {\"info\":{\"path\":\"app\\\\essentials\\\\code.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/essentials/code.mdx\"},\"data\":{\"title\":\"Code Blocks\",\"description\":\"Display inline code and code blocks\",\"icon\":\"Code\",\"index\":false},\"content\":\"\\n## Basic\\n\\n### Inline Code\\n\\nTo denote a `word` or `phrase` as code, enclose it in backticks (`).\\n\\n```\\nTo denote a `word` or `phrase` as code, enclose it in backticks (`).\\n```\\n\\n### Code Block\\n\\nUse [fenced code blocks](https://www.markdownguide.org/extended-syntax/#fenced-code-blocks) by enclosing code in three backticks and follow the leading ticks with the programming language of your snippet to get syntax highlighting. Optionally, you can also write the name of your code after the programming language. Syntax Highlighting is supported by default using [Rehype Code](https://fumadocs.dev/docs/headless/mdx/rehype-code).\\n\\n```java title=\\\"HelloWorld.java\\\"\\nclass HelloWorld {\\n    public static void main(String[] args) {\\n        System.out.println(\\\"Hello, World!\\\");\\n    }\\n}\\n```\\n\\n````md\\n```java title=\\\"HelloWorld.java\\\"\\nclass HelloWorld {\\n    public static void main(String[] args) {\\n        System.out.println(\\\"Hello, World!\\\");\\n    }\\n}\\n```\\n````\\n\\n## Advanced\\n\\n### Package Install\\n\\nThe package install block automatically detects common package managers (npm, yarn, pnpm) and displays installation commands for each. Users can switch between different package managers using tabs.\\n\\n````md tab=\\\"Input\\\"\\n```package-install\\nnpm i next -D\\n```\\n````\\n\\n```package-install tab=\\\"Output\\\"\\n@types/react\\n```\\n\\n### Shiki Transformers\\n\\nWe support some of the [Shiki Transformers](https://shiki.style/packages/transformers), allowing you to highlight/style specific lines.\\n\\n````md tab=\\\"Input\\\"\\n```tsx\\n// highlight a line\\n<div>Hello World</div>  // [\\\\!code highlight]\\n\\n// highlight a word\\n// [\\\\!code word:Fumadocs]\\n<div>Fumadocs</div>\\n\\n// diff styles\\nconsole.log('hewwo'); // [\\\\!code --]\\nconsole.log('hello'); // [\\\\!code ++]\\n```\\n````\\n\\n```tsx tab=\\\"Output\\\"\\n// highlight a line\\n<div>Hello World</div>  // [!code highlight]\\n\\n// highlight a word\\n// [!code word:Fumadocs]\\n<div>Fumadocs</div>\\n\\n// diff styles:\\nconsole.log('hewwo'); // [!code --]\\nconsole.log('hello'); // [!code ++]\\n```\\n\\n### Twoslash Notations\\n\\nLearn more about [Twoslash notations](https://twoslash.netlify.app/refs/notations).\\n\\n```ts twoslash title=\\\"Test\\\"\\ntype Player = {\\n  /**\\n   * The player name\\n   * @default 'user'\\n   */\\n  name: string;\\n};\\n\\n// ---cut---\\n// @noErrors\\nconsole.g;\\n//       ^|\\n\\n// ---cut-start---\\n// ---cut-end---\\n\\n// ---cut-start---\\n// ---cut-end---\\n\\n// ---cut-start---\\n// ---cut-end---\\n\\n// ---cut-start---\\n// ---cut-end---\\n\\nconst player: Player = { name: 'Hello World' };\\n//    ^?\\n```\\n\\n```ts twoslash\\nconst a = '123';\\n\\nconsole.log(a);\\n//      ^^^\\n```\\n\\n```ts twoslash\\nimport { generateFiles } from 'fumadocs-openapi';\\n\\nvoid generateFiles({\\n  input: ['./museum.yaml'],\\n  output: './content/docs/app',\\n});\\n```\\n\\n```ts twoslash\\n// @errors: 2588\\nconst a = '123';\\n\\na = 132;\\n```\\n\"}, {\"info\":{\"path\":\"app\\\\essentials\\\\markdown.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/essentials/markdown.mdx\"},\"data\":{\"title\":\"Markdown Syntax\",\"description\":\"Text, title, and styling in standard markdown\",\"icon\":\"LetterText\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nFumadocs provides many useful extensions to MDX, a markup language. Here is a brief introduction to the default MDX syntax of Fumadocs.\\n\\n> MDX is not the only supported format of Fumadocs. In fact, you can use any renderers such as `next-mdx-remote` or CMS.\\n\\n## MDX\\n\\nWe recommend MDX, a superset of Markdown with JSX syntax.\\nIt allows you to import components, and use them right in the document, or even export values.\\n\\nSee:\\n\\n- [MDX Syntax](https://mdxjs.com/docs/what-is-mdx/#mdx-syntax).\\n- GFM (GitHub Flavored Markdown) is also supported, see [GFM Specification](https://github.github.com/gfm).\\n\\n```mdx\\n---\\ntitle: This is a document\\n---\\n\\nimport { Component } from './component';\\n\\n<Component name=\\\"Hello\\\" />\\n\\n# Heading\\n\\n## Heading\\n\\n### Heading\\n\\n#### Heading\\n\\nHello World, **Bold**, _Italic_, ~~Hidden~~\\n\\n1. First\\n2. Second\\n3. Third\\n\\n- Item 1\\n- Item 2\\n\\n> Quote here\\n\\n![alt](/image.png)\\n\\n| Table | Description |\\n| ----- | ----------- |\\n| Hello | World       |\\n```\\n\\n### Images\\n\\nImages are automatically optimized for `next/image`.\\n\\n```mdx\\n![Image](/image.png)\\n```\\n\\n### Auto Links\\n\\nInternal links use the `next/link` component to allow prefetching and avoid hard-reload.\\n\\nExternal links will get the default `rel=\\\"noreferrer noopener\\\" target=\\\"_blank\\\"` attributes for security.\\n\\n```mdx\\n[My Link](https://github.github.com/gfm)\\n\\nThis also works: https://github.github.com/gfm.\\n```\\n\\n### Cards\\n\\nUseful for adding links.\\n\\n```mdx\\nimport { HomeIcon } from 'lucide-react';\\n\\n<Cards>\\n  <Card\\n    href=\\\"https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating\\\"\\n    title=\\\"Fetching, Caching, and Revalidating\\\"\\n  >\\n    Learn more about caching in Next.js\\n  </Card>\\n  <Card title=\\\"href is optional\\\">Learn more about `fetch` in Next.js.</Card>\\n  <Card icon={<HomeIcon />} href=\\\"/\\\" title=\\\"Home\\\">\\n    You can include icons too.\\n  </Card>\\n</Cards>\\n```\\n\\n<Cards>\\n  <Card href=\\\"https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating\\\" title=\\\"Fetching, Caching, and Revalidating\\\">\\n    Learn more about caching in Next.js\\n  </Card>\\n\\n<Card title=\\\"href is optional\\\">Learn more about `fetch` in Next.js.</Card>\\n\\n  <Card icon={<HomeIcon />} href=\\\"/\\\" title=\\\"Home\\\">\\n    You can include icons too.\\n  </Card>\\n</Cards>\\n\\n### Callouts\\n\\nUseful for adding tips/warnings, it is included by default. You can specify the type of callout:\\n\\n- `info` (default)\\n- `warn`\\n- `error`\\n\\n```mdx\\n<Callout>Hello World</Callout>\\n\\n<Callout title=\\\"Title\\\">Hello World</Callout>\\n\\n<Callout title=\\\"Title\\\" type=\\\"error\\\">\\n  Hello World\\n</Callout>\\n```\\n\\n<Callout>Hello World</Callout>\\n\\n<Callout title=\\\"Title\\\">Hello World</Callout>\\n\\n<Callout title=\\\"Title\\\" type=\\\"error\\\">\\n  Hello World\\n</Callout>\\n\\n### Headings\\n\\nAn anchor is automatically applied to each heading, it sanitizes invalid characters like spaces. (e.g. `Hello World` to `hello-world`)\\n\\n```md\\n# Hello `World`\\n```\\n\\n#### TOC Settings\\n\\nThe table of contents (TOC) will be generated based on headings, you can also customize the effects of headings:\\n\\n```md\\n# Heading [!toc]\\n\\nThis heading will be hidden from TOC.\\n\\n# Another Heading [toc]\\n\\nThis heading will **only** be visible in TOC, you can use it to add additional TOC items.\\nLike headings rendered in a React component:\\n\\n<MyComp />\\n```\\n\\n#### Custom Anchor\\n\\nYou can add `[#slug]` to customize heading anchors.\\n\\n```md\\n# heading [#my-heading-id]\\n```\\n\\nYou can also chain it with TOC settings like:\\n\\n```md\\n# heading [toc] [#my-heading-id]\\n```\\n\\nTo link people to a specific heading, add the heading id to hash fragment: `/page#my-heading-id`.\\n\\n#### Tab Groups\\n\\nYou can use code blocks with the `<Tab />` component.\\n\\n````mdx\\nimport { Tab, Tabs } from 'fumadocs-ui/components/tabs';\\n\\n```ts tab=\\\"Tab 1\\\"\\nconsole.log('A');\\n```\\n\\n```ts tab=\\\"Tab 2\\\"\\nconsole.log('B');\\n```\\n````\\n\\n> Note that you can add MDX components instead of importing them in MDX files.\\n\\n```ts tab=\\\"Tab 1\\\"\\nconsole.log('A');\\n```\\n\\n```ts tab=\\\"Tab 2\\\"\\nconsole.log('B');\\n```\\n\\n### Include\\n\\nReference another file (can also be a Markdown/MDX document).\\nSpecify the target file path in `<include>` tag (relative to the MDX file itself).\\n\\n```mdx title=\\\"page.mdx\\\"\\n<include>./another.mdx</include>\\n```\\n\\nSee [other usages](https://fumadocs.dev/docs/mdx/include).\\n\\n### Mermaid\\n\\nRendering diagrams in your docs\\n\\n```mdx\\n<Mermaid\\n  chart=\\\"\\ngraph TD;\\nsubgraph AA [Consumers]\\nA[Mobile app];\\nB[Web app];\\nC[Node.js client];\\nend\\nsubgraph BB [Services]\\nE[REST API];\\nF[GraphQL API];\\nG[SOAP API];\\nend\\nZ[GraphQL API];\\nA --> Z;\\nB --> Z;\\nC --> Z;\\nZ --> E;\\nZ --> F;\\nZ --> G;\\\"\\n/>\\n```\\n\\n<Mermaid\\n  chart=\\\"\\ngraph TD;\\nsubgraph AA [Consumers]\\nA[Mobile app];\\nB[Web app];\\nC[Node.js client];\\nend\\nsubgraph BB [Services]\\nE[REST API];\\nF[GraphQL API];\\nG[SOAP API];\\nend\\nZ[GraphQL API];\\nA --> Z;\\nB --> Z;\\nC --> Z;\\nZ --> E;\\nZ --> F;\\nZ --> G;\\\"\\n/>\\n\\n### LaTeX\\n\\nFumadocs supports [LaTeX](https://www.latex-project.org) through the Latex component.\\n\\n````mdx\\nInline: $$c = \\\\pm\\\\sqrt{a^2 + b^2}$$\\n\\n```math\\nc = \\\\pm\\\\sqrt{a^2 + b^2}\\n```\\n````\\n\\nInline: $$c = \\\\pm\\\\sqrt{a^2 + b^2}$$\\n\\n```math\\nc = \\\\pm\\\\sqrt{a^2 + b^2}\\n```\\n\\nTaylor Expansion (expressing holomorphic function $$f(x)$$ in power series):\\n\\n```math\\n\\\\displaystyle {\\\\begin{aligned}T_{f}(z)&=\\\\sum _{k=0}^{\\\\infty }{\\\\frac {(z-c)^{k}}{2\\\\pi i}}\\\\int _{\\\\gamma }{\\\\frac {f(w)}{(w-c)^{k+1}}}\\\\,dw\\\\\\\\&={\\\\frac {1}{2\\\\pi i}}\\\\int _{\\\\gamma }{\\\\frac {f(w)}{w-c}}\\\\sum _{k=0}^{\\\\infty }\\\\left({\\\\frac {z-c}{w-c}}\\\\right)^{k}\\\\,dw\\\\\\\\&={\\\\frac {1}{2\\\\pi i}}\\\\int _{\\\\gamma }{\\\\frac {f(w)}{w-c}}\\\\left({\\\\frac {1}{1-{\\\\frac {z-c}{w-c}}}}\\\\right)\\\\,dw\\\\\\\\&={\\\\frac {1}{2\\\\pi i}}\\\\int _{\\\\gamma }{\\\\frac {f(w)}{w-z}}\\\\,dw=f(z),\\\\end{aligned}}\\n```\\n\\n<Callout title=\\\"Tip\\\">\\n  You can actually copy equations on Wikipedia, they will be converted into a KaTeX string when you paste it.\\n\\n```math\\n\\\\displaystyle S[{\\\\boldsymbol {q}}]=\\\\int _{a}^{b}L(t,{\\\\boldsymbol {q}}(t),{\\\\dot {\\\\boldsymbol {q}}}(t))\\\\,dt.\\n```\\n\\n</Callout>\\n\"}, {\"info\":{\"path\":\"app\\\\essentials\\\\routing.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/essentials/routing.mdx\"},\"data\":{\"title\":\"Routing\",\"description\":\"A shared convention for organizing your documents\",\"icon\":\"Map\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nFumadocs uses a file-system based routing system to organize your documents. This allows you to create a clear and consistent structure for your documentation, making it easier for users to navigate and find the information they need.\\n\\n<Files>\\n    <Folder name=\\\"content/docs (content directory)\\\" defaultOpen>\\n        <Folder name=\\\"app\\\" defaultOpen>\\n\\n            <File name=\\\"index.mdx\\\" />\\n\\n            <File name=\\\"getting-started.mdx\\\" />\\n        </Folder>\\n        <Folder name=\\\"api-reference\\\" defaultOpen>\\n\\n            <File name=\\\"index.mdx\\\" />\\n\\n            <Folder name=\\\"events\\\" defaultOpen>\\n                <File name=\\\"get.mdx\\\" />\\n                <File name=\\\"post.json\\\" />\\n            </Folder>\\n\\n        </Folder>\\n        <Folder name=\\\"changelog\\\" defaultOpen>\\n\\n            <File name=\\\"index.mdx\\\" />\\n\\n        </Folder>\\n    </Folder>\\n\\n</Files>\\n\\n## File\\n\\nA [MDX](https://mdxjs.com) or Markdown file, you can customize its frontmatter.\\n\\n```mdx\\n---\\ntitle: My Page\\ndescription: Best document ever\\nicon: HomeIcon\\nfull: true\\n---\\n\\n## Learn More\\n```\\n\\n| name          | description                                        |\\n| ------------- | -------------------------------------------------- |\\n| `title`       | The title of page                                  |\\n| `description` | The description of page                            |\\n| `icon`        | The name of icon, see [Icons](#icons)              |\\n| `full`        | Fill all available space on the page (Fumadocs UI) |\\n\\n### Slugs\\n\\nThe slugs of a page are generated from its file path.\\n\\n| path (relative to content folder) | slugs             |\\n| --------------------------------- | ----------------- |\\n| `./dir/page.mdx`                  | `['dir', 'page']` |\\n| `./dir/index.mdx`                 | `['dir']`         |\\n\\n## Folder\\n\\nOrganize multiple pages, you can create a [Meta file](#meta) to customize folders.\\n\\n### Folder Group\\n\\nBy default, putting a file into folder will change its slugs.\\nYou can wrap the folder name in parentheses to avoid impacting the slugs of child files.\\n\\n| path (relative to content folder) | slugs      |\\n| --------------------------------- | ---------- |\\n| `./(group-name)/page.mdx`         | `['page']` |\\n\\n## Meta\\n\\nCustomize folders by creating a `meta.json` file in the folder.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"title\\\": \\\"Display Name\\\",\\n  \\\"icon\\\": \\\"MyIcon\\\",\\n  \\\"pages\\\": [\\\"index\\\", \\\"getting-started\\\"],\\n  \\\"defaultOpen\\\": true\\n}\\n```\\n\\n| name          | description                           |\\n| ------------- | ------------------------------------- |\\n| `title`       | Display name                          |\\n| `icon`        | The name of icon, see [Icons](#icons) |\\n| `pages`       | Folder items (see below)              |\\n| `defaultOpen` | Open the folder by default            |\\n\\n### Pages\\n\\nBy default, folder items are sorted alphabetically.\\n\\nYou can add or control the order of items using `pages`, items are not included unless they are listed inside.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"title\\\": \\\"Name of Folder\\\",\\n  \\\"pages\\\": [\\\"guide\\\", \\\"components\\\", \\\"---My Separator---\\\", \\\"./nested/page\\\"]\\n}\\n```\\n\\n<Files>\\n  <File name=\\\"meta.json\\\" />\\n\\n<File name=\\\"guide.mdx\\\" />\\n\\n<File name=\\\"components.mdx\\\" />\\n\\n  <File name=\\\"nested/page.mdx\\\" />\\n</Files>\\n\\n#### Rest\\n\\nAdd a `...` item to include remaining pages (sorted alphabetically), or `z...a` for descending order.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"pages\\\": [\\\"guide\\\", \\\"...\\\"]\\n}\\n```\\n\\nYou can add `!name` to prevent an item from being included.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"pages\\\": [\\\"guide\\\", \\\"...\\\", \\\"!components\\\"]\\n}\\n```\\n\\n#### Extract\\n\\nYou can extract the items from a folder with `...folder_name`.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"pages\\\": [\\\"guide\\\", \\\"...nested\\\"]\\n}\\n```\\n\\n#### Link\\n\\nUse the syntax `[Text](url)` to insert links, or `[Icon][Text](url)` to add icon.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"pages\\\": [\\n    \\\"[Vercel](https://vercel.com)\\\",\\n    \\\"[Triangle][Vercel](https://vercel.com)\\\"\\n  ]\\n}\\n```\\n\\n## Icons\\n\\nThis Fumadocs template converts the icon names to JSX elements in runtime, and renders it as a component.\\n\\n## Root Folder\\n\\nMarks the folder as a root folder, only items in the opened root folder will be considered.\\n\\n```json title=\\\"meta.json\\\"\\n{\\n  \\\"title\\\": \\\"Name of Folder\\\",\\n  \\\"description\\\": \\\"The description of root folder (optional)\\\",\\n  \\\"root\\\": true\\n}\\n```\\n\\nFor example, when you are opening a root folder `framework`, the other folders (e.g. `headless`) are not shown on the sidebar and other navigation elements.\\n\\n<Files>\\n  <Folder name=\\\"framework\\\" defaultOpen>\\n    <File name=\\\"index.mdx\\\" />\\n\\n    <File name=\\\"current-page.mdx\\\" className=\\\"!text-fd-primary !bg-fd-primary/10\\\" />\\n\\n    <File name=\\\"other-pages.mdx\\\" />\\n\\n  </Folder>\\n\\n  <Folder name=\\\"headless (hidden)\\\" className=\\\"opacity-50\\\" disabled defaultOpen>\\n    <File name=\\\"my-page.mdx\\\" />\\n  </Folder>\\n</Files>\\n\\n<Callout title=\\\"Fumadocs UI\\\">\\n  Fumadocs UI renders root folders as [Sidebar\\n  Tabs](https://fumadocs.dev/docs/ui/navigation/sidebar#sidebar-tabs), which\\n  allows user to switch between them.\\n</Callout>\\n\"}, {\"info\":{\"path\":\"app\\\\features\\\\ai-search.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/features/ai-search.mdx\"},\"data\":{\"title\":\"AI Search\",\"description\":\"Configure and use AI-powered search in your documentation\",\"icon\":\"Search\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nThis template comes with built-in AI-powered search capabilities. Although this feature is enabled by default, you’ll need to configure an API key for full functionality.\\n\\n## Setting Up the API Key\\n\\nTo activate AI search, add your OpenAI API key to the `.env` file at the root of your project. If you don’t already have one, you can generate a key by signing up at the [OpenAI platform](https://platform.openai.com/signup).\\n\\n```bash\\nOPENAI_API_KEY=your_api_key_here\\n```\\n\\n## Application Configuration\\n\\nThe logic for AI search is pre-configured in the `components/fumadocs/ai` directory. You can explore or modify the implementation there to suit your needs.\\n\\n## How Queries Are Handled\\n\\nAI Search uses the OpenAI API to process user queries. Instead of Retrieval-Augmented Generation (RAG), it relies on Web Search for external information.\\n\\n> Tip: You can replace the default OpenAI integration with a custom setup (such as [Inkeep](https://www.inkeep.com/)) for a more efficient Retrieval-Augmented Generation (RAG) implementation tailored to your content and infrastructure.\\n\"}, {\"info\":{\"path\":\"app\\\\features\\\\async-mode.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/features/async-mode.mdx\"},\"data\":{\"title\":\"Async Mode\",\"description\":\"Runtime compilation of content files.\",\"icon\":\"Clock\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nBy default, all Markdown and MDX files must be precompiled, even when running the development server. This requirement can increase startup time for large documentation sites.\\n\\nTo improve performance, this template enables **Async Mode** by default, allowing content files to be compiled at runtime instead.\\n\\n## Constraints\\n\\nAsync Mode introduces some limitations to MDX features:\\n\\n- **No import/export statements** are allowed inside MDX files. If you need to use custom components, pass them through the `components` prop instead.\\n- **Images must be referenced using URLs** (e.g. `/images/test.png`). Avoid relative file paths like `./image.png`. Place your images inside the `public` folder and reference them via their public URL path.\\n\"}, {\"info\":{\"path\":\"app\\\\features\\\\llms.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/features/llms.mdx\"},\"data\":{\"title\":\"LLM Support\",\"description\":\"Provide AI-friendly endpoints to assist large language models\",\"icon\":\"Bot\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nLarge language models waste tokens crawling HTML, JS, and site chrome. By shipping a few simple endpoints, you can provide a more efficient way for LLMs to access your documentation.\\n\\n1. **`/llms.txt`**: a concise sitemap with titles, URLs, and summaries\\n2. **`/llms-full.txt`**: a full Markdown dump of every document\\n3. **`/llms.mdx/*`**: the raw MDX/Markdown content of a requested page\\n\\n## `/llms.txt`\\n\\n- Lists each section and page in your documentation\\n- Includes optional one-line descriptions from frontmatter\\n- No boilerplate or styling—just the outline\\n\\n```md\\n# Docs\\n\\n## API Reference\\n\\n- [Get museum hours](/docs/api-reference/operations/museum-hours): No description available\\n- [Buy museum tickets](/docs/api-reference/tickets): No description available\\n\\n## UI Framework\\n\\n- [Quickstart](/docs/app/quickstart): Start building awesome documentation in under 5 minutes\\n```\\n\\nThis file is generated at the root of your deployed site, enabling agents to discover it at `https://<your-domain>/llms.txt`.\\n\\n## `/llms-full.txt`\\n\\n- Concatenates the raw MDX/Markdown of every page\\n- Preserves headings, paragraphs, code samples, and frontmatter\\n- Lets an LLM ingest your entire documentation corpus in a single fetch\\n\\n```md\\n# API Reference: Get museum hours\\n\\nURL: /docs/api-reference/operations/museum-hours\\nSource: content/docs/api-reference/operations/museum-hours.mdx\\n\\n> No description available\\n\\n## Operation: GET /museum-hours\\n\\n...full MDX content...\\n```\\n\\nThis file is generated at build time by globbing `content/docs/**/*.mdx` and joining files in sidebar order.\\n\\n## `/llms.mdx/*`\\n\\n- Provides the raw MDX/Markdown content of a requested page\\n- Preserves headings, paragraphs, code samples, and frontmatter\\n\\nExample: `/llms.mdx/app`\\n\\n```md\\n# App Framework: Introduction\\n\\nURL: /docs/app\\nSource: https://raw.githubusercontent.com/techwithanirudh/fumadocs-starter/main/content/docs/app/index.mdx\\n\\nWelcome to your new documentation.\\n\\n## Getting Started\\n\\nThe first step to creating amazing documentation is setting up your editing environment.\\n...\\n```\\n\"}, {\"info\":{\"path\":\"app\\\\features\\\\openapi.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/features/openapi.mdx\"},\"data\":{\"title\":\"OpenAPI\",\"description\":\"Generate and document your OpenAPI schema\",\"icon\":\"Webhook\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nFumadocs provides an official OpenAPI integration to generate and document your OpenAPI schema.\\n\\n## Features\\n\\nThe official OpenAPI integration supports:\\n\\n- Basic API endpoint information\\n- Interactive API playground\\n- Example code to send request (in different programming languages)\\n- Response samples and TypeScript definitions\\n- Request parameters and body generated from schemas\\n\\n## Changing the OpenAPI schema\\n\\nThe `generate-docs` script uses the OpenAPI schema to generate the documentation. You can change the OpenAPI schema by modifying the `openapi.json` file in `content/docs/api-reference` of your project.\\n\\n```js title=\\\"scripts/generate-docs.mts\\\"\\nimport * as OpenAPI from 'fumadocs-openapi';\\nimport { rimraf } from 'rimraf';\\n\\nexport async function generateDocs() {\\n  await rimraf('./content/docs/api-reference', {\\n    filter(v) {\\n      return (\\n        !v.endsWith('index.mdx') &&\\n        !v.endsWith('openapi.json') &&\\n        !v.endsWith('openapi.yml') &&\\n        !v.endsWith('meta.json')\\n      );\\n    },\\n  });\\n\\n  await Promise.all([\\n    OpenAPI.generateFiles({\\n      input: ['./content/docs/api-reference/openapi.yml'], // [!code highlight]\\n      output: './content/docs/api-reference',\\n      per: 'operation',\\n      includeDescription: true,\\n      groupBy: 'tag',\\n    }),\\n  ]);\\n}\\n```\\n\\n> Only OpenAPI 3.0 and 3.1 are supported.\\n\\nGenerate docs with the script:\\n\\n```bash\\nbun run build:pre\\n```\\n\"}, {\"info\":{\"path\":\"app\\\\guides\\\\adding-a-root-folder.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/guides/adding-a-root-folder.mdx\"},\"data\":{\"title\":\"Adding a Root Folder\",\"description\":\"Learn how to add a new root folder to your documentation\",\"icon\":\"FolderCheck\",\"index\":false},\"content\":\"\\n## Introduction\\n\\nFumadocs allows you to create new root folders in your documentation. This helps you organize your docs into clear sections or categories. This guide will walk you through adding a new root folder step by step.\\n\\n## Steps to Add a New Root Folder\\n\\n<div className='fd-steps [&_h3]:fd-step'>\\n\\n### Step 1: Create a New Folder\\n\\nCreate a new folder in the `content/docs` directory. You can name it anything; this example uses `cli`.\\n\\n```bash\\nmkdir content/docs/cli\\n```\\n\\n### Step 2: Update the `meta.json` File\\n\\nOpen the `meta.json` file located in the `content/docs` directory. Add your new folder name to the `pages` array:\\n\\n```json\\n{\\n  \\\"pages\\\": [\\n    \\\"app\\\",\\n    \\\"api-reference\\\",\\n    \\\"changelog\\\",\\n    \\\"cli\\\" // [!code ++]\\n  ]\\n}\\n```\\n\\n### Step 3: Update the Homepage\\n\\nOpen `page.tsx` in the `app/(home)` directory and add a new `DocumentationItem`:\\n\\n```tsx\\n<div className=\\\"mt-8 grid grid-cols-1 gap-4 text-left md:grid-cols-2\\\">\\n  <DocumentationItem\\n    title=\\\"Documentation\\\"\\n    description=\\\"Get started with the Fumadocs framework.\\\"\\n    icon={BookIcon}\\n    id=\\\"app\\\"\\n    href=\\\"/docs/app\\\"\\n  />\\n\\n  <DocumentationItem\\n    title=\\\"API Reference\\\"\\n    description=\\\"Explore Fumadocs's API reference.\\\"\\n    icon={RocketIcon}\\n    id=\\\"api-reference\\\"\\n    href=\\\"/docs/api-reference\\\"\\n  />\\n\\n  <DocumentationItem\\n    title=\\\"CLI\\\" {/* [!code ++] */}\\n    description=\\\"Learn how to use Fumadocs's CLI.\\\" {/* [!code ++] */}\\n    icon={ChevronRightIcon} {/* [!code ++] */}\\n    id=\\\"cli\\\" {/* [!code ++] */}\\n    href=\\\"/docs/cli\\\" {/* [!code ++] */}\\n  /> {/* [!code ++] */}\\n</div>\\n```\\n\\n### Step 4: Update Colors (Optional)\\n\\nEdit `globals.css` in the `styles` directory to define colors for your new folder:\\n\\n```css\\n@layer base {\\n  :root {\\n    --app-color: hsl(220deg 91% 54%);\\n    --api-reference-color: hsl(250 80% 54%);\\n    --changelog-color: var(--color-fd-foreground);\\n    --cli-color: hsl(120 100% 54%); /* [!code ++] */\\n  }\\n\\n  .dark {\\n    --app-color: hsl(217deg 92% 76%);\\n    --api-reference-color: hsl(250 100% 80%);\\n    --changelog-color: var(--color-fd-foreground);\\n    --cli-color: hsl(120 100% 80%); /* [!code ++] */\\n  }\\n}\\n```\\n\\nUpdate the base styles to apply the new color:\\n\\n```css\\n@layer base {\\n  body {\\n    overscroll-behavior-y: none;\\n    background-color: var(--color-fd-background);\\n  }\\n\\n  .app {\\n    --color-fd-primary: var(--app-color) !important;\\n  }\\n\\n  .api-reference {\\n    --color-fd-primary: var(--api-reference-color) !important;\\n  }\\n\\n  .changelog {\\n    --color-fd-primary: var(--changelog-color) !important;\\n  }\\n\\n  .cli {\\n    /* [!code ++] */\\n    --color-fd-primary: var(--cli-color) !important; /* [!code ++] */\\n  } /* [!code ++] */\\n}\\n```\\n\\n### Step 5: Create `meta.json` in the New Folder\\n\\nCreate a `meta.json` file in your new `cli` folder:\\n\\n```bash\\ntouch content/docs/cli/meta.json\\n```\\n\\nAdd the following content to `meta.json`:\\n\\n```json\\n{\\n  // [!code ++]\\n  \\\"title\\\": \\\"CLI\\\", // [!code ++]\\n  \\\"description\\\": \\\"Learn how to use the Fumadocs CLI\\\", // [!code ++]\\n  \\\"root\\\": true, // [!code ++]\\n  \\\"icon\\\": \\\"ChevronRight\\\", // [!code ++]\\n  \\\"pages\\\": [\\\"---Getting Started---\\\", \\\"index\\\", \\\"...\\\"] // [!code ++]\\n} // [!code ++]\\n```\\n\\nThis file defines the metadata for your new folder, including its title, description, and icon.\\n\\n### Step 6: Create a New Page\\n\\nCreate an `index.mdx` page in your new `cli` folder:\\n\\n```bash\\ntouch content/docs/cli/index.mdx\\n```\\n\\nAdd initial content to `index.mdx`:\\n\\n```mdx\\n---\\ntitle: CLI\\ndescription: 'Learn how to use the Fumadocs CLI'\\nicon: ChevronRightIcon\\n---\\n\\n## Introduction\\n\\nThe Fumadocs CLI is a command-line tool for managing your documentation. It helps you create, build, and deploy your docs.\\n\\n## Installation\\n\\n...\\n```\\n\\n</div>\\n\\nThat's it! You've successfully added a new root folder to your documentation. Now, navigate to your docs website to view your new folder and content.\\n\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\operations\\\\museum-hours.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/operations/museum-hours.mdx\"},\"data\":{\"title\":\"Get museum hours\",\"full\":true,\"_openapi\":{\"method\":\"GET\",\"route\":\"/museum-hours\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Get upcoming museum operating hours.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nGet upcoming museum operating hours.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/museum-hours\\\",\\\"method\\\":\\\"get\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\tickets\\\\tickets.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/tickets/tickets.mdx\"},\"data\":{\"title\":\"Buy museum tickets\",\"full\":true,\"_openapi\":{\"method\":\"POST\",\"route\":\"/tickets\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Purchase museum tickets for general entry or special events.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nPurchase museum tickets for general entry or special events.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/tickets\\\",\\\"method\\\":\\\"post\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events\\\\special-events\\\\get.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events/special-events/get.mdx\"},\"data\":{\"title\":\"List special events\",\"full\":true,\"_openapi\":{\"method\":\"GET\",\"route\":\"/special-events\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Return a list of upcoming special events at the museum.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nReturn a list of upcoming special events at the museum.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/special-events\\\",\\\"method\\\":\\\"get\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events\\\\special-events\\\\post.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events/special-events/post.mdx\"},\"data\":{\"title\":\"Create special events\",\"full\":true,\"_openapi\":{\"method\":\"POST\",\"route\":\"/special-events\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Creates a new special event for the museum.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nCreates a new special event for the museum.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/special-events\\\",\\\"method\\\":\\\"post\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events\\\\special-events\\\\eventid\\\\delete.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events/special-events/eventid/delete.mdx\"},\"data\":{\"title\":\"Delete special event\",\"full\":true,\"_openapi\":{\"method\":\"DELETE\",\"route\":\"/special-events/{eventId}\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Delete a special event from the collection. Allows museum to cancel planned events.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nDelete a special event from the collection. Allows museum to cancel planned events.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/special-events/{eventId}\\\",\\\"method\\\":\\\"delete\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events\\\\special-events\\\\eventid\\\\get.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events/special-events/eventid/get.mdx\"},\"data\":{\"title\":\"Get special event\",\"full\":true,\"_openapi\":{\"method\":\"GET\",\"route\":\"/special-events/{eventId}\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Get details about a special event.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nGet details about a special event.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/special-events/{eventId}\\\",\\\"method\\\":\\\"get\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\events\\\\special-events\\\\eventid\\\\patch.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/events/special-events/eventid/patch.mdx\"},\"data\":{\"title\":\"Update special event\",\"full\":true,\"_openapi\":{\"method\":\"PATCH\",\"route\":\"/special-events/{eventId}\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Update the details of a special event.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nUpdate the details of a special event.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/special-events/{eventId}\\\",\\\"method\\\":\\\"patch\\\"}]} webhooks={[]} hasHead={false} />\"}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\tickets\\\\tickets\\\\ticketid\\\\qr.mdx\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/tickets/tickets/ticketid/qr.mdx\"},\"data\":{\"title\":\"Get ticket QR code\",\"full\":true,\"_openapi\":{\"method\":\"GET\",\"route\":\"/tickets/{ticketId}/qr\",\"toc\":[],\"structuredData\":{\"headings\":[],\"contents\":[{\"content\":\"Return an image of your ticket with scannable QR code. Used for event entry.\"}]}},\"index\":false},\"content\":\"\\n{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}\\n\\nReturn an image of your ticket with scannable QR code. Used for event entry.\\n\\n<APIPage document={\\\"./content/docs/api-reference/openapi.yml\\\"} operations={[{\\\"path\\\":\\\"/tickets/{ticketId}/qr\\\",\\\"method\\\":\\\"get\\\"}]} webhooks={[]} hasHead={false} />\"}], [{\"info\":{\"path\":\"meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/meta.json\"},\"data\":{\"pages\":[\"app\",\"api-reference\",\"changelog\"]}}, {\"info\":{\"path\":\"api-reference\\\\meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/meta.json\"},\"data\":{\"title\":\"API Reference\",\"pages\":[\"---API Reference---\",\"index\",\"...(generated)\"],\"description\":\"A demo for Fumadocs OpenAPI\",\"root\":true,\"icon\":\"Rocket\"}}, {\"info\":{\"path\":\"app\\\\meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/meta.json\"},\"data\":{\"title\":\"Documentation\",\"pages\":[\"---Getting Started---\",\"index\",\"quickstart\",\"---Essentials---\",\"...essentials\",\"---Features---\",\"...features\",\"---Guides---\",\"...guides\",\"...\"],\"description\":\"Set up your documentation\",\"root\":true,\"icon\":\"Book\"}}, {\"info\":{\"path\":\"changelog\\\\meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/changelog/meta.json\"},\"data\":{\"title\":\"Changelog\",\"pages\":[\"---Changelog---\",\"index\",\"...\"],\"description\":\"Updates and changes\",\"root\":true,\"icon\":\"History\"}}, {\"info\":{\"path\":\"api-reference\\\\(generated)\\\\meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/(generated)/meta.json\"},\"data\":{\"pages\":[\"---Events---\",\"...events\",\"---Tickets---\",\"...tickets\",\"---Operations---\",\"...operations\",\"---Other---\",\"...\"]}}, {\"info\":{\"path\":\"app\\\\essentials\\\\meta.json\",\"absolutePath\":\"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/app/essentials/meta.json\"},\"data\":{\"pages\":[\"markdown\",\"code\",\"routing\",\"...\"]}}], \"docs\", _sourceConfig)"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AACpC;AAAA;AAGA;;;;;;AAFA,MAAM,gBAAgB,IAAA,6KAAW,EAAC;;AAG3B,MAAM,OAAO,8LAAa,CAAC,IAAI,CAAsB;IAAC;QAAC,QAAO;YAAC,QAAO;YAA2B,gBAAe;QAAiH;QAAE,QAAO;YAAC,SAAQ;YAAe,eAAc;YAAsD,QAAO;YAAO,SAAQ;QAAK;QAAE,WAAU;IAA2F;IAAG;QAAC,QAAO;YAAC,QAAO;YAAiB,gBAAe;QAAuG;QAAE,QAAO;YAAC,SAAQ;YAAe,eAAc;YAAoC,QAAO;YAAO,SAAQ;QAAK;QAAE,WAAU;IAAo9C;IAAG;QAAC,QAAO;YAAC,QAAO;YAAsB,gBAAe;QAA4G;QAAE,QAAO;YAAC,SAAQ;YAAa,eAAc;YAA0D,QAAO;YAAO,SAAQ;QAAK;QAAE,WAAU;IAAmqG;IAAG;QAAC,QAAO;YAAC,QAAO;YAAuB,gBAAe;QAA6G;QAAE,QAAO;YAAC,SAAQ;YAAkB,eAAc;YAA+B,QAAO;YAAO,SAAQ;QAAK;QAAE,WAAU;IAAmnJ;IAAG;QAAC,QAAO;YAAC,QAAO;YAAyC,gBAAe;QAA8H;QAAE,QAAO;YAAC,SAAQ;YAA0B,QAAO;YAAK,YAAW;gBAAC,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW,EAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAyW;IAAG;QAAC,QAAO;YAAC,QAAO;YAA4B,gBAAe;QAAiH;QAAE,QAAO;YAAC,SAAQ;YAAc,eAAc;YAAsC,QAAO;YAAO,SAAQ;QAAK;QAAE,WAAU;IAA20F;IAAG;QAAC,QAAO;YAAC,QAAO;YAAgC,gBAAe;QAAqH;QAAE,QAAO;YAAC,SAAQ;YAAkB,eAAc;YAAgD,QAAO;YAAa,SAAQ;QAAK;QAAE,WAAU;IAA8nM;IAAG;QAAC,QAAO;YAAC,QAAO;YAA+B,gBAAe;QAAoH;QAAE,QAAO;YAAC,SAAQ;YAAU,eAAc;YAAoD,QAAO;YAAM,SAAQ;QAAK;QAAE,WAAU;IAAysK;IAAG;QAAC,QAAO;YAAC,QAAO;YAA+B,gBAAe;QAAoH;QAAE,QAAO;YAAC,SAAQ;YAAY,eAAc;YAA4D,QAAO;YAAS,SAAQ;QAAK;QAAE,WAAU;IAA+lC;IAAG;QAAC,QAAO;YAAC,QAAO;YAAgC,gBAAe;QAAqH;QAAE,QAAO;YAAC,SAAQ;YAAa,eAAc;YAAwC,QAAO;YAAQ,SAAQ;QAAK;QAAE,WAAU;IAAqwB;IAAG;QAAC,QAAO;YAAC,QAAO;YAA0B,gBAAe;QAA+G;QAAE,QAAO;YAAC,SAAQ;YAAc,eAAc;YAAgE,QAAO;YAAM,SAAQ;QAAK;QAAE,WAAU;IAAgmE;IAAG;QAAC,QAAO;YAAC,QAAO;YAA6B,gBAAe;QAAkH;QAAE,QAAO;YAAC,SAAQ;YAAU,eAAc;YAA4C,QAAO;YAAU,SAAQ;QAAK;QAAE,WAAU;IAA08C;IAAG;QAAC,QAAO;YAAC,QAAO;YAAwC,gBAAe;QAA6H;QAAE,QAAO;YAAC,SAAQ;YAAuB,eAAc;YAA2D,QAAO;YAAc,SAAQ;QAAK;QAAE,WAAU;IAAs8H;IAAG;QAAC,QAAO;YAAC,QAAO;YAA2D,gBAAe;QAA+I;QAAE,QAAO;YAAC,SAAQ;YAAmB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAM,SAAQ;gBAAgB,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAAsC;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAgW;IAAG;QAAC,QAAO;YAAC,QAAO;YAAmD,gBAAe;QAAuI;QAAE,QAAO;YAAC,SAAQ;YAAqB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAO,SAAQ;gBAAW,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAA8D;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAoX;IAAG;QAAC,QAAO;YAAC,QAAO;YAA8D,gBAAe;QAAiJ;QAAE,QAAO;YAAC,SAAQ;YAAsB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAM,SAAQ;gBAAkB,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAAyD;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAqX;IAAG;QAAC,QAAO;YAAC,QAAO;YAA+D,gBAAe;QAAkJ;QAAE,QAAO;YAAC,SAAQ;YAAwB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAO,SAAQ;gBAAkB,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAA6C;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAA0W;IAAG;QAAC,QAAO;YAAC,QAAO;YAA0E,gBAAe;QAA4J;QAAE,QAAO;YAAC,SAAQ;YAAuB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAS,SAAQ;gBAA4B,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAAqF;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAA8Z;IAAG;QAAC,QAAO;YAAC,QAAO;YAAuE,gBAAe;QAAyJ;QAAE,QAAO;YAAC,SAAQ;YAAoB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAM,SAAQ;gBAA4B,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAAoC;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAA0W;IAAG;QAAC,QAAO;YAAC,QAAO;YAAyE,gBAAe;QAA2J;QAAE,QAAO;YAAC,SAAQ;YAAuB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAQ,SAAQ;gBAA4B,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAAwC;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAgX;IAAG;QAAC,QAAO;YAAC,QAAO;YAAiE,gBAAe;QAAmJ;QAAE,QAAO;YAAC,SAAQ;YAAqB,QAAO;YAAK,YAAW;gBAAC,UAAS;gBAAM,SAAQ;gBAAyB,OAAM,EAAE;gBAAC,kBAAiB;oBAAC,YAAW,EAAE;oBAAC,YAAW;wBAAC;4BAAC,WAAU;wBAA8E;qBAAE;gBAAA;YAAC;YAAE,SAAQ;QAAK;QAAE,WAAU;IAAiZ;CAAE,EAAE;IAAC;QAAC,QAAO;YAAC,QAAO;YAAY,gBAAe;QAAmG;QAAE,QAAO;YAAC,SAAQ;gBAAC;gBAAM;gBAAgB;aAAY;QAAA;IAAC;IAAG;QAAC,QAAO;YAAC,QAAO;YAA2B,gBAAe;QAAiH;QAAE,QAAO;YAAC,SAAQ;YAAgB,SAAQ;gBAAC;gBAAsB;gBAAQ;aAAiB;YAAC,eAAc;YAA8B,QAAO;YAAK,QAAO;QAAQ;IAAC;IAAG;QAAC,QAAO;YAAC,QAAO;YAAiB,gBAAe;QAAuG;QAAE,QAAO;YAAC,SAAQ;YAAgB,SAAQ;gBAAC;gBAAwB;gBAAQ;gBAAa;gBAAmB;gBAAgB;gBAAiB;gBAAc;gBAAe;gBAAY;aAAM;YAAC,eAAc;YAA4B,QAAO;YAAK,QAAO;QAAM;IAAC;IAAG;QAAC,QAAO;YAAC,QAAO;YAAuB,gBAAe;QAA6G;QAAE,QAAO;YAAC,SAAQ;YAAY,SAAQ;gBAAC;gBAAkB;gBAAQ;aAAM;YAAC,eAAc;YAAsB,QAAO;YAAK,QAAO;QAAS;IAAC;IAAG;QAAC,QAAO;YAAC,QAAO;YAAwC,gBAAe;QAA6H;QAAE,QAAO;YAAC,SAAQ;gBAAC;gBAAe;gBAAY;gBAAgB;gBAAa;gBAAmB;gBAAgB;gBAAc;aAAM;QAAA;IAAC;IAAG;QAAC,QAAO;YAAC,QAAO;YAA6B,gBAAe;QAAkH;QAAE,QAAO;YAAC,SAAQ;gBAAC;gBAAW;gBAAO;gBAAU;aAAM;QAAA;IAAC;CAAE,EAAE,QAAQ", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/lib/source.ts"], "sourcesContent": ["import type { InferMetaType, InferPageType } from 'fumadocs-core/source'\nimport { loader } from 'fumadocs-core/source'\nimport { attachFile, createOpenAPI } from 'fumadocs-openapi/server'\nimport { icons } from 'lucide-react'\nimport { createElement } from 'react'\nimport { docs } from '@/.source'\n\nexport const source = loader({\n  baseUrl: '/docs',\n  icon(icon) {\n    if (icon && icon in icons)\n      return createElement(icons[icon as keyof typeof icons])\n  },\n  source: docs.toFumadocsSource(),\n  pageTree: {\n    attachFile,\n  },\n})\n\nexport const openapi = createOpenAPI({\n  proxyUrl: '/api/proxy',\n  shikiOptions: {\n    themes: {\n      dark: 'vesper',\n      light: 'vitesse-light',\n    },\n  },\n})\n\nexport type Page = InferPageType<typeof source>\nexport type Meta = InferMetaType<typeof source>\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,SAAS,IAAA,uLAAM,EAAC;IAC3B,SAAS;IACT,MAAK,IAAI;QACP,IAAI,QAAQ,QAAQ,4MAAK,EACvB,qBAAO,IAAA,wNAAa,EAAC,4MAAK,CAAC,KAA2B;IAC1D;IACA,QAAQ,2HAAI,CAAC,gBAAgB;IAC7B,UAAU;QACR,YAAA,sLAAU;IACZ;AACF;AAEO,MAAM,UAAU,IAAA,kLAAa,EAAC;IACnC,UAAU;IACV,cAAc;QACZ,QAAQ;YACN,MAAM;YACN,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/app/api/search/route.ts"], "sourcesContent": ["import { createSearchAPI } from 'fumadocs-core/search/server'\nimport { source } from '@/lib/source'\n\nexport const { GET } = createSearchAPI('advanced', {\n  indexes: await Promise.all(\n    source.getPages().map(async (page) => {\n      const { structuredData } = await page.data.load()\n\n      return {\n        title: page.data.title,\n        description: page.data.description,\n        url: page.url,\n        id: page.url,\n        structuredData,\n      }\n    })\n  ),\n})\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAEO,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,iMAAe,EAAC,YAAY;IACjD,SAAS,MAAM,QAAQ,GAAG,CACxB,gIAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO;QAC3B,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,KAAK,IAAI,CAAC,IAAI;QAE/C,OAAO;YACL,OAAO,KAAK,IAAI,CAAC,KAAK;YACtB,aAAa,KAAK,IAAI,CAAC,WAAW;YAClC,KAAK,KAAK,GAAG;YACb,IAAI,KAAK,GAAG;YACZ;QACF;IACF;AAEJ", "debugId": null}}]}