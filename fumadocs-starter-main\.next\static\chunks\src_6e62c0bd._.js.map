{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/lib/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\nimport { cva } from 'class-variance-authority'\nimport { Airplay, Moon, Sun } from 'lucide-react'\nimport { motion } from 'motion/react'\nimport { useTheme } from 'next-themes'\nimport { type HTMLAttributes, useLayoutEffect, useState } from 'react'\nimport { cn } from '@/lib/cn'\n\nconst themes = [\n  {\n    key: 'light',\n    icon: Sun,\n    label: 'Light theme',\n  },\n  {\n    key: 'dark',\n    icon: Moon,\n    label: 'Dark theme',\n  },\n  {\n    key: 'system',\n    icon: Airplay,\n    label: 'System theme',\n  },\n]\n\nconst itemVariants = cva(\n  'relative size-6.5 rounded-full p-1.5 text-fd-muted-foreground',\n  {\n    variants: {\n      active: {\n        true: 'bg-fd-accent text-fd-accent-foreground',\n        false: 'text-fd-muted-foreground',\n      },\n    },\n  }\n)\n\nexport function ThemeToggle({\n  className,\n  mode = 'light-dark',\n  ...props\n}: HTMLAttributes<HTMLElement> & {\n  mode?: 'light-dark' | 'light-dark-system'\n}) {\n  const { setTheme, theme, resolvedTheme } = useTheme()\n  const [mounted, setMounted] = useState(false)\n\n  useLayoutEffect(() => {\n    setMounted(true)\n  }, [])\n\n  const container = cn(\n    'relative inline-flex items-center rounded-full p-1 ring-1 ring-border',\n    className\n  )\n\n  if (mode === 'light-dark') {\n    const value = mounted ? resolvedTheme : null\n\n    return (\n      <button\n        className={container}\n        aria-label={`Toggle Theme`}\n        onClick={() => setTheme(value === 'light' ? 'dark' : 'light')}\n        data-theme-toggle=''\n        {...props}\n      >\n        {themes\n          // biome-ignore lint/suspicious/useIterableCallbackReturn: we intentionally skip 'system'\n          .map(({ key, icon: Icon }) => {\n            const isActive = value === key\n\n            if (key === 'system') return\n\n            return (\n              <div key={key} className={cn(itemVariants({ active: isActive }))}>\n                {isActive && (\n                  <motion.div\n                    layoutId='activeTheme'\n                    className='absolute inset-0 rounded-full bg-accent'\n                    transition={{\n                      type: 'spring',\n                      duration: 0.4,\n                    }}\n                  />\n                )}\n                <Icon className='size-full' fill='currentColor' />\n              </div>\n            )\n          })}\n      </button>\n    )\n  }\n\n  const value = mounted ? theme : null\n\n  return (\n    <div className={container} data-theme-toggle='' {...props}>\n      {themes.map(({ key, icon: Icon }) => (\n        <button\n          type='button'\n          key={key}\n          aria-label={key}\n          className={cn(itemVariants({ active: value === key }))}\n          onClick={() => setTheme(key)}\n        >\n          {value === key && (\n            <motion.div\n              layoutId='activeTheme'\n              className='absolute inset-0 rounded-full bg-accent'\n              transition={{\n                type: 'spring',\n                duration: 0.4,\n              }}\n            />\n          )}\n          <Icon className='size-full' fill='currentColor' />\n        </button>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,MAAM,SAAS;IACb;QACE,KAAK;QACL,MAAM,0MAAG;QACT,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM,6MAAI;QACV,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM,sNAAO;QACb,OAAO;IACT;CACD;AAED,MAAM,eAAe,IAAA,0KAAG,EACtB,iEACA;IACE,UAAU;QACR,QAAQ;YACN,MAAM;YACN,OAAO;QACT;IACF;AACF;AAGK,SAAS,YAAY,KAM3B;QAN2B,EAC1B,SAAS,EACT,OAAO,YAAY,EACnB,GAAG,OAGJ,GAN2B;;IAO1B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,IAAA,+JAAQ;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yLAAQ,EAAC;IAEvC,IAAA,gMAAe;uCAAC;YACd,WAAW;QACb;sCAAG,EAAE;IAEL,MAAM,YAAY,IAAA,yHAAE,EAClB,yEACA;IAGF,IAAI,SAAS,cAAc;QACzB,MAAM,QAAQ,UAAU,gBAAgB;QAExC,qBACE,6MAAC;YACC,WAAW;YACX,cAAa;YACb,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;YACrD,qBAAkB;YACjB,GAAG,KAAK;sBAER,MACC,yFAAyF;aACxF,GAAG,CAAC;oBAAC,EAAE,GAAG,EAAE,MAAM,IAAI,EAAE;gBACvB,MAAM,WAAW,UAAU;gBAE3B,IAAI,QAAQ,UAAU;gBAEtB,qBACE,6MAAC;oBAAc,WAAW,IAAA,yHAAE,EAAC,aAAa;wBAAE,QAAQ;oBAAS;;wBAC1D,0BACC,6MAAC,uMAAM,CAAC,GAAG;4BACT,UAAS;4BACT,WAAU;4BACV,YAAY;gCACV,MAAM;gCACN,UAAU;4BACZ;;;;;;sCAGJ,6MAAC;4BAAK,WAAU;4BAAY,MAAK;;;;;;;mBAXzB;;;;;YAcd;;;;;;IAGR;IAEA,MAAM,QAAQ,UAAU,QAAQ;IAEhC,qBACE,6MAAC;QAAI,WAAW;QAAW,qBAAkB;QAAI,GAAG,KAAK;kBACtD,OAAO,GAAG,CAAC;gBAAC,EAAE,GAAG,EAAE,MAAM,IAAI,EAAE;iCAC9B,6MAAC;gBACC,MAAK;gBAEL,cAAY;gBACZ,WAAW,IAAA,yHAAE,EAAC,aAAa;oBAAE,QAAQ,UAAU;gBAAI;gBACnD,SAAS,IAAM,SAAS;;oBAEvB,UAAU,qBACT,6MAAC,uMAAM,CAAC,GAAG;wBACT,UAAS;wBACT,WAAU;wBACV,YAAY;4BACV,MAAM;4BACN,UAAU;wBACZ;;;;;;kCAGJ,6MAAC;wBAAK,WAAU;wBAAY,MAAK;;;;;;;eAf5B;;;;;;;;;;;AAoBf;GApFgB;;QAO6B,+JAAQ;;;KAPrC", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/app/layout.client.tsx"], "sourcesContent": ["'use client'\n\nimport { useParams } from 'next/navigation'\nimport type { ReactNode } from 'react'\nimport { cn } from '@/lib/cn'\n\nexport function Body({\n  children,\n}: {\n  children: ReactNode\n}): React.ReactElement {\n  const mode = useMode()\n\n  return (\n    <body className={cn(mode, 'relative flex min-h-screen flex-col')}>\n      {children}\n    </body>\n  )\n}\n\nexport function useMode(): string | undefined {\n  const { slug } = useParams()\n  return Array.isArray(slug) && slug.length > 0 ? slug[0] : undefined\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAJA;;;AAMO,SAAS,KAAK,KAIpB;QAJoB,EACnB,QAAQ,EAGT,GAJoB;;IAKnB,MAAM,OAAO;IAEb,qBACE,6MAAC;QAAK,WAAW,IAAA,yHAAE,EAAC,MAAM;kBACvB;;;;;;AAGP;GAZgB;;QAKD;;;KALC;AAcT,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,kJAAS;IAC1B,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,EAAE,GAAG;AAC5D;IAHgB;;QACG,kJAAS", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/components/fumadocs/ai/index.tsx"], "sourcesContent": ["'use client'\nimport dynamic from 'next/dynamic'\nimport { type ButtonHTMLAttributes, useState } from 'react'\n\n// lazy load the dialog\nconst SearchAI = dynamic(() => import('./search'), { ssr: false })\n\n/**\n * The trigger component for AI search dialog.\n *\n * Use it like a normal button component.\n */\nexport function AISearchTrigger(\n  props: ButtonHTMLAttributes<HTMLButtonElement>\n) {\n  const [open, setOpen] = useState<boolean>()\n\n  return (\n    <>\n      {open !== undefined ? (\n        <SearchAI open={open} onOpenChange={setOpen} />\n      ) : null}\n      <button {...props} onClick={() => setOpen(true)} />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAFA;;;AAIA,uBAAuB;AACvB,MAAM,WAAW,IAAA,6KAAO,EAAC;;;;;;IAA4B,KAAK;;KAApD;AAOC,SAAS,gBACd,KAA8C;;IAE9C,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yLAAQ;IAEhC,qBACE;;YACG,SAAS,0BACR,6MAAC;gBAAS,MAAM;gBAAM,cAAc;;;;;uBAClC;0BACJ,6MAAC;gBAAQ,GAAG,KAAK;gBAAE,SAAS,IAAM,QAAQ;;;;;;;;AAGhD;GAbgB;MAAA", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/cn'\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n        xs: 'px-2 py-1.5 text-xs',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : 'button'\n\n  return (\n    <Comp\n      data-slot='button'\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6MAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/components/search.tsx"], "sourcesContent": ["'use client'\nimport { useDocsSearch } from 'fumadocs-core/search/client'\nimport {\n  SearchDialog,\n  SearchDialogClose,\n  SearchDialogContent,\n  SearchDialogHeader,\n  SearchDialogIcon,\n  SearchDialogInput,\n  SearchDialogList,\n  SearchDialogOverlay,\n  type SharedProps,\n} from 'fumadocs-ui/components/dialog/search'\nimport { I18nLabel, useI18n } from 'fumadocs-ui/contexts/i18n'\nimport { MessageCircle } from 'lucide-react'\nimport { AISearchTrigger } from './fumadocs/ai'\nimport { buttonVariants } from './ui/button'\n\nconst Empty = () => (\n  <div className='flex flex-col items-center justify-between gap-2 px-2 py-12 text-center text-sm'>\n    <I18nLabel label='searchNoResult' />\n    <AISearchTrigger\n      className={buttonVariants({\n        variant: 'ghost',\n        size: 'sm',\n        className: '!px-1 h-auto py-0.5',\n      })}\n    >\n      <MessageCircle className='size-4' />\n      Ask AI?\n    </AISearchTrigger>\n  </div>\n)\n\nexport default function DefaultSearchDialog(props: SharedProps) {\n  const { locale } = useI18n() // (optional) for i18n\n  const { search, setSearch, query } = useDocsSearch({\n    type: 'fetch',\n    locale,\n  })\n\n  return (\n    <SearchDialog\n      search={search}\n      onSearchChange={setSearch}\n      isLoading={query.isLoading}\n      {...props}\n    >\n      <SearchDialogOverlay />\n      <SearchDialogContent>\n        <SearchDialogHeader>\n          <SearchDialogIcon />\n          <SearchDialogInput />\n          <SearchDialogClose />\n        </SearchDialogHeader>\n        <SearchDialogList\n          items={query.data !== 'empty' ? query.data : null}\n          Empty={Empty}\n        />\n      </SearchDialogContent>\n    </SearchDialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAWA;AACA;AACA;AACA;;;AAhBA;;;;;;;AAkBA,MAAM,QAAQ,kBACZ,6MAAC;QAAI,WAAU;;0BACb,6MAAC,0KAAS;gBAAC,OAAM;;;;;;0BACjB,6MAAC,mKAAe;gBACd,WAAW,IAAA,uJAAc,EAAC;oBACxB,SAAS;oBACT,MAAM;oBACN,WAAW;gBACb;;kCAEA,6MAAC,4OAAa;wBAAC,WAAU;;;;;;oBAAW;;;;;;;;;;;;;KAVpC;AAgBS,SAAS,oBAAoB,KAAkB;;IAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,wKAAO,IAAG,sBAAsB;;IACnD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAA,gLAAa,EAAC;QACjD,MAAM;QACN;IACF;IAEA,qBACE,6MAAC,2LAAY;QACX,QAAQ;QACR,gBAAgB;QAChB,WAAW,MAAM,SAAS;QACzB,GAAG,KAAK;;0BAET,6MAAC,kMAAmB;;;;;0BACpB,6MAAC,kMAAmB;;kCAClB,6MAAC,iMAAkB;;0CACjB,6MAAC,+LAAgB;;;;;0CACjB,6MAAC,gMAAiB;;;;;0CAClB,6MAAC,gMAAiB;;;;;;;;;;;kCAEpB,6MAAC,+LAAgB;wBACf,OAAO,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,GAAG;wBAC7C,OAAO;;;;;;;;;;;;;;;;;;AAKjB;GA5BwB;;QACH,wKAAO;QACW,gLAAa;;;MAF5B", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client'\n\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/cn'\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot='tooltip-provider'\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot='tooltip-content'\n        sideOffset={sideOffset}\n        className={cn(\n          'fade-in-0 zoom-in-95 data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) animate-in text-balance rounded-md bg-primary px-3 py-1.5 text-primary-foreground text-xs data-[state=closed]:animate-out',\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className='z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px] bg-primary fill-primary' />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6MAAC,kLAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6MAAC;kBACC,cAAA,6MAAC,8KAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6MAAC,iLAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6MAAC,gLAAuB;kBACtB,cAAA,6MAAC,iLAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,yHAAE,EACX,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6MAAC,+KAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/src/app/providers.tsx"], "sourcesContent": ["'use client'\nimport { ProgressProvider } from '@bprogress/next/app'\nimport { RootProvider } from 'fumadocs-ui/provider'\nimport type { ReactNode } from 'react'\nimport SearchDialog from '@/components/search'\nimport { TooltipProvider } from '@/components/ui/tooltip'\n\nexport function Providers({ children }: { children: ReactNode }) {\n  return (\n    <RootProvider\n      search={{\n        SearchDialog,\n      }}\n    >\n      <ProgressProvider\n        height='2px'\n        color='var(--color-primary)'\n        options={{\n          showSpinner: false,\n        }}\n        stopDelay={1000}\n        delay={1000}\n        startOnLoad\n        shallowRouting\n      >\n        <TooltipProvider>{children}</TooltipProvider>\n      </ProgressProvider>\n    </RootProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AACA;AALA;;;;;;AAOO,SAAS,UAAU,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;IACxB,qBACE,6MAAC,8LAAY;QACX,QAAQ;YACN,cAAA,0IAAY;QACd;kBAEA,cAAA,6MAAC,yKAAgB;YACf,QAAO;YACP,OAAM;YACN,SAAS;gBACP,aAAa;YACf;YACA,WAAW;YACX,OAAO;YACP,WAAW;YACX,cAAc;sBAEd,cAAA,6MAAC,yJAAe;0BAAE;;;;;;;;;;;;;;;;AAI1B;KAtBgB", "debugId": null}}]}