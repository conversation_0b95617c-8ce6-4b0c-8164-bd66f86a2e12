/**
 * Create an extension for `mdast-util-from-markdown` to enable MDX.js ESM in
 * markdown.
 *
 * When using the micromark syntax extension with `addResult`, nodes will have
 * a `data.estree` field set to an ESTree [`Program`][program] node.
 *
 * @returns {FromMarkdownExtension}
 *   Extension for `mdast-util-from-markdown` to enable MDX.js ESM.
 */
export function mdxjsEsmFromMarkdown(): FromMarkdownExtension;
/**
 * Create an extension for `mdast-util-to-markdown` to enable MDX.js ESM in
 * markdown.
 *
 * @returns {ToMarkdownExtension}
 *   Extension for `mdast-util-to-markdown` to enable MDX.js ESM.
 */
export function mdxjsEsmToMarkdown(): ToMarkdownExtension;
export type CompileContext = import('mdast-util-from-markdown').CompileContext;
export type FromMarkdownExtension = import('mdast-util-from-markdown').Extension;
export type FromMarkdownHandle = import('mdast-util-from-markdown').Handle;
export type ToMarkdownHandle = import('mdast-util-to-markdown').Handle;
export type ToMarkdownExtension = import('mdast-util-to-markdown').Options;
export type MdxjsEsm = import('../index.js').MdxjsEsm;
