{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "dictionaries": ["software-terms", "npm", "fullstack"], "files": ["**", ".vscode/**", ".github/**"], "ignorePaths": ["pnpm-lock.yaml", "bun.lock", "bun.lockb"], "ignoreRegExpList": ["api<PERSON>ey='[a-zA-Z0-9-]{32}'"], "import": ["@cspell/dict-bash/cspell-ext.json"], "useGitignore": true, "version": "0.2", "words": ["<PERSON><PERSON><PERSON><PERSON>", "techwith<PERSON><PERSON><PERSON>", "fumadocs", "shiki", "shi<PERSON><PERSON><PERSON>", "turbopack", "remark", "katex", "lefthook", "orama", "<PERSON><PERSON><PERSON><PERSON>", "inkeep", "redocly", "devcontainers", "twoslash", "llms", "unrs", "jsxs", "Arrrrr", "seaaa", "whoosits", "hewwo", "infty", "boldsymbol", "stackoverflow", "vitesse", "xlink", "linecap", "linejoin", "scira", "subsecond", "activedescendant", "noopener", "bi<PERSON>r", "wordcount", "tabout", "autoremove", "chsh", "noninteractive", "bunx", "bprogress", "contentlayer", "basehub"]}