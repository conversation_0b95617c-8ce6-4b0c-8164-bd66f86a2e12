{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/mdx-js/recma/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/estree": "^1.0.0", "esast-util-from-js": "^2.0.0", "unified": "^11.0.0", "vfile": "^6.0.0"}, "description": "recma plugin to parse JavaScript", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "homepage": "https://github.com/mdx-js/recma", "keywords": ["abstract", "ast", "javascript", "parse", "plugin", "recma-plugin", "recma", "syntax", "tree", "unified"], "license": "MIT", "name": "recma-parse", "repository": "https://github.com/mdx-js/recma/tree/main/packages/recma-parse", "scripts": {}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}, "type": "module", "version": "1.0.0"}