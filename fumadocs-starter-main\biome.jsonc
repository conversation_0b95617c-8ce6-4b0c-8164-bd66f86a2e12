{"$schema": "node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**", "!node_modules", "!**/.next", "!**/dist", "!**/public", "!.github", "!.vercel", "!pnpm-lock.yaml", "!bun.lock", "!**/*.md", "!**/*.mdx", "!components/ui", "!**/*.css", "!**/*.grit"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineEnding": "lf", "indentWidth": 2, "lineWidth": 80}, "linter": {"enabled": true, "rules": {"a11y": {"noSvgWithoutTitle": "off", "useGenericFontNames": "off"}, "correctness": {"noUnusedImports": {"fix": "safe", "level": "info"}, "useUniqueElementIds": "warn"}, "suspicious": {"noArrayIndexKey": "off", "noDoubleEquals": {"fix": "safe", "level": "warn", "options": {}}}, "style": {"noNonNullAssertion": "off", "useSelfClosingElements": {"fix": "safe", "level": "info", "options": {}}, "useTemplate": {"fix": "safe", "level": "info"}}, "nursery": {"useSortedClasses": {"fix": "safe", "level": "info", "options": {"functions": ["cn"]}}}}}, "css": {"linter": {"enabled": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "parser": {"cssModules": true}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded", "trailingCommas": "es5", "jsxQuoteStyle": "single"}}, "json": {"parser": {"allowComments": true}}}