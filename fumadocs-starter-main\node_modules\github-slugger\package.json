{"name": "github-slugger", "description": "Generate a slug just like GitHub does for markdown headings.", "version": "2.0.0", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://wooorm.com)"], "bugs": {"url": "https://github.com/Flet/github-slugger/issues"}, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js", "regex.d.ts", "regex.js"], "devDependencies": {"@octokit/rest": "^19.0.0", "@types/regenerate": "^1.0.0", "@types/tape": "^4.0.0", "@unicode/unicode-13.0.0": "^1.0.0", "c8": "^7.0.0", "hast-util-select": "^5.0.0", "mdast-util-gfm": "^2.0.0", "mdast-util-to-markdown": "^1.0.0", "node-fetch": "^3.0.0", "regenerate": "^1.0.0", "rehype-parse": "^8.0.0", "rimraf": "^3.0.0", "standard": "*", "tap-spec": "^5.0.0", "tape": "^5.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "unified": "^10.0.0"}, "homepage": "https://github.com/Flet/github-slugger", "keywords": ["anchor", "github", "hash", "heading", "markdown", "slug", "slugger", "url"], "license": "ISC", "repository": {"type": "git", "url": "https://github.com/Flet/github-slugger.git"}, "scripts": {"prepack": "npm run build && npm run format", "build": "rimraf \"{script,test}/**/*.d.ts\" \"*.d.ts\" && tsc && type-coverage", "format": "standard --fix", "test-api": "tape test | tap-spec", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}}