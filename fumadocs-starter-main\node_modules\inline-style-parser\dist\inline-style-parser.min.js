!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).InlineStyleParser=n()}(this,(function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var n=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,r=/\n/g,t=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,u=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,c=/^[;\s]*/,f=/^\s+|\s+$/g,a="";function s(e){return e?e.replace(f,a):a}return e((function(e,f){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];f=f||{};var l=1,p=1;function h(e){var n=e.match(r);n&&(l+=n.length);var t=e.lastIndexOf("\n");p=~t?e.length-t:p+e.length}function m(){var e={line:l,column:p};return function(n){return n.position=new d(e),y(),n}}function d(e){this.start=e,this.end={line:l,column:p},this.source=f.source}function v(n){var r=new Error(f.source+":"+l+":"+p+": "+n);if(r.reason=n,r.filename=f.source,r.line=l,r.column=p,r.source=e,!f.silent)throw r}function g(n){var r=n.exec(e);if(r){var t=r[0];return h(t),e=e.slice(t.length),r}}function y(){g(t)}function w(e){var n;for(e=e||[];n=A();)!1!==n&&e.push(n);return e}function A(){var n=m();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var r=2;a!=e.charAt(r)&&("*"!=e.charAt(r)||"/"!=e.charAt(r+1));)++r;if(r+=2,a===e.charAt(r-1))return v("End of comment missing");var t=e.slice(2,r-2);return p+=2,h(t),e=e.slice(r),p+=2,n({type:"comment",comment:t})}}function b(){var e=m(),r=g(o);if(r){if(A(),!g(i))return v("property missing ':'");var t=g(u),f=e({type:"declaration",property:s(r[0].replace(n,a)),value:t?s(t[0].replace(n,a)):a});return g(c),f}}return d.prototype.content=e,y(),function(){var e,n=[];for(w(n);e=b();)!1!==e&&(n.push(e),w(n));return n}()}))}));
//# sourceMappingURL=inline-style-parser.min.js.map
