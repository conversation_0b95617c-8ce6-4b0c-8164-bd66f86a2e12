var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/search/route.js")
R.c("server/chunks/[root-of-the-server]__07f285ae._.js")
R.c("server/chunks/node_modules_next_dist_6b293c3d._.js")
R.c("server/chunks/node_modules_@opentelemetry_api_build_esm_7e8f41ca._.js")
R.c("server/chunks/node_modules_fumadocs-core_dist_2f3f7091._.js")
R.c("server/chunks/node_modules_@orama_orama_dist_esm_f85fe30e._.js")
R.c("server/chunks/node_modules_@scalar_openapi-parser_dist_80ba5fcc._.js")
R.c("server/chunks/292d6_yaml_dist_5cfd25a6._.js")
R.c("server/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_5816509f._.js")
R.c("server/chunks/node_modules_lucide-react_dist_esm_icons_index_3f2f8181.js")
R.c("server/chunks/node_modules_lucide-react_dist_esm_icons_e41b68cb._.js")
R.c("server/chunks/node_modules_lucide-react_dist_esm_e1bddda1._.js")
R.c("server/chunks/node_modules_acorn_dist_02865a39._.js")
R.c("server/chunks/node_modules_micromark-core-commonmark_dev_lib_1c8db1e0._.js")
R.c("server/chunks/node_modules_zod_v4_eb797a01._.js")
R.c("server/chunks/node_modules_parse5_dist_40c6d876._.js")
R.c("server/chunks/node_modules_katex_dist_katex_mjs_70e0d7c1._.js")
R.c("server/chunks/node_modules_@shikijs_de019fdf._.js")
R.c("server/chunks/node_modules_7a146756._.js")
R.c("server/chunks/[root-of-the-server]__52f88d30._.js")
R.m("[project]/.next-internal/server/app/api/search/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/search/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/search/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
