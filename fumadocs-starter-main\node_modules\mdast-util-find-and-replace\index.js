/**
 * @typedef {import('./lib/index.js').FindAndReplaceList} FindAndReplaceList
 * @typedef {import('./lib/index.js').FindAndReplaceTuple} FindAndReplaceTuple
 * @typedef {import('./lib/index.js').Find} Find
 * @typedef {import('./lib/index.js').Options} Options
 * @typedef {import('./lib/index.js').RegExpMatchObject} RegExpMatchObject
 * @typedef {import('./lib/index.js').ReplaceFunction} ReplaceFunction
 * @typedef {import('./lib/index.js').Replace} Replace
 */

export {findAndReplace} from './lib/index.js'
