{"name": "micromark-util-sanitize-uri", "version": "2.0.1", "description": "micromark utility to sanitize urls", "license": "MIT", "keywords": ["micromark", "util", "utility", "sanitize", "clear", "url"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-sanitize-uri", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["dev/", "index.d.ts.map", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-encode": "^2.0.0", "micromark-util-symbol": "^2.0.0"}, "scripts": {"build": "micromark-build"}, "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"unicorn/prefer-code-point": "off"}}}