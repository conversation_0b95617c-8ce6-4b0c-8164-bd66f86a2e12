/**
 * Parse JSX (flow).
 *
 * @param {Acorn | undefined} acorn
 *   Acorn parser to use (optional).
 * @param {Options} options
 *   Configuration.
 * @returns {Construct}
 *   Construct.
 */
export function jsxFlow(acorn: Acorn | undefined, options: Options): Construct;
import type { Acorn } from 'micromark-util-events-to-acorn';
import type { Options } from 'micromark-extension-mdx-jsx';
import type { Construct } from 'micromark-util-types';
//# sourceMappingURL=jsx-flow.d.ts.map