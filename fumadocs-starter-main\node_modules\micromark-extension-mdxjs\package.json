{"name": "micromark-extension-mdxjs", "version": "3.0.0", "description": "micromark extension to support MDX.js", "license": "MIT", "keywords": ["micromark", "micromark-extension", "mdx", "mdxjs", "esm", "jsx", "expression", "markdown", "unified"], "repository": "micromark/micromark-extension-mdxjs", "bugs": "https://github.com/micromark/micromark-extension-mdxjs/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js"], "dependencies": {"acorn": "^8.0.0", "acorn-jsx": "^5.0.0", "micromark-extension-mdx-expression": "^3.0.0", "micromark-extension-mdx-jsx": "^3.0.0", "micromark-extension-mdx-md": "^2.0.0", "micromark-extension-mdxjs-esm": "^3.0.0", "micromark-util-combine-extensions": "^2.0.0", "micromark-util-types": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^8.0.0", "micromark": "^4.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.56.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api-prod": "node --conditions production test.js", "test-api-dev": "node --conditions development test.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true}}