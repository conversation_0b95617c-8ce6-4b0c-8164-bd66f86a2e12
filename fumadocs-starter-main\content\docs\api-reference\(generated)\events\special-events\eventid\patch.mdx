---
title: Update special event
full: true
_openapi:
  method: PATCH
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Update the details of a special event.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

Update the details of a special event.

<APIPage document={"./content/docs/api-reference/openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"patch"}]} webhooks={[]} hasHead={false} />