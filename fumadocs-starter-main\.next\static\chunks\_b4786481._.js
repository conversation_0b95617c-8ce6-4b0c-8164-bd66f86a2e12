(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-ui_dist_components_dialog_search-default_6c1bef2c.js",
  "static/chunks/node_modules_fumadocs-ui_dist_components_dialog_search-default_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_bf125829._.js",
  "static/chunks/node_modules_fumadocs-core_dist_mixedbread-AG5AAOKO_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_7d9172cb._.js",
  "static/chunks/node_modules_fumadocs-core_dist_static-IWYDJ3C5_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_28413b14._.js",
  "static/chunks/node_modules_fumadocs-core_dist_orama-cloud-BYTAI6QU_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_3dd12844._.js",
  "static/chunks/node_modules_fumadocs-core_dist_algolia-KPRGMSJO_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_fetch-ITPHBPBE_7b02bcf9.js",
  "static/chunks/node_modules_fumadocs-core_dist_fetch-ITPHBPBE_553dd4f6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/fumadocs/ai/search.tsx [app-client] (ecmascript, next/dynamic entry, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_shiki_dist_1f17738a._.js",
  "static/chunks/node_modules_zod_v4_1cd37d28._.js",
  "static/chunks/94ec6_zod-to-json-schema_dist_esm_d746afaa._.js",
  "static/chunks/node_modules_ai_dist_index_mjs_a7281a1b._.js",
  "static/chunks/node_modules_micromark-core-commonmark_dev_lib_fc41dd51._.js",
  "static/chunks/node_modules_b570d2e7._.js",
  "static/chunks/src_components_fumadocs_ai_51e1519c._.js",
  "static/chunks/src_components_fumadocs_ai_search_tsx_0de7e379._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/fumadocs/ai/search.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
]);