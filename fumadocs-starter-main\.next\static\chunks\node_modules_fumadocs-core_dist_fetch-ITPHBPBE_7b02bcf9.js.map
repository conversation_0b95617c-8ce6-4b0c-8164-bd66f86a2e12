{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js"], "sourcesContent": ["import \"./chunk-JSBRDJBE.js\";\n\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, { api = \"/api/search\", locale, tag }) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", Array.isArray(tag) ? tag.join(\",\") : tag);\n  const key = `${api}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\nexport {\n  fetchDocs\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,6BAA6B;AAC7B,IAAI,QAAQ,aAAa,GAAG,IAAI;AAChC,eAAe,UAAU,KAAK,EAAE,KAAoC;QAApC,EAAE,MAAM,aAAa,EAAE,MAAM,EAAE,GAAG,EAAE,GAApC;IAC9B,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,CAAC,SAAS;IACpB,IAAI,QAAQ,OAAO,GAAG,CAAC,UAAU;IACjC,IAAI,KAAK,OAAO,GAAG,CAAC,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;IAChE,MAAM,MAAM,AAAC,GAAS,OAAP,KAAI,KAAU,OAAP;IACtB,MAAM,SAAS,MAAM,GAAG,CAAC;IACzB,IAAI,QAAQ,OAAO;IACnB,MAAM,MAAM,MAAM,MAAM;IACxB,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI;IAC3C,MAAM,SAAS,MAAM,IAAI,IAAI;IAC7B,MAAM,GAAG,CAAC,KAAK;IACf,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}