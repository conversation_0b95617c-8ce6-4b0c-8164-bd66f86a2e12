var R=require("../../chunks/[turbopack]_runtime.js")("server/app/icon.png/route.js")
R.c("server/chunks/node_modules_7c01d88c._.js")
R.c("server/chunks/[root-of-the-server]__d0d7104d._.js")
R.m("[project]/.next-internal/server/app/icon.png/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/icon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/icon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)").exports
